import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/customer_sales_report_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class CustomerSalesReportWidget extends StatefulWidget {
  final CustomerSalesReportDTO report;

  const CustomerSalesReportWidget({
    Key? key,
    required this.report,
  }) : super(key: key);

  @override
  State<CustomerSalesReportWidget> createState() =>
      _CustomerSalesReportWidgetState();
}

class _CustomerSalesReportWidgetState extends State<CustomerSalesReportWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab bar at the top (fixed)
        TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: T('Customers')),
            Tab(text: T('Invoices')),
          ],
          labelColor: context.newPrimaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: context.newPrimaryColor,
        ),

        // Content area (scrollable)
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Customers tab
              _buildScrollableTab(_buildCustomersContent()),

              // Invoices tab
              _buildScrollableTab(_buildInvoicesContent()),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to create a scrollable tab
  Widget _buildScrollableTab(Widget content) {
    return ListView(
      children: [
        // Report header at the top of each tab
        _buildReportHeader(),

        // Tab content
        content,
      ],
    );
  }

  // Build report header with key metrics
  Widget _buildReportHeader() {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final fromDate = dateFormat.format(widget.report.fromDate);
    final toDate = dateFormat.format(widget.report.toDate);
    final dateRange =
        fromDate == toDate ? fromDate : '$fromDate ${T('to')} $toDate';

    return Container(
      padding: const EdgeInsets.all(16),
      color: context.newPrimaryColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            T('Customer Sales Report'),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: context.newPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            dateRange,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),

          // Key metrics in a responsive layout
          LayoutBuilder(
            builder: (context, constraints) {
              // Check if we're on a small screen (mobile)
              final isSmallScreen = constraints.maxWidth < 600;

              return isSmallScreen
                  ? Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Total Sales'),
                                value:
                                    widget.report.totalSales.toStringAsFixed(2),
                                icon: Icons.attach_money,
                                color: Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Invoices'),
                                value: widget.report.invoiceCount.toString(),
                                icon: Icons.receipt_long,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Customers'),
                                value: widget.report.customerSummaries.length
                                    .toString(),
                                icon: Icons.people,
                                color: Colors.purple,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Avg. Sale'),
                                value: widget.report.invoiceCount > 0
                                    ? (widget.report.totalSales /
                                            widget.report.invoiceCount)
                                        .toStringAsFixed(2)
                                    : '0.00',
                                icon: Icons.trending_up,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Total Sales'),
                            value: widget.report.totalSales.toStringAsFixed(2),
                            icon: Icons.attach_money,
                            color: Colors.green,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Invoices'),
                            value: widget.report.invoiceCount.toString(),
                            icon: Icons.receipt_long,
                            color: Colors.blue,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Customers'),
                            value: widget.report.customerSummaries.length
                                .toString(),
                            icon: Icons.people,
                            color: Colors.purple,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Avg. Sale'),
                            value: widget.report.invoiceCount > 0
                                ? (widget.report.totalSales /
                                        widget.report.invoiceCount)
                                    .toStringAsFixed(2)
                                : '0.00',
                            icon: Icons.trending_up,
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    );
            },
          ),
        ],
      ),
    );
  }

  // Build a metric card for the header
  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build the customers tab content
  Widget _buildCustomersContent() {
    final topCustomers = widget.report.getTopCustomers();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top customers chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildTopCustomersChart(topCustomers),
            ),
          ),

          // Customers table
          _buildCustomersTable(widget.report.customerSummaries),

          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build the invoices tab content
  Widget _buildInvoicesContent() {
    final salesByDate = widget.report.calculateSalesByDate();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sales by date chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildSalesByDateChart(salesByDate),
            ),
          ),

          // Invoices list
          Text(
            T('Invoices'),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          if (widget.report.invoices.isEmpty)
            Center(
              child: Text(
                T('No invoices found for the selected period'),
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.report.invoices.length,
              itemBuilder: (context, index) {
                final invoice = widget.report.invoices[index];
                return _buildInvoiceCard(invoice);
              },
            ),

          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build a table of customers
  Widget _buildCustomersTable(List<CustomerSalesSummary> customers) {
    if (customers.isEmpty) {
      return Center(
        child: Text(
          T('No customer data available'),
          style: TextStyle(
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    // Sort customers by sales (highest first)
    final sortedCustomers = List<CustomerSalesSummary>.from(customers);
    sortedCustomers.sort((a, b) => b.totalSales.compareTo(a.totalSales));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Customers by Sales'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Table header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: context.newPrimaryColor.withOpacity(0.1),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  T('Customer'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  T('Invoices'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  T('Avg. Sale'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  T('Total'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),

        // Table rows
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sortedCustomers.length,
          itemBuilder: (context, index) {
            final customer = sortedCustomers[index];
            final isEven = index % 2 == 0;

            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: isEven ? Colors.grey.shade100 : Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Text(
                      customer.customerName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      customer.invoiceCount.toString(),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      customer.averageSale.toStringAsFixed(2),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      customer.totalSales.toStringAsFixed(2),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  // Build a card for a single invoice
  Widget _buildInvoiceCard(InvoiceModel invoice) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final formattedDate = invoice.entryDate != null
        ? dateFormat.format(invoice.entryDate!)
        : T('Unknown date');

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? T('Unknown')}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  formattedDate,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            const Divider(),

            // Invoice details
            _buildInvoiceDetailRow(
              label: T('Customer'),
              value: invoice.customerName ?? T('Unknown'),
              icon: Icons.person,
            ),
            _buildInvoiceDetailRow(
              label: T('Warehouse'),
              value: invoice.storeName ?? T('Unknown'),
              icon: Icons.warehouse,
            ),
            _buildInvoiceDetailRow(
              label: T('Total'),
              value: (invoice.total ?? 0.0).toStringAsFixed(2),
              icon: Icons.attach_money,
              valueColor: Colors.green.shade700,
              valueBold: true,
            ),
          ],
        ),
      ),
    );
  }

  // Build a detail row for the invoice card
  Widget _buildInvoiceDetailRow({
    required String label,
    required String value,
    required IconData icon,
    Color? valueColor,
    bool valueBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: TextStyle(
              fontWeight: valueBold ? FontWeight.bold : FontWeight.normal,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  // Build top customers chart
  Widget _buildTopCustomersChart(List<CustomerSalesSummary> customers) {
    if (customers.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No customer data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Limit to top 10 customers for the chart
    final topCustomers =
        customers.length > 10 ? customers.sublist(0, 10) : customers;

    // Sort customers by sales (highest first)
    topCustomers.sort((a, b) => b.totalSales.compareTo(a.totalSales));

    // Calculate the maximum sales value for scaling
    final maxSales =
        topCustomers.map((p) => p.totalSales).reduce((a, b) => a > b ? a : b);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Top Customers'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: topCustomers.length,
          itemBuilder: (context, index) {
            final customer = topCustomers[index];
            // Calculate percentage of max value for bar width
            final percentage = customer.totalSales / maxSales;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  // Customer name
                  SizedBox(
                    width: 150,
                    child: Text(
                      customer.customerName,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Bar
                  Expanded(
                    child: Stack(
                      children: [
                        // Background
                        Container(
                          height: 25,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),

                        // Actual bar
                        FractionallySizedBox(
                          widthFactor: percentage,
                          child: Container(
                            height: 25,
                            decoration: BoxDecoration(
                              color: context.newPrimaryColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),

                        // Value label
                        Positioned.fill(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Text(
                                NumberFormat('#,##0.00')
                                    .format(customer.totalSales),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: percentage > 0.75
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  // Build sales by date chart
  Widget _buildSalesByDateChart(Map<String, double> salesByDate) {
    if (salesByDate.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No sales data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Sort dates
    final sortedDates = salesByDate.keys.toList()..sort();

    // Calculate the maximum sales value for scaling
    final maxSales = salesByDate.values.reduce((a, b) => a > b ? a : b);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Sales by Date'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          itemCount: sortedDates.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (conext, index) {
            final date = sortedDates[index];
            final value = salesByDate[date]!;
            // Calculate percentage of max value for bar width
            final percentage = value / maxSales;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 6.0),
              child: Row(
                children: [
                  // Date
                  SizedBox(
                    width: 100,
                    child: Text(
                      date,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Bar
                  Expanded(
                    child: Stack(
                      children: [
                        // Background
                        Container(
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),

                        // Actual bar
                        FractionallySizedBox(
                          widthFactor: percentage,
                          child: Container(
                            height: 20,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),

                        // Value label
                        Positioned.fill(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Text(
                                NumberFormat('#,##0.00').format(value),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: percentage > 0.75
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}
