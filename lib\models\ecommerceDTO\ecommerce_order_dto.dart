class EcommerceOrderDto {
  TransactionTypes? transactionType;
  String? erpReferenceId;
  int? customerId;
  double? total;
  double? totalDiscount;
  String? erpReferenceCode;
  String? salesReferenceCode;
  int? eRPStoreId;
  DateTime? date;
  String? note;
  List<EcommerceItemTransactionDto>? items;

  EcommerceOrderDto(
      {this.transactionType,
      this.erpReferenceId,
      this.erpReferenceCode,
      this.salesReferenceCode,
      this.date,
      this.note,
      this.items,
      this.total,
      this.customerId,
      this.eRPStoreId,
      this.totalDiscount});

  factory EcommerceOrderDto.fromJson(Map<String, dynamic> json) {
    return EcommerceOrderDto(
      transactionType: TransactionTypes.values.firstWhere(
          (e) => e.toString() == 'TransactionTypes.${json['transactionType']}',
          orElse: () => TransactionTypes.unknown), // Handle unknown types
      erpReferenceId: json['ERPReferenceId'],
      erpReferenceCode: json['ERPReferenceCode'],
      salesReferenceCode: json['salesReferenceCode'],
      eRPStoreId: json['eRPStoreId'],
      note: json['Note'],
      date: json['Date'] != null ? DateTime.parse(json['Date']) : null,
      items: json['items'] != null
          ? (json['items'] as List)
              .map((i) => EcommerceItemTransactionDto.fromJson(i))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'TransactionType': transactionType?.index,
      'ERPReferenceId': erpReferenceId,
      'ERPReferenceCode': erpReferenceCode,
      'salesReferenceCode': salesReferenceCode,
      'eRPStoreId': eRPStoreId,
      'customerId': customerId,
      'total': total,
      'totalDiscount': totalDiscount,
      'Date': date?.toIso8601String(),
      'Note': note,
      'items': items?.map((i) => i.toJson()).toList(),
    };
  }
}

class EcommerceItemTransactionDto {
  int? erpProductId;
  double? price;
  int? quantity;
  bool? isHaveCombination;
  List<int>? selectedOptionIds;

  EcommerceItemTransactionDto({
    this.erpProductId,
    this.price,
    this.quantity,
    this.isHaveCombination,
    this.selectedOptionIds,
  });

  factory EcommerceItemTransactionDto.fromJson(Map<String, dynamic> json) {
    return EcommerceItemTransactionDto(
      erpProductId: json['ErpProductId'],
      price: (json['Price'] as num?)?.toDouble(),
      quantity: json['Quantity'],
      isHaveCombination: json['IsHaveCombination'],
      selectedOptionIds: (json['selectedOptionIds'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ErpProductId': erpProductId,
      'Price': price,
      'Quantity': quantity,
      'IsHaveCombination': isHaveCombination,
      'selectedOptionIds': selectedOptionIds,
    };
  }
}

enum TransactionTypes { purchase, sale, refund, unknown }
