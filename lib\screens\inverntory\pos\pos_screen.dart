import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/category_model.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/components/continuous_barcode_scanner_dialog.dart';
import 'package:inventory_application/screens/inverntory/pos/widgets/pos_always_on_scanner.dart';
import 'package:inventory_application/screens/inverntory/pos/widgets/pos_cart_widget.dart';
import 'package:inventory_application/screens/inverntory/pos/widgets/pos_product_grid.dart';
import 'package:inventory_application/screens/inverntory/pos/widgets/pos_top_action_bar.dart';
import 'package:inventory_application/screens/inverntory/pos/widgets/pos_bottom_navbar.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/models/model/product_model.dart';

class POSScreen extends StatefulWidget {
  final bool isEditing;

  const POSScreen({super.key, this.isEditing = false});

  @override
  State<POSScreen> createState() => _POSScreenState();
}

class _POSScreenState extends State<POSScreen> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _barcodeController = TextEditingController();
  final FocusNode _barcodeFocusNode = FocusNode();
  final FocusNode _searchFocusNode = FocusNode();
  final FocusNode _rootFocusNode = FocusNode();
  int? _selectedCategory;
  bool _isLoading = true;
  bool _isSearching = false;
  List<ProductDTO> _products = [];
  List<CateogryModel> _categories = [];
  late TabController _tabController;

  // Debounce timer for search
  Timer? _searchDebounce;

  // Create keys for the widgets
  final GlobalKey<POSBottomNavbarState> _bottomNavbarKey =
      GlobalKey<POSBottomNavbarState>();
  final GlobalKey<POSCartWidgetState> _cartWidgetKey =
      GlobalKey<POSCartWidgetState>();

  // Handle key events globally
  bool _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.f3) {
        _focusOnSearch();
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f4) {
        _focusOnBarcode();
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f10) {
        // Call the saveOrder method on the POSBottomNavbar
        if (_bottomNavbarKey.currentState != null) {
          _bottomNavbarKey.currentState!.saveOrder();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.keyR &&
          (HardwareKeyboard.instance.isControlPressed ||
              HardwareKeyboard.instance.isMetaPressed)) {
        // Call the returnLastOrder method on the POSBottomNavbar
        if (_bottomNavbarKey.currentState != null) {
          _bottomNavbarKey.currentState!.returnLastOrder();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f1) {
        // Call the viewLastOrder method on the POSBottomNavbar
        if (_bottomNavbarKey.currentState != null) {
          _bottomNavbarKey.currentState!.viewLastOrder();
        }
      } else if (event.logicalKey == LogicalKeyboardKey.f2) {
        // Call the returnOrderByCode method on the POSBottomNavbar
        if (_bottomNavbarKey.currentState != null) {
          _bottomNavbarKey.currentState!.returnOrderByCode();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.add ||
          event.logicalKey == LogicalKeyboardKey.numpadAdd) {
        // Increase quantity of last added product
        if (_cartWidgetKey.currentState != null) {
          _cartWidgetKey.currentState!.increaseLastProductQuantity();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.minus ||
          event.logicalKey == LogicalKeyboardKey.numpadSubtract) {
        // Decrease quantity of last added product
        if (_cartWidgetKey.currentState != null) {
          _cartWidgetKey.currentState!.decreaseLastProductQuantity();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.asterisk ||
          event.logicalKey == LogicalKeyboardKey.numpadMultiply) {
        // Open price edit dialog for the last added product
        if (_cartWidgetKey.currentState != null) {
          _cartWidgetKey.currentState!.editLastProductPrice();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.keyD &&
          (HardwareKeyboard.instance.isControlPressed ||
              HardwareKeyboard.instance.isMetaPressed)) {
        // Open discount dialog
        if (_cartWidgetKey.currentState != null) {
          _cartWidgetKey.currentState!.openDiscountDialog();
        }
        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f9) {
        // Create a new order
        final saleInvoiceController =
            Provider.of<SaleInvoiceController>(context, listen: false);
        saleInvoiceController.createNewOrder();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(T('New order created')),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green.shade600,
            duration: const Duration(seconds: 1),
          ),
        );
        return true; // Event handled
      } else if ((HardwareKeyboard.instance.isControlPressed ||
              HardwareKeyboard.instance.isMetaPressed) &&
          _isNumericKey(event.logicalKey)) {
        // Switch to order by index (Ctrl+1, Ctrl+2, etc.)
        final saleInvoiceController =
            Provider.of<SaleInvoiceController>(context, listen: false);

        // Get the order index from the key (1-based to 0-based)
        final orderIndex = _getNumericKeyValue(event.logicalKey) - 1;

        // Check if the order index is valid
        if (orderIndex >= 0 && orderIndex < saleInvoiceController.orderCount) {
          saleInvoiceController.switchToOrder(orderIndex);

          // Show success message

          return true; // Event handled
        }
        return false; // Event not handled (invalid order index)
      } else if (event.logicalKey == LogicalKeyboardKey.delete) {
        // Close the current order
        final saleInvoiceController =
            Provider.of<SaleInvoiceController>(context, listen: false);

        // Show confirmation dialog
        _showCloseOrderConfirmation(saleInvoiceController);

        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f5) {
        // Open calculator
        _openCalculator();

        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f7) {
        // Print last invoice
        if (_cartWidgetKey.currentState != null) {
          _cartWidgetKey.currentState!.printLastInvoice();
        }

        return true; // Event handled
      } else if (event.logicalKey == LogicalKeyboardKey.f6) {
        // Print last invoice
        final saleInvoiceController =
            Provider.of<SaleInvoiceController>(context, listen: false);
        if (_cartWidgetKey.currentState != null) {
          _cartWidgetKey.currentState!.showNotesDialog(
              context, saleInvoiceController.invoice.note, (note) {
            saleInvoiceController.invoice.note = note;
          });
        }

        return true; // Event handled
      }
    }
    return false; // Event not handled
  }

  // Method to focus on barcode field
  void _focusOnBarcode() {
    // Focus on barcode field
    FocusScope.of(context).unfocus(); // Clear any existing focus

    // Request focus on the barcode field
    Future.delayed(Duration.zero, () {
      FocusScope.of(context).requestFocus(_barcodeFocusNode);
    });
  }

  @override
  void initState() {
    super.initState();
    _initPOS();

    // Set up keyboard focus handling
    _barcodeFocusNode.addListener(_onFocusChange);

    // Register for keyboard events
    HardwareKeyboard.instance.addHandler(_handleKeyEvent);

    // Show price update tip after a short delay
    Future.delayed(const Duration(seconds: 1), () {
      _showPriceUpdateTip();
    });
  }

  void _onFocusChange() {
    // If focus is lost and there's text, process it
    if (!_barcodeFocusNode.hasFocus && _barcodeController.text.isNotEmpty) {
      _onBarcodeScanned(_barcodeController.text);
      _barcodeController.clear();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _barcodeController.dispose();
    _barcodeFocusNode.removeListener(_onFocusChange);
    _barcodeFocusNode.dispose();
    _searchFocusNode.dispose();
    _rootFocusNode.dispose();
    _tabController.dispose();

    // Cancel any active search debounce timer
    if (_searchDebounce?.isActive ?? false) {
      _searchDebounce!.cancel();
    }

    // Unregister keyboard listener
    HardwareKeyboard.instance.removeHandler(_handleKeyEvent);

    super.dispose();
  }

  Future<void> _initPOS() async {
    setState(() {
      _isLoading = true;
    });

    // Initialize the sale invoice
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);
    final customerController =
        Provider.of<CustomerController>(context, listen: false);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    // Only reset the invoice if we're not editing an existing one
    if (!widget.isEditing) {
      // Reset the current invoice
      saleInvoiceController.invoice = InvoiceDto();
      saleInvoiceController.selectedSaleInvoiceProduct = [];

      // Set invoice type
      saleInvoiceController.invoice.invoiceType = SalesType.Invoice;
      saleInvoiceController.invoice.invoiceTypeName = "Invoice";

      // Get invoice number
      await saleInvoiceController.getSaleInvoiceNumber();

      // The getSaleInvoiceNumber method sets the appReferanceCode, but let's make sure
      // it's properly displayed if it's available
      final appRefCode = saleInvoiceController.invoice.appReferanceCode;
      if (appRefCode == null || appRefCode.isEmpty) {
        saleInvoiceController.invoice.appReferanceCode = "Pending";
      }

      // Display placeholder for server invoice code until saved
      if (saleInvoiceController.invoice.invoiceCode == null ||
          saleInvoiceController.invoice.invoiceCode!.isEmpty) {
        saleInvoiceController.invoice.invoiceCode = "Will be generated";
      }

      // First check if we have a default warehouse in settings
      if (invoiceSettingsController.defaultWarehouseId != null) {
        saleInvoiceController.invoice.warehouseId =
            invoiceSettingsController.defaultWarehouseId;
        saleInvoiceController.invoice.warehouseName =
            invoiceSettingsController.defaultWarehouseName;
      }
      // If not, set default warehouse from the first available warehouse
      else if (warehouseController.warehouses.isNotEmpty) {
        saleInvoiceController.invoice.warehouseId =
            warehouseController.warehouses.first.id;
        saleInvoiceController.invoice.warehouseName =
            warehouseController.warehouses.first.name;
      }

      // First check if we have a default customer in settings
      if (invoiceSettingsController.defaultCustomerId != null) {
        saleInvoiceController.invoice.customerId =
            invoiceSettingsController.defaultCustomerId;
        saleInvoiceController.invoice.custoemrName =
            invoiceSettingsController.defaultCustomerName;
      }
      // If not, set default customer from the first available customer
      else if (customerController.customers.isNotEmpty) {
        try {
          final firstCustomer = customerController.customers.first;
          if (firstCustomer != null && firstCustomer.iD != null) {
            saleInvoiceController.invoice.customerId = firstCustomer.iD;
            saleInvoiceController.invoice.custoemrName = firstCustomer.name;
          } else {
            errorSnackBar(
              message:
                  T("Could not set default customer. Please select manually."),
            );
          }
        } catch (e) {
          print("Error setting default customer: $e");
        }
      }

      // Set today's date
      saleInvoiceController.invoice.invoiceDate = DateTime.now();

      // Set payment type to cash
      saleInvoiceController.invoice.invoicePaymentType =
          InvoicePaymentType.chash;
    }

    // Load products
    await _loadProducts();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadProducts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Create a ProductController instance
      final productController =
          Provider.of<ProductController>(context, listen: false);

      // Fetch products using the getItems method from the instance
      await productController.getItems(resetAndRefresh: true);

      // Get products from the controller
      final products = productController.realProductList;

      final categoryController =
          Provider.of<CategoryController>(context, listen: false).categories;
      // Extract categories
      final categories = categoryController;

      setState(() {
        _products = products;
        _categories = categories.toList();
        _isLoading = false;
      });
    } catch (e) {
      print("Error loading products: $e");
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(message: T("Failed to load products. Please try again."));
    }
  }

  void _onSearchTextChanged(String text) {
    // Cancel previous timer if it exists
    if (_searchDebounce?.isActive ?? false) {
      _searchDebounce!.cancel();
    }

    // Set a new timer to delay the search
    _searchDebounce = Timer(const Duration(milliseconds: 500), () {
      if (text.isEmpty) {
        setState(() {
          _isSearching = false;
        });
        _loadProducts();
      } else {
        setState(() {
          _isSearching = true;
        });
        _searchProducts(text);
      }
    });
  }

  Future<void> _searchProducts(String query) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Create a ProductController instance
      final productController =
          Provider.of<ProductController>(context, listen: false);

      // Use the getItems method with search parameter
      await productController.getItems(resetAndRefresh: true, search: query);

      // Get the filtered products
      final products = productController.realProductList;

      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(message: T("Error searching products"));
    }
  }

  void _onCategorySelected(CateogryModel? category) {
    setState(() {
      _selectedCategory = category?.iD;
    });
    _filterProductsByCategory(category);
  }

  Future<void> _filterProductsByCategory(CateogryModel? category) async {
    if (category == null) {
      _loadProducts();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final productController =
          Provider.of<ProductController>(context, listen: false);
      await productController.getItems(
          resetAndRefresh: true, categoryId: category.iD, search: null);

      final allProducts = productController.realProductList;

      // Filter products by category
      final filteredProducts = allProducts
          .where((product) => product.categoryId == category.iD)
          .toList();

      setState(() {
        _products = filteredProducts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(message: T("Error filtering products"));
    }
  }

  Future<bool> _onBarcodeScanned(String barcode) async {
    try {
      final product =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (product != null) {
        // First check if this is a final barcode with pre-selected attributes
        if (product.hasSelectedAttributes) {
          // If it has pre-selected attributes, add directly to cart
          final saleInvoiceController =
              Provider.of<SaleInvoiceController>(context, listen: false);
          saleInvoiceController.addProductToSelectedList(product);
          return true;
        }

        // Check if product has attributes that need to be selected
        if (product.itemAttributes != null &&
            product.itemAttributes!.isNotEmpty) {
          // For barcode scanning, we need to handle the attribute dialog differently
          // because we're potentially inside another dialog already
          bool? result;
          await Future.microtask(() async {
            result = await _showAttributeSelectionDialog(product);
          });
          return result ?? false;
        }

        // No attributes, add product to cart
        final saleInvoiceController =
            Provider.of<SaleInvoiceController>(context, listen: false);
        saleInvoiceController.addProductToSelectedList(product);
        return true;
      }
      return false;
    } catch (e) {
      print("Error scanning barcode: $e");
      return false;
    }
  }

  void _onProductTap(ProductDTO product) {
    // First check if this is a product with pre-selected attributes
    if (product.hasSelectedAttributes) {
      // If it has pre-selected attributes, add directly to cart
      final saleInvoiceController =
          Provider.of<SaleInvoiceController>(context, listen: false);
      saleInvoiceController.addProductToSelectedList(product);
      return;
    }

    // Check if product has attributes that need to be selected
    if (product.itemAttributes != null && product.itemAttributes!.isNotEmpty) {
      _showAttributeSelectionDialog(product);
    } else {
      // No attributes, add directly to cart
      final saleInvoiceController =
          Provider.of<SaleInvoiceController>(context, listen: false);
      saleInvoiceController.addProductToSelectedList(product);
    }
  }

  // Show attribute selection dialog and return true if product was added to cart
  Future<bool> _showAttributeSelectionDialog(ProductDTO product) async {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};
    bool productAdded = false;

    await showDialog(
      context: context,
      barrierDismissible: false, // User must select attributes
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                T('Select Product Options'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: context.newPrimaryColor,
                ),
              ),
              content: Container(
                width: MediaQuery.of(context).size.width * 0.5,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.title ?? '',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Sort attributes by their order property
                      ...(() {
                        final sortedAttributes =
                            List<ItemAttribute>.from(product.itemAttributes!);
                        sortedAttributes.sort(
                            (a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
                        return sortedAttributes;
                      })()
                          .map((attribute) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              attribute.attributeName ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                                color: context.newTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Wrap options in a grid
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: attribute.itemsAttributeOptions!
                                  .map((option) {
                                final isSelected =
                                    selectedOptions[attribute.id] == option;

                                return InkWell(
                                  onTap: () {
                                    setState(() {
                                      selectedOptions[attribute.id!] = option;
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? context.newPrimaryColor
                                          : Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: isSelected
                                            ? context.newPrimaryColor
                                            : Colors.grey.shade300,
                                      ),
                                    ),
                                    child: Text(
                                      option.optionName ?? '',
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : context.newTextColor,
                                        fontWeight: isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                            const SizedBox(height: 16),
                          ],
                        );
                      }).toList(),

                      // Preview of the selected options
                      if (selectedOptions.isNotEmpty) ...[
                        const Divider(),
                        const SizedBox(height: 8),
                        Text(
                          T('Selected Options:'),
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getFormattedSelectedOptions(selectedOptions),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.newPrimaryColor,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    productAdded = false;
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    T('Cancel'),
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: selectedOptions.length ==
                          product.itemAttributes!.length
                      ? () {
                          // All attributes have been selected
                          productAdded = true;
                          Navigator.of(context).pop();
                          _addProductWithSelectedAttributes(
                              product, selectedOptions);
                        }
                      : null, // Disable button if not all attributes are selected
                  child: Text(T('Add to Cart')),
                ),
              ],
            );
          },
        );
      },
    );

    return productAdded;
  }

  // Format the selected options as a string (e.g., "S/Red")
  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    if (selectedOptions.isEmpty) return '';

    // Sort by attribute ID (which should correspond to the attribute order)
    final sortedKeys = selectedOptions.keys.toList()..sort();

    // Join option names with '/'
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }

  // Add product to cart with selected attributes
  void _addProductWithSelectedAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Create a unique key for this product + attributes combination
    // We'll use the selected option IDs to create a "virtual" product ID
    String optionsHash = selectedOptions.entries
        .map((e) => '${e.value.optionId}-${e.key}')
        .join('_');

    String virtualProductId = '${product.id}_$optionsHash';

    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: product.uniteId,
      uniteName: product.uniteName,
      category: product.category,
      quantity: product.quantity,
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      itemAttributes: product.itemAttributes,
      virtualProductId: virtualProductId,
      hasSelectedAttributes: true,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - ${optionsString}';

      // Store selected options in the description field for reference
      // This way we can retrieve the information later if needed
      String attributeDetails = selectedOptions.entries
          .map((entry) =>
              '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
          .join(', ');

      if (productCopy.description == null || productCopy.description!.isEmpty) {
        productCopy.description = attributeDetails;
      } else {
        productCopy.description =
            '${productCopy.description}\n[${attributeDetails}]';
      }
    }

    // Add to cart
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    saleInvoiceController.addProductToSelectedList(productCopy);
  }

  void _showPriceUpdateTip() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.white),
              const SizedBox(width: 10),
              Expanded(
                child: Text(T(
                    'Tip: You can edit the price of items in your cart by clicking "Edit Price"')),
              ),
            ],
          ),
          duration: const Duration(seconds: 5),
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: T('Got it'),
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  void _showCameraScanner() {
    showDialog(
      context: context,
      builder: (context) => ContinuousBarcodeScannerDialog(
        onBarcodeDetected: (barcode) async {
          return await _onBarcodeScanned(barcode);
        },
      ),
    );
  }

  // Method to focus on search field
  void _focusOnSearch() {
    // Focus on search field
    FocusScope.of(context).unfocus(); // Clear any existing focus

    // Request focus on the search field
    Future.delayed(Duration.zero, () {
      FocusScope.of(context).requestFocus(_searchFocusNode);
      _searchController.selection = TextSelection.fromPosition(
        TextPosition(offset: _searchController.text.length),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final saleInvoiceController = Provider.of<SaleInvoiceController>(context);
    final cartProducts = saleInvoiceController.selectedSaleInvoiceProduct;

    // Check if keyboard is visible to adjust UI
    final viewInsets = MediaQuery.of(context).viewInsets;
    final isKeyboardVisible = viewInsets.bottom > 0;

    return Scaffold(
      // Prevent screen resizing when keyboard appears on mobile
      resizeToAvoidBottomInset: Platform.isWindows ? true : false,
      body: GestureDetector(
        // Add tap detection to dismiss keyboard
        onTap: () {
          // Unfocus any active text input to dismiss the keyboard
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Header
            CommonHeader(
              icon: Icons.point_of_sale,
              title: T("Point of Sale"),
            ),

            // Orders tab bar

            // Main content
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Products section with scanner and products (70% of width)
                  Expanded(
                    flex: 7,
                    child: Column(
                      children: [
                        const SizedBox(height: 5),
                        SizedBox(
                          height: 50,
                          child: Row(
                            children: [
                              // Tab bar
                              Expanded(
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: saleInvoiceController.orderCount,
                                  itemBuilder: (context, index) {
                                    final order =
                                        saleInvoiceController.orders[index];
                                    final isActive = index ==
                                        saleInvoiceController.activeOrderIndex;
                                    return GestureDetector(
                                      onTap: () => saleInvoiceController
                                          .switchToOrder(index),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 8),
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 4, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: isActive
                                              ? context.newPrimaryColor
                                              : Colors.grey.shade100,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          children: [
                                            Text(
                                              'الطلب ${index + 1}',
                                              style: TextStyle(
                                                color: isActive
                                                    ? Colors.white
                                                    : Colors.black,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            if (order.products.isNotEmpty)
                                              Container(
                                                margin: const EdgeInsets.only(
                                                    left: 8),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 2),
                                                decoration: BoxDecoration(
                                                  color: isActive
                                                      ? Colors.white
                                                      : context.newPrimaryColor,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  '${order.products.length}',
                                                  style: TextStyle(
                                                    color: isActive
                                                        ? context
                                                            .newPrimaryColor
                                                        : Colors.white,
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              // Add new order button
                              Row(
                                children: [
                                  if (Platform.isWindows)
                                    Text(
                                      "F9",
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700),
                                    ),
                                  // const SizedBox(width: 5),
                                  IconButton(
                                    icon: const Icon(Icons.add),
                                    onPressed: () {
                                      saleInvoiceController.createNewOrder();
                                    },
                                  ),
                                ],
                              ),
                              // Close order button (only show if there are multiple orders)
                              if (saleInvoiceController.orderCount > 1)
                                Row(
                                  children: [
                                    if (Platform.isWindows)
                                      Text(
                                        "Delete",
                                        style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade700),
                                      ),
                                    IconButton(
                                      icon: const Icon(Icons.close),
                                      onPressed: () {
                                        _showCloseOrderDialog(
                                            context, saleInvoiceController);
                                      },
                                      color: Colors.red,
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),

                        // Always-on barcode scanner (compact)
                        POSAlwaysOnScanner(
                          onBarcodeDetected: _onBarcodeScanned,
                          barcodeFocusNode: _barcodeFocusNode,
                          barcodeController: _barcodeController,
                        ),

                        // Products container
                        Expanded(
                          child: Card(
                            elevation: 2,
                            margin: const EdgeInsets.all(8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Top action bar with search and category selection
                                POSTopActionBar(
                                  searchController: _searchController,
                                  searchFocusNode: _searchFocusNode,
                                  onSearchChanged: _onSearchTextChanged,
                                  categories: _categories,
                                  selectedCategory: _selectedCategory,
                                  onCategorySelected: _onCategorySelected,
                                ),

                                // Product grid - always visible with consistent height
                                Expanded(
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    // Don't adjust height on mobile when keyboard is visible
                                    height: (isKeyboardVisible &&
                                            Platform.isWindows)
                                        ? MediaQuery.of(context).size.height *
                                            0.3
                                        : null,
                                    child: _isLoading
                                        ? const Center(
                                            child: CircularProgressIndicator())
                                        : POSProductGrid(
                                            products: _products,
                                            onProductTap: _onProductTap,
                                          ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Cart section (30% of width)
                  Expanded(
                    flex: 3,
                    child: Card(
                      elevation: 2,
                      margin: const EdgeInsets.all(8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: POSCartWidget(
                        key: _cartWidgetKey,
                        products: cartProducts,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom navbar with total and checkout button
            POSBottomNavbar(key: _bottomNavbarKey),
          ],
        ),
      ),
    );
  }

  Widget _buildCodeBox({
    required String label,
    required String code,
    required BuildContext context,
    Color? backgroundColor,
    Color? labelColor,
    Color? borderColor,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: borderColor ?? Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () => _copyToClipboard(context, code, label),
          borderRadius: BorderRadius.circular(8),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: labelColor ?? Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      code,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.copy,
                size: 16,
                color: labelColor ?? Colors.grey.shade600,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text, String label) {
    Clipboard.setData(ClipboardData(text: text)).then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${label} copied to clipboard'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    });
  }

  // Helper method to check if a key is a numeric key (1-9)
  bool _isNumericKey(LogicalKeyboardKey key) {
    return key == LogicalKeyboardKey.digit1 ||
        key == LogicalKeyboardKey.digit2 ||
        key == LogicalKeyboardKey.digit3 ||
        key == LogicalKeyboardKey.digit4 ||
        key == LogicalKeyboardKey.digit5 ||
        key == LogicalKeyboardKey.digit6 ||
        key == LogicalKeyboardKey.digit7 ||
        key == LogicalKeyboardKey.digit8 ||
        key == LogicalKeyboardKey.digit9 ||
        key == LogicalKeyboardKey.numpad1 ||
        key == LogicalKeyboardKey.numpad2 ||
        key == LogicalKeyboardKey.numpad3 ||
        key == LogicalKeyboardKey.numpad4 ||
        key == LogicalKeyboardKey.numpad5 ||
        key == LogicalKeyboardKey.numpad6 ||
        key == LogicalKeyboardKey.numpad7 ||
        key == LogicalKeyboardKey.numpad8 ||
        key == LogicalKeyboardKey.numpad9;
  }

  // Helper method to get the numeric value of a key (1-9)
  int _getNumericKeyValue(LogicalKeyboardKey key) {
    if (key == LogicalKeyboardKey.digit1 || key == LogicalKeyboardKey.numpad1) {
      return 1;
    }
    if (key == LogicalKeyboardKey.digit2 || key == LogicalKeyboardKey.numpad2) {
      return 2;
    }
    if (key == LogicalKeyboardKey.digit3 || key == LogicalKeyboardKey.numpad3) {
      return 3;
    }
    if (key == LogicalKeyboardKey.digit4 || key == LogicalKeyboardKey.numpad4) {
      return 4;
    }
    if (key == LogicalKeyboardKey.digit5 || key == LogicalKeyboardKey.numpad5) {
      return 5;
    }
    if (key == LogicalKeyboardKey.digit6 || key == LogicalKeyboardKey.numpad6) {
      return 6;
    }
    if (key == LogicalKeyboardKey.digit7 || key == LogicalKeyboardKey.numpad7) {
      return 7;
    }
    if (key == LogicalKeyboardKey.digit8 || key == LogicalKeyboardKey.numpad8) {
      return 8;
    }
    if (key == LogicalKeyboardKey.digit9 || key == LogicalKeyboardKey.numpad9) {
      return 9;
    }
    return 0; // Default value (should not happen)
  }

  // Show confirmation dialog for closing the current order
  Future<void> _showCloseOrderConfirmation(
      SaleInvoiceController controller) async {
    // Don't allow closing the last order
    if (controller.orderCount <= 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(T('Cannot close the last order')),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red.shade600,
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('Close Order')),
        content: Text(T('Are you sure you want to close this order?')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(T('Cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(T('Close Order')),
          ),
        ],
      ),
    );

    if (result == true) {
      final currentIndex = controller.activeOrderIndex;
      controller.removeOrder(currentIndex);

      // Show success message if the context is still mounted
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(T('Order closed')),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.orange.shade600,
            duration: const Duration(seconds: 1),
          ),
        );
      }
    }
  }

  // Open calculator with focus
  void _openCalculator() {
    if (_bottomNavbarKey.currentState != null) {
      _bottomNavbarKey.currentState!.showCalculator();
    }
  }

  // Show close order dialog
  Future<void> _showCloseOrderDialog(
      BuildContext context, SaleInvoiceController controller) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('Close Order')),
        content: Text(T('Are you sure you want to close this order?')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(T('Cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(T('Close Order')),
          ),
        ],
      ),
    );

    if (result == true) {
      controller.removeOrder(controller.activeOrderIndex);
    }
  }
}
