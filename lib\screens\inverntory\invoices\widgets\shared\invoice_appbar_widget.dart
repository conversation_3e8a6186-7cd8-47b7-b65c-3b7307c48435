import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/pdf_helper.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_button.dart';

import 'package:flutter_pdfview/flutter_pdfview.dart';

class InvoiceAppbarWidget extends StatefulWidget {
  const InvoiceAppbarWidget(
      {super.key, required this.onSave, required this.onSaveAndNavigate});
  final Function onSave;
  final Function onSaveAndNavigate;

  @override
  State<InvoiceAppbarWidget> createState() => _InvoiceAppbarWidgetState();
}

class _InvoiceAppbarWidgetState extends State<InvoiceAppbarWidget> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Column(
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonMaterialButton(
                textStyle: TextStyle(fontSize: 12),
                label: T("حفظ وانتقال للتفاصيل"),
                width: context.width / 2.2,
                backgroundColor: context.backgroundColor,
                borderColor: context.colors.scrim,
                textColor: context.colors.scrim,
                borderRadius: 2,
                height: 50,
                onPressed: () async {
                  widget.onSaveAndNavigate();
                },
              ),
              CommonMaterialButton(
                textStyle:
                    TextStyle(fontSize: 12, color: context.backgroundColor),
                width: context.width / 2.2,
                label: T("حفظ وإضافة جديد"),
                borderRadius: 2,
                height: 50,
                onPressed: () {
                  widget.onSave();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class PdfPreviewScreen extends StatefulWidget {
  const PdfPreviewScreen({super.key});

  @override
  _PdfPreviewScreenState createState() => _PdfPreviewScreenState();
}

class _PdfPreviewScreenState extends State<PdfPreviewScreen> {
  String? pdfPath;

  @override
  void initState() {
    super.initState();
    generatePdf();
  }

  Future<void> generatePdf() async {
    // final pdf = pw.Document();
    final invoice = InvoiceDto(
      appReferanceCode: 'INV001',
      custoemrName: 'John Doe',
      invoiceDate: DateTime.now(),
      customerId: 123,
      // Add other required fields as necessary
    );
    // pdf.addPage(
    //   pw.Page(
    //     pageFormat: PdfPageFormat.a4,
    //     build: (context) => pw.Center(
    //       child: pw.Text("Sales Invoice", style: pw.TextStyle(fontSize: 24)),
    //     ),
    //   ),
    // );

    // // Save PDF file
    // final output = await getTemporaryDirectory();
    final file = await createInvoicePdf(invoice);
    // await file.writeAsBytes(await pdf.save());

    setState(() {
      pdfPath = file.path;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('PDF Preview')),
      body: pdfPath == null
          ? Center(child: CircularProgressIndicator())
          : PDFView(
              filePath: pdfPath!,
            ),
    );
  }
}
