import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_confirm_order_dto.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/invoice%20details/inovice_details_page.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:provider/provider.dart';

class QRInvoiceScannerScreen extends StatefulWidget {
  const QRInvoiceScannerScreen({super.key});

  @override
  State<QRInvoiceScannerScreen> createState() => _QRInvoiceScannerScreenState();
}

class _QRInvoiceScannerScreenState extends State<QRInvoiceScannerScreen> {
  final MobileScannerController _scannerController = MobileScannerController();
  bool _isProcessing = false;
  InvoiceDto? _invoiceDetails;
  String _manualInvoiceNumber = '';

  @override
  void dispose() {
    _scannerController.dispose();
    super.dispose();
  }

  bool get _isInternetConnected => AppController.isThereConnection ?? false;

  Future<void> _processQRCode(String code) async {
    if (_isProcessing) return;

    if (!_isInternetConnected) {
      errorSnackBar(
        message: T(
            'You cannot access invoices without an Internet connection Please connect to the Internet first'),
        context: context,
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final invoiceController =
          Provider.of<InvoiceController>(context, listen: false);
      final result = await invoiceController.getInvoiceByTypeAndCode2(
        type: 'Order',
        code: code,
      );

      if (result != null) {
        setState(() {
          _invoiceDetails = result;
          _isProcessing = false;
        });
        successSnackBar(
          message: T('Invoice found successfully!'),
          context: context,
        );
      } else {
        errorSnackBar(
          message: T('Invoice not found'),
          context: context,
        );
        setState(() {
          _isProcessing = false;
        });
      }
    } catch (e) {
      errorSnackBar(
        message: '${T('Error')}: ${e.toString()}',
        context: context,
      );
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          CommonHeader(
            icon: Icons.qr_code_scanner,
            title: T("Scan Invoice QR Code"),
          ),
          Expanded(
            child: !_isInternetConnected
                ? _buildNoInternetWidget()
                : _isProcessing
                    ? _buildLoadingWidget()
                    : _buildMainContent(),
          ),
        ],
      ),
      floatingActionButton: _invoiceDetails != null
          ? FloatingActionButton(
              heroTag: 'scan_again_button',
              onPressed: () {
                setState(() {
                  _invoiceDetails = null;
                });
              },
              backgroundColor: context.colors.primary,
              child: const Icon(Icons.refresh, color: Colors.white),
              tooltip: T("Scan Again"),
            )
          : null,
    );
  }

  Widget _buildNoInternetWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.signal_wifi_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 20),
            Text(
              T('You cannot access invoices without an Internet connection Please connect to the Internet first'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {});
              },
              icon: const Icon(Icons.refresh),
              label: Text(T('Try Again')),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 20),
          Text(
            T('Loading invoice data...'),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Camera controls
          if (_invoiceDetails == null)
            Container(
              decoration: BoxDecoration(
                color: context.colors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    T('Scan QR Code'),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: context.colors.primary,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.flash_on,
                          color: context.colors.primary,
                        ),
                        onPressed: () => _scannerController.toggleTorch(),
                        tooltip: T("Toggle Flash"),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.flip_camera_ios,
                          color: context.colors.primary,
                        ),
                        onPressed: () => _scannerController.switchCamera(),
                        tooltip: T("Switch Camera"),
                      ),
                    ],
                  ),
                ],
              ),
            ),

          const SizedBox(height: 16),

          // Scanner or result
          Expanded(
            child: _invoiceDetails == null
                ? _buildScannerWidget()
                : _buildInvoiceDetails(),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerWidget() {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
      child: ListView(
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        children: [
          Container(
            height: MediaQuery.of(context).size.height * 0.4,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Stack(
                children: [
                  MobileScanner(
                    controller: _scannerController,
                    onDetect: (capture) {
                      final List<Barcode> barcodes = capture.barcodes;
                      if (barcodes.isNotEmpty && !_isProcessing) {
                        final String code = barcodes.first.rawValue ?? '';
                        if (code.isNotEmpty) {
                          _processQRCode(code);
                        }
                      }
                    },
                  ),
                  // QR Scanner Overlay
                  Center(
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.white,
                          width: 3.0,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Stack(
                        children: [
                          // Top-left corner
                          Positioned(
                            top: -3,
                            left: -3,
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                      color: context.colors.primary, width: 6),
                                  left: BorderSide(
                                      color: context.colors.primary, width: 6),
                                ),
                              ),
                            ),
                          ),
                          // Top-right corner
                          Positioned(
                            top: -3,
                            right: -3,
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                      color: context.colors.primary, width: 6),
                                  right: BorderSide(
                                      color: context.colors.primary, width: 6),
                                ),
                              ),
                            ),
                          ),
                          // Bottom-left corner
                          Positioned(
                            bottom: -3,
                            left: -3,
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                      color: context.colors.primary, width: 6),
                                  left: BorderSide(
                                      color: context.colors.primary, width: 6),
                                ),
                              ),
                            ),
                          ),
                          // Bottom-right corner
                          Positioned(
                            bottom: -3,
                            right: -3,
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                      color: context.colors.primary, width: 6),
                                  right: BorderSide(
                                      color: context.colors.primary, width: 6),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Scan line animation
                  Center(
                    child: SizedBox(
                      width: 200,
                      height: 200,
                      child: _ScannerAnimation(),
                    ),
                  ),
                  // Darkened overlay with transparent center
                  CustomPaint(
                    size: Size.infinite,
                    painter: HolePainter(
                      holeSizeWidth: 200,
                      holeSizeHeight: 200,
                      cornerRadius: 12,
                    ),
                  ),
                  // Instruction text
                  Positioned(
                    bottom: 20,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          T('Position the QR code within the frame'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // ElevatedButton.icon(
                //   onPressed: () => _processQRCode("SO2500025"),
                //   icon: const Icon(Icons.receipt),
                //   label: Text(T('Test Invoice SO2500004')),
                //   style: ElevatedButton.styleFrom(
                //     backgroundColor: context.colors.primary,
                //     foregroundColor: Colors.white,
                //     padding: const EdgeInsets.symmetric(
                //         horizontal: 20, vertical: 12),
                //     shape: RoundedRectangleBorder(
                //       borderRadius: BorderRadius.circular(10),
                //     ),
                //   ),
                // ),
                const SizedBox(height: 20),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Text(
                          T('No camera? Enter invoice number manually'),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.colors.primary,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: TextFormField(
                          decoration: InputDecoration(
                            hintText: T('Enter invoice number'),
                            prefixIcon: const Icon(Icons.numbers),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                          ),
                          textInputAction: TextInputAction.search,
                          onFieldSubmitted: (value) {
                            if (value.isNotEmpty) {
                              _processQRCode(value);
                            }
                          },
                          onChanged: (value) {
                            setState(() {
                              _manualInvoiceNumber = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 12),
                      Padding(
                        padding: const EdgeInsets.only(
                            bottom: 12.0, left: 12.0, right: 12.0),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            if (_manualInvoiceNumber.isNotEmpty) {
                              _processQRCode(_manualInvoiceNumber);
                            }
                          },
                          icon: const Icon(Icons.search),
                          label: Text(T('Search Invoices')),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.colors.secondary,
                            foregroundColor: Colors.white,
                            minimumSize: const Size(double.infinity, 45),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Add extra space at the bottom to ensure keyboard doesn't cover content
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom + 100),
        ],
      ),
    );
  }

  Widget _buildInvoiceDetails() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Success message with improved design using the common component
          successMessageBox(
            title: T('Success!'),
            message: T('Invoice details retrieved successfully'),
          ),

          const SizedBox(height: 20),

          // Invoice header
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.colors.primary,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(15),
                      topRight: Radius.circular(15),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.receipt_long,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          T('Invoice Number') +
                              ': ${_invoiceDetails?.invoiceCode}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(
                        T('Date'),
                        _invoiceDetails?.invoiceDate != null
                            ? '${_invoiceDetails!.invoiceDate!.day}/${_invoiceDetails!.invoiceDate!.month}/${_invoiceDetails!.invoiceDate!.year}'
                            : T('N/A'),
                        Icons.calendar_today,
                      ),
                      _buildInfoRow(
                        T('Customer Name'),
                        _invoiceDetails?.custoemrName ?? T('N/A'),
                        Icons.person,
                      ),
                      _buildInfoRow(
                        T('Warehouse'),
                        _invoiceDetails?.warehouseName ?? T('N/A'),
                        Icons.warehouse,
                      ),
                      const Divider(),
                      _buildInfoRow(
                        T('Total'),
                        '${_invoiceDetails?.total?.toStringAsFixed(2) ?? T('N/A')}',
                        Icons.attach_money,
                        valueColor: context.colors.primary,
                      ),
                      _buildInfoRow(
                        T('Discount'),
                        '${_invoiceDetails?.discountValue?.toStringAsFixed(2) ?? '0.00'}',
                        Icons.discount,
                        valueColor: Colors.red,
                      ),
                      _buildInfoRow(
                        T('Net'),
                        '${_invoiceDetails?.totalAfterDiscount?.toStringAsFixed(2) ?? T('N/A')}',
                        Icons.account_balance_wallet,
                        valueColor: Colors.green[700],
                        isBold: true,
                      ),
                      CommonMaterialButton(
                        label: "دفع الفاتورة",
                        onPressed: () async {
                          pleaseWaitDialog(context: context, isShown: true);
                          var oldCode = _invoiceDetails?.invoiceCode;

                          _invoiceDetails?.invoiceCode = "*";
                          _invoiceDetails?.id = 0;
                          _invoiceDetails?.appReferanceCode =
                              await CounterGenerator
                                  .getSaleInvoiceNextCounter();
                          _invoiceDetails?.salesOrderID = oldCode;

                          Provider.of<SaleInvoiceController>(context,
                                  listen: false)
                              .invoice = _invoiceDetails ?? InvoiceDto();
                          Provider.of<SaleInvoiceController>(context,
                                      listen: false)
                                  .selectedSaleInvoiceProduct =
                              _invoiceDetails?.salesItems ?? [];

                          var result = await Provider.of<SaleInvoiceController>(
                                  context,
                                  listen: false)
                              .saveSaleInvoice();

                          if (result.isSuccess) {
                            var confirmToEcommerce = await Provider.of<
                                    InvoiceController>(context, listen: false)
                                .confirmOrderToEcommerce(
                                    EcommerceConfirmOrderDto(
                                        orderCode: oldCode,
                                        referenceId: result.data.id,
                                        salesReferenceCode: result.data.code));

                            if (confirmToEcommerce) {
                              pleaseWaitDialog(
                                  // ignore: use_build_context_synchronously
                                  context: context,
                                  isShown: false);
                              successSnackBar(message: "تم الدفع بنجاح");
                              return;
                            }
                          }
                          // ignore: use_build_context_synchronously
                          pleaseWaitDialog(context: context, isShown: false);
                          errorSnackBar(message: "حدث خطا في عملية الدفع");
                        },
                        backgroundColor: context.errorColor,
                        borderColor: context.errorColor,
                        width: context.width - 20,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Products section
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.colors.secondary,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(15),
                      topRight: Radius.circular(15),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.shopping_cart,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        T('Products'),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                _invoiceDetails?.salesItems != null &&
                        _invoiceDetails!.salesItems!.isNotEmpty
                    ? ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _invoiceDetails!.salesItems!.length,
                        separatorBuilder: (context, index) =>
                            const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final item = _invoiceDetails!.salesItems![index];
                          return Padding(
                            padding: const EdgeInsets.all(12),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color:
                                        context.colors.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${index + 1}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: context.colors.primary,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        item.title ?? T('Unknown Item'),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${T('Quantity')}: ${item.quantity} × ${item.price}',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      '${(item.quantity! * item.price!).toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    if (item.discountValue != null &&
                                        item.discountValue! > 0)
                                      Text(
                                        '${T('Discount')}: ${item.discountValue}',
                                        style: const TextStyle(
                                          color: Colors.red,
                                          fontSize: 12,
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    : Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Column(
                            children: [
                              Icon(
                                Icons.inventory_2_outlined,
                                size: 48,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 10),
                              Text(
                                T('No items found'),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    if (_invoiceDetails != null) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => InoviceDetailsPage(
                            model: _invoiceDetails!,
                          ),
                        ),
                      );
                    }
                  },
                  icon: const Icon(Icons.visibility),
                  label: Text(T('View Full Invoice Details')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.colors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon,
      {Color? valueColor, bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
              fontSize: 15,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.end,
              style: TextStyle(
                fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
                color: valueColor,
                fontSize: isBold ? 16 : 15,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Scanner Animation Widget
class _ScannerAnimation extends StatefulWidget {
  @override
  _ScannerAnimationState createState() => _ScannerAnimationState();
}

class _ScannerAnimationState extends State<_ScannerAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController)
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _animationController.repeat();
        }
      });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: ScanLinePainter(
        progress: _animation.value,
        color: context.colors.primary,
      ),
    );
  }
}

// Scan Line Painter
class ScanLinePainter extends CustomPainter {
  final double progress;
  final Color color;

  ScanLinePainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    final double lineY = progress * size.height;

    // Draw scan line
    canvas.drawLine(
      Offset(0, lineY),
      Offset(size.width, lineY),
      paint,
    );

    // Draw glow effect
    final Paint glowPaint = Paint()
      ..color = color.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);

    canvas.drawLine(
      Offset(0, lineY),
      Offset(size.width, lineY),
      glowPaint,
    );
  }

  @override
  bool shouldRepaint(ScanLinePainter oldDelegate) =>
      oldDelegate.progress != progress;
}

// Hole Painter for darkened overlay with transparent center
class HolePainter extends CustomPainter {
  final double holeSizeWidth;
  final double holeSizeHeight;
  final double cornerRadius;

  HolePainter({
    required this.holeSizeWidth,
    required this.holeSizeHeight,
    required this.cornerRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final double left = (size.width - holeSizeWidth) / 2;
    final double top = (size.height - holeSizeHeight) / 2;
    final double right = left + holeSizeWidth;
    final double bottom = top + holeSizeHeight;

    final Path path = Path()
      ..fillType = PathFillType.evenOdd
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTRB(left, top, right, bottom),
          Radius.circular(cornerRadius),
        ),
      );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(HolePainter oldDelegate) =>
      oldDelegate.holeSizeWidth != holeSizeWidth ||
      oldDelegate.holeSizeHeight != holeSizeHeight ||
      oldDelegate.cornerRadius != cornerRadius;
}
