import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class ReceiveFromAccountScreen extends StatefulWidget {
  const ReceiveFromAccountScreen({super.key});

  @override
  State<ReceiveFromAccountScreen> createState() =>
      _ReceiveFromAccountScreenState();
}

class _ReceiveFromAccountScreenState extends State<ReceiveFromAccountScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header Row: Title and Back Button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'ادارة سندات القبض', // Title
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Implement back navigation
                  },
                  icon: Icon(Icons.arrow_back),
                  label: Text('رجوع للقائمة'),
                ),
              ],
            ),
            SizedBox(height: 20),

            // First Section: Top input fields (رقم السند, التاريخ, طرق الدفع, etc.)
            Card(
              elevation: 2.0,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // Row 1
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(labelText: 'رقم السند'),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                                labelText: 'التاريخ'), // التاريخ
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                                labelText: 'طرق الدفع'), // طرق الدفع
                            items: [], // TODO: Add actual items
                            onChanged: (value) {},
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    // Row 2
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                                labelText:
                                    'رقم فاتورة المبيعات'), // رقم فاتورة المبيعات
                            items: [], // TODO: Add actual items
                            onChanged: (value) {},
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                                labelText: 'مبلغ السند'), // مبلغ السند
                          ),
                        ),
                        SizedBox(width: 16),
                        // Currency field with static unit
                      ],
                    ),
                    SizedBox(height: 16),
                    // Row 3
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                                labelText: 'اسم العميل'), // اسم العميل
                            items: [], // TODO: Add actual items
                            onChanged: (value) {},
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                                labelText:
                                    'اجمالي الفاتورة'), // اجمالي الفاتورة
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),

                    // Row 5
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration:
                                InputDecoration(labelText: 'الدافع'), // الدافع
                            items: [], // TODO: Add actual items
                            onChanged: (value) {},
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                                labelText: 'مدين / المدفوع له',
                                hintText:
                                    'رقم الحساب :: اسم الحساب'), // مدين / المدفوع له with hint
                            items: [], // TODO: Add actual items
                            onChanged: (value) {},
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    // Row 6 - البيان
                    TextFormField(
                      decoration:
                          InputDecoration(labelText: 'البيان'), // البيان
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            // Paid in Advance Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المدفوع مسبقا', // المدفوع مسبقا
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration:
                        InputDecoration(labelText: ''), // Paid in advance input
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            // Balance Section
            Card(
              elevation: 2.0,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Text('الرصيد'),
                        Text('0.000',
                            style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                    Column(
                      children: [
                        Text('دائن'),
                        Text('0.000',
                            style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                    Column(
                      children: [
                        Text('مدين'),
                        Text('0.000',
                            style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            Text(
              'سندات القبض',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            // سندات القبض Table
            DataTable(
              columns: [
                DataColumn(label: Text('رقم السند')),
                DataColumn(label: Text('التاريخ')),
                DataColumn(label: Text('مبلغ السند')),
                DataColumn(label: Text('الدافع')),
                DataColumn(label: Text('البيان')),
              ],
              rows: [
                // TODO: Add actual data rows here
              ],
            ),
            SizedBox(height: 10),
            Center(
              child: Text(
                'لا يوجد بيانات لعرضها', // Message when table is empty
                style:
                    TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              ),
            ),

            SizedBox(height: 20),

            // Bottom Buttons (طباعة, إلغاء, حفظ)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement Print functionality
                  },
                  child: Text('طباعة'),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement Save functionality
                  },
                  child: Text('حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }
}
