import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class ReceiveFromAccountScreen extends StatefulWidget {
  const ReceiveFromAccountScreen({super.key});

  @override
  State<ReceiveFromAccountScreen> createState() =>
      _ReceiveFromAccountScreenState();
}

class _ReceiveFromAccountScreenState extends State<ReceiveFromAccountScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
