import 'package:bottom_bar_matu/utils/app_utils.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/inverntory/invoices/invoice%20details/invoice_details_for_local_screen.dart';
import 'package:provider/provider.dart';

class LocacInvoicesList extends StatefulWidget {
  LocacInvoicesList({
    super.key,
    required this.invoicesFuture,
    required this.selectedFilter,
  });

  final Future<List<InvoiceDtoWithLiteId>> invoicesFuture;
  final String selectedFilter;

  @override
  State<LocacInvoicesList> createState() => _LocacInvoicesListState();
}

class _LocacInvoicesListState extends State<LocacInvoicesList> {
  late List<InvoiceDtoWithLiteId> _invoices = [];

  @override
  void initState() {
    super.initState();
    widget.invoicesFuture.then((invoices) {
      setState(() {
        _invoices = invoices;
      });
    });
  }

  void _removeLocalInvoice(String invoiceId) {
    setState(() {
      _invoices.removeWhere((invoice) => invoice.id == invoiceId);
    });
  }

  void _refreshInvoices() {
    setState(() {});
  }

  void _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required Function delete,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            title,
            style: TextStyle(
              color: context.newSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            content,
            style: TextStyle(
              color: context.newTextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: Text(
                T("Cancel"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                delete();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<InvoiceDtoWithLiteId>>(
      future: widget.invoicesFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SliverFillRemaining(
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (snapshot.hasError) {
          return SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    T('Error loading invoices'),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: context.newTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${snapshot.error}',
                    style: TextStyle(
                      color: context.newTextColor.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long,
                    color: context.newPrimaryColor.withOpacity(0.3),
                    size: 80,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    T('No invoices found'),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: context.newTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    T('Try changing your filter or create a new invoice'),
                    style: TextStyle(
                      color: context.newTextColor.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        // Filter invoices based on the selected filter
        final invoices = widget.selectedFilter == T("All")
            ? snapshot.data! // If "All" is selected, use all data
            : snapshot.data!.where((invoice) {
                return invoice.status == widget.selectedFilter.toLowerCase();
              }).toList();

        return SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                // Protección contra índices fuera de rango
                if (index >= invoices.length) {
                  return const SizedBox.shrink();
                }

                final invoice = invoices[index].data;
                final status = invoices[index].status ?? "pending";
                final String invoiceCode = invoice?.code ?? "";
                final invoiceType = invoiceCode.startsWith("SI")
                    ? T("Sales Invoice")
                    : T("Return Invoice");

                // Valores seguros para evitar problemas de nulos
                final String customerName =
                    invoice?.customerName ?? T("No Customer Name");
                final String localCode = invoice?.appReferanceCode ?? "";
                final String serverCode = invoice?.code ?? "";
                final double paidAmount =
                    invoice?.paidAmount?.toStringAsFixed(2).toDouble() ?? 0.0;
                final DateTime? entryDate = invoice?.entryDate;

                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Invoice header
                      Container(
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: context.newBackgroundColor,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.receipt,
                                        color: context.newSecondaryColor,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        invoiceType,
                                        style: TextStyle(
                                          color: context.newSecondaryColor,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    customerName,
                                    style: TextStyle(
                                      color: context.newTextColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 15,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: context.newPrimaryColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                "".myDateFormatter(entryDate,
                                    isShowTime: false),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Invoice details
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 12),
                        child: Column(
                          children: [
                            _buildInvoiceInfoRow(
                              context,
                              T("Local Code"),
                              localCode,
                            ),
                            _buildInvoiceInfoRow(
                              context,
                              T("Server Code"),
                              serverCode,
                            ),
                            _buildInvoiceInfoRow(
                              context,
                              T("Status"),
                              status == "pending" ? T("Pending") : T("Synced"),
                              valueColor: status == "pending"
                                  ? Colors.orange
                                  : Colors.green,
                            ),
                            _buildInvoiceInfoRow(
                              context,
                              T("Total"),
                              0.0.covertDoubleToMoneyReturnString(paidAmount),
                              isTotal: true,
                            ),
                          ],
                        ),
                      ),

                      // Action buttons
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 10),
                        decoration: BoxDecoration(
                          color: context.newBackgroundColor.withOpacity(0.3),
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(12),
                            bottomRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.of(context).push(MaterialPageRoute(
                                    builder: (context) =>
                                        InoviceDetailsForLocalPage(
                                      id: int.tryParse(localCode) ?? 0,
                                    ),
                                  ));
                                },
                                icon: const Icon(Icons.visibility, size: 18),
                                label: Text(T("Details")),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: context.newPrimaryColor,
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () async {
                                  _showConfirmDialog(
                                    title: T("Warning"),
                                    content: T(
                                        "Are you sure you want to delete this invoice?"),
                                    confirmText: T("Delete"),
                                    delete: () async {
                                      if (status == "pending") {
                                        if (localCode.isNotEmpty) {
                                          int result = await Provider.of<
                                                      InvoiceController>(
                                                  context,
                                                  listen: false)
                                              .deleteInvoice(localCode);

                                          if (result > 0) {
                                            _removeLocalInvoice(invoices[index]
                                                    .id
                                                    ?.toString() ??
                                                localCode);
                                            successSnackBar(
                                                message:
                                                    T("Successfully deleted"));
                                            _refreshInvoices();
                                          } else {
                                            errorSnackBar(
                                                message: T(
                                                    "Failed to delete invoice"));
                                          }
                                        }
                                      } else if (status == "synced") {
                                        int local = await Provider.of<
                                                    InvoiceController>(context,
                                                listen: false)
                                            .deleteInvoice(localCode);

                                        if (local > 0) {
                                          _removeLocalInvoice(
                                              invoices[index].id?.toString() ??
                                                  localCode);
                                          successSnackBar(
                                              message:
                                                  T("Successfully deleted"));
                                        } else {
                                          errorSnackBar(
                                              message: T(
                                                  "Failed to delete invoice"));
                                        }
                                      }
                                    },
                                  );
                                },
                                icon:
                                    const Icon(Icons.delete_outline, size: 18),
                                label: Text(T("Delete")),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.red,
                                  side: const BorderSide(color: Colors.red),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
              childCount: invoices.length,
            ),
          ),
        );
      },
    );
  }

  Widget _buildInvoiceInfoRow(BuildContext context, String label, String value,
      {bool isTotal = false, Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: context.newTextColor.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: valueColor ??
                  (isTotal ? context.newSecondaryColor : context.newTextColor),
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
        ],
      ),
    );
  }
}
