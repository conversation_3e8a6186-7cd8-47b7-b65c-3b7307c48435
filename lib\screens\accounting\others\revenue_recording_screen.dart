import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class RevenueRecodingScreen extends StatefulWidget {
  const RevenueRecodingScreen({super.key});

  @override
  State<RevenueRecodingScreen> createState() => _RevenueRecodingScreenState();
}

class _RevenueRecodingScreenState extends State<RevenueRecodingScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
