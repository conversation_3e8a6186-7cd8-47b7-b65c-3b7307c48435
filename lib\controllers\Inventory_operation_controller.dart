import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/base/network/ecommerce_api.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/models/dto/inventoryOperations/inventory_operation_dto.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_balance_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_order_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';

class InventoryOperationController with ChangeNotifier {
  List<InvoiceDto> invoices = [];
  InvoiceDto? invoiceDetails;
  // List<InvoiceDtoWithLiteId> _allInvoices = [];
  // List<InvoiceDtoWithLiteId> _pendingInvoices = [];
  // List<InvoiceDtoWithLiteId> _syncedInvoices = [];
  List<SqlLiteInvoiceModel> localInvoices = [];

  // List<InvoiceDtoWithLiteId> get allInvoices => _allInvoices;
  // List<InvoiceDtoWithLiteId> get pendingInvoices => _pendingInvoices;
  // List<InvoiceDtoWithLiteId> get syncedInvoices => _syncedInvoices;

  // InvoiceListFilterDTO filterModel = InvoiceListFilterDTO(
  //     fromDate: DateTime.now().add(
  //       const Duration(days: -30),
  //     ),
  //     toDate: DateTime.now(),
  //     invoiceTypeName: "فواتير مبيعات",
  //     dataTableParameters: DataTableParameters(),
  //     invoiceType: SalesType.Invoice);

  // final int _pageSize = 10;

//---------------------------------------------------------------------------
//   Future<InvoiceDto?> getInvoiceByTypeAndCode2({
//     required String type,
//     required String code,
//   }) async {
//     try {
//       var url = '/Sales/Manage?transactions_type=$type&code=$code';

//       var result = await Api.getOne(action: url);
//       if (result != null) {
//         if (result.isSuccess) {
//           var invoiceModel = InvoiceModel.fromJson(result.data);
//           print('Total: ${invoiceDetails?.total}');
//           print('Total After Discount: ${invoiceDetails?.totalAfterDiscount}');
//           invoiceDetails = mapInvoiceModel(invoiceModel);
//           invoiceDetails?.total = invoiceDetails?.calculateTotal();
//           invoiceDetails?.totalDiscount =
//               invoiceDetails?.calculateTotalDiscount();
//           invoiceDetails?.totalAfterDiscount =
//               invoiceDetails?.calculateTotalAfterDiscount();
//           invoiceDetails?.totalAfterDiscount =
//               invoiceDetails?.calculateNetTotal();
//           // notifyListeners();
//           return invoiceDetails;
//         } else {
//           return null;
//         }
//       }

//       return null;
//     } catch (e) {
//       print(e);
//       return null;
//     }
//   }

// //---------------------------------------------------------------------------

//   Future<void> getInvoices({bool resetAndRefresh = false}) async {
//     try {
//       if (resetAndRefresh) {
//         invoices.clear();
//       }

//       var url = '/Sales/SalesList';
//       filterModel.invoiceTypeNameForRequest =
//           getInvoiceTypeName(filterModel.invoiceType);
//       filterModel.dataTableParameters = DataTableParameters(
//           columnName: "Entry_Date",
//           dir: "desc",
//           skip: invoices.length,
//           take: _pageSize);

//       var result = await Api.post(action: url, body: filterModel.toJson());
//       if (result != null) {
//         if (result.isSuccess) {
//           for (var element in result.data) {
//             var invoiceModel = InvoiceModel.fromJson(element);
//             invoices.add(mapInvoiceModel(invoiceModel));
//             print(element);
//           }
//         }
//       }

//       notifyListeners();
//     } catch (e) {
//       print(e);
//     }
//   }

// //---------------------------------------------------------------------------
//   Future<InvoiceModel?> getInvoiceByTypeAndCode(
//       {required String type, required String code}) async {
//     try {
//       var url = '/Sales/Manage?transactions_type=$type&code=$code';

//       var result = await Api.getOne(action: url);
//       if (result != null) {
//         if (result.isSuccess) {
//           var productModel = InvoiceModel.fromJson(result.data);

//           return productModel;
//         } else {
//           return null;
//         }
//       }

//       return null;
//     } catch (e) {
//       print(e);
//       return null;
//     }
//   }

//---------------------------------------------------------------------------
  // Function to insert an invoice
  Future<int> insertOrUpdateInventoryOperation(
      Map<String, dynamic> operation) async {
    var IdToReturn = 0;
    final db = await DatabaseHelper().database;
    var checkIfExist =
        await checkIfInventoryOperationExists(operation["localCode"]);
    if (checkIfExist == false) {
      IdToReturn = await db.insert('InventoryOperation', operation);
    } else {
      IdToReturn = await updateInvoice(
          operation["localCode"], json.decode(operation["data"]));
    }

    return IdToReturn;
  }

//------------------------------------------------------------------------------
  // Function to get pending invoices
  Future<List<InventoryOperationDtoWithLiteId>> getPendingOperation() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('InventoryOperation',
        where: 'status = ?',
        whereArgs: [getInvoiceSyncStatus(InvoiceSyncStatus.pending)]);
    print(result); // Check raw database result

    List<InventoryOperationDtoWithLiteId> operations = [];
    for (var element in result) {
      var operationModel = SqlLiteInvoiceModel.fromJson(element);

      if (operationModel.data != null) {
        try {
          // Parse data field as JSON
          var parsedData = jsonDecode(operationModel.data!);
          // print("Parsed data: $parsedData");

          var invoiceData = InventoryOperationModel.fromJson(parsedData);

          operations.add(InventoryOperationDtoWithLiteId(
              data: invoiceData, id: operationModel.id));
        } catch (e) {
          // ignore: avoid_print
          print("Error parsing or deserializing data: $e");
        }
      }
    }

    return operations;
  }

//------------------------------------------------------------------------------
  Future<bool> syncOperationWithSever() async {
    try {
      var operations = await getPendingOperation();
      if (operations.isEmpty) {
        // errorSnackBar(message: T("There are no invoices to sync"));
        return false;
      }

      var url = '/InventoryOperation/Manage';
      for (var operation in operations) {
        operation.data?.code = "*";

        var result = await Api.post(
          action: url,
          body: operation.data?.toJson(),
        );
        if (result != null) {
          if (result.isSuccess) {
            await updateInventoryOperationSyncStatus(
                getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                operation.id ?? 0);
            await updateInventoryOperationServerId(
                operation.id ?? 0, result.data["ID"], result.data["Code"]);
            operation.data?.code = result.data["Code"];
            operation.data?.iD = result.data["ID"];
            // if (operation.data?.operationType == "RetrunInvoice") {
            //   await saveReturnToEcommerce(operation.data ?? InventoryOperationModel());
            // } else {
            //   await saveOrderToEcommerce(operation.data ?? InventoryOperationModel());
            // }
          }

          notifyListeners();
          await InventoryOperationCounterGenerator
              .setInventoryOperationCounterInServer();

          // successSnackBar(message: "تمت مزامنة hg بنجاح");
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

//------------------------------------------------

  // Function to update invoice sync status
  Future<int> updateInventoryOperationSyncStatus(String status, int id) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      'InventoryOperation',
      {'status': status},
      where: 'ID = ?',
      whereArgs: [id],
    );
  }

  //------------------------------------------------------------------------------
  Future<int> updateInventoryOperationServerId(
      int localId, int serverId, String code) async {
    final db = await DatabaseHelper().database;

    // Retrieve the existing data for the invoice
    var result = await db.query(
      'InventoryOperation',
      columns: ['data'],
      where: 'ID = ?',
      whereArgs: [localId],
    );

    if (result.isNotEmpty) {
      // Parse the JSON string to a Map
      Map<String, dynamic> data = jsonDecode(result.first['data'] as String);

      // Update the data.ID field with the new server ID
      data['ID'] = serverId;
      data['Code'] = code;

      // Convert the updated map back to a JSON string
      String updatedDataJson = jsonEncode(data);

      // Update only the data field in the database
      return await db.update(
        'InventoryOperation',
        {
          'data': updatedDataJson, // Update the data with the new ID
        },
        where: 'ID = ?',
        whereArgs: [localId],
      );
    } else {
      // If no record is found, return 0 to indicate failure
      return 0;
    }
  }
  //------------------------------------------------------------------------------

  Future<List<SqlLiteInvoiceModel>> getOperationFromLocalStorage() async {
    final db = await DatabaseHelper().database; // Get the database instance

    final List<Map<String, dynamic>> result =
        await db.query('InventoryOperation');

    // Map the result to a list of SqlLiteInvoiceModel instances
    return result.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
  }

//--------------------------------------------------------

  Future<InventoryOperationModel> getOperationById(int id) async {
    final db = await DatabaseHelper().database;
    var invoice = await db.query(
      'InventoryOperation',
      where: 'ID = ?',
      whereArgs: [id],
    );

    var mappedItem =
        invoice.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
    var result = mappedItem
        .map(
          (e) => InventoryOperationDtoWithLiteId(
            id: e.id,
            status: e.status,
            data: InventoryOperationModel.fromJson(
              json.decode(e.data ?? ""),
            ),
          ),
        )
        .toList();
    if (result.isNotEmpty) {
      // invoiceDto.totalAfterDiscount = invoiceDto.calculateNetTotal();

      return result[0].data ?? InventoryOperationModel();
    }

    return InventoryOperationModel();
  }

//--------------------------------------------------------
  Future<List<InventoryOperationDtoWithLiteId>> fetchLocalInvoices() async {
    try {
      localInvoices = await getOperationFromLocalStorage();

      var data = localInvoices
          .map(
            (e) => InventoryOperationDtoWithLiteId(
              id: e.id,
              status: e.status,
              data: InventoryOperationModel.fromJson(
                json.decode(e.data ?? ""),
              ),
            ),
          )
          .toList();

      return data;
    } catch (e) {
      // ignore: avoid_print
      print(e);
      return [];
    }
  }

//------------------------------------------------------------------------------
  Future<bool> checkIfInventoryOperationExists(
      String operationLocalCode) async {
    final db = await DatabaseHelper().database;

    var result = await db.query(
      'InventoryOperation',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [operationLocalCode],
    );

    return result.isNotEmpty;
  }

//------------------------------------------------------------------------------
  Future<InventoryOperationModel?> getInventoryOperationByCode(
      {required String invoiceLocalCode}) async {
    final db = await DatabaseHelper().database;
    var operation = await db.query(
      'InventoryOperation',
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    var mappedItem =
        operation.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
    var result = mappedItem
        .map(
          (e) => InventoryOperationDtoWithLiteId(
            id: e.id,
            status: e.status,
            data: InventoryOperationModel.fromJson(
              json.decode(e.data ?? ""),
            ),
          ),
        )
        .toList();
    if (result.isNotEmpty) {
      result[0].data?.localId = result[0].id;
      // var invoiceDto = mapInvoiceModel(result[0].data ?? InvoiceModel());
      // invoiceDto.total = invoiceDto.calculateTotal();
      // invoiceDto.totalDiscount = invoiceDto.calculateTotalDiscount();
      // invoiceDto.totalAfterDiscount = invoiceDto.calculateTotalAfterDiscount();
      // invoiceDto.totalAfterDiscount = invoiceDto.calculateNetTotal();

      return result[0].data;
    }

    return null;
  }

//------------------------------------------------------------------------------
  Future<int> updateInvoice(
      String invoiceLocalCode, Map<String, dynamic> invoice) async {
    final db = await DatabaseHelper().database;

    // Retrieve the existing data for the invoice
    var result = await db.query(
      'InventoryOperation',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    if (result.isNotEmpty) {
      // Parse the JSON string to a Map
      Map<String, dynamic> data = jsonDecode(result.first['data'] as String);

      data = invoice;

      // Convert the updated map back to a JSON string
      String updatedDataJson = jsonEncode(data);

      // Update only the data field in the database
      var response = await db.update(
        'InventoryOperation',
        {
          'data': updatedDataJson, // Update the data with the new ID
        },
        where: 'localCode = ?',
        whereArgs: [invoiceLocalCode],
      );
      // var sadas = response;
      return response;
    } else {
      // If no record is found, return 0 to indicate failure
      return 0;
    }
  }

// //------------------------------------------------------------------------------

//   Future<int> deleteInvoice(String id) async {
//     final db = await DatabaseHelper().database;

//     // Perform the deletion
//     int result = await db.delete(
//       'Invoice',
//       where: 'localCode = ?',
//       whereArgs: [id],
//     );

//     // Return the number of rows affected (should be 1 if successful)
//     return result;
//   }

// //-----------------------------------------------------------------------------
//   Future<bool> deleteSyncInvoice(int invoiceId, String transactionsType) async {
//     try {
//       var url =
//           'Sales/Delete?transactions_type=$transactionsType&id=$invoiceId';

//       var result = await Api.post(action: url);
//       if (result?.isSuccess == true) {
//         successSnackBar(message: "تم الحذف بنجاح");
//       }

//       final db = await DatabaseHelper().database;
//       var deleteResult = await db.delete(
//         'Invoice',
//         where: 'localCode = ?',
//         whereArgs: [invoiceId],
//       );

//       if (deleteResult == 0) {
//         updateInvoiceDeletionStatus(invoiceId);
//         notifyListeners();
//         print("Invoice successfully deleted from local database.");
//         return true;
//       } else {
//         errorSnackBar(
//             message: "Invoice deleted from server but not found locally.");
//         return false;
//       }
//     } catch (e, stackTrace) {
//       print("Error during invoice deletion: $e");
//       print("Stack Trace: $stackTrace");
//       errorSnackBar(message: "Error: ${e.toString()}");
//       return false;
//     }
//   }

// //-----------------------------------------------------------------------------
//   Future<void> updateInvoiceDeletionStatus(int invoiceId) async {
//     invoices.removeWhere((invoice) => invoice.id == invoiceId);
//     notifyListeners();
//   }

//   //---------------------------------------------------------------------------
  Future<void> saveInventoryOperationToEcommerce(
      InventoryOperationModel model, TransactionTypes type) async {
    try {
      var url = 'api/A_ItemTransactions/SaveInventoryOperationFromErp';

      var mapedOperation =
          mapInventoryOperationModelWithEcommerceDto(model, type);

      var result = await EcommerceApi.post(
        action: url,
        body: mapedOperation.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          notifyListeners();
        }
      }
    } catch (e) {
      print(e);
    }
  }

//   //---------------------------------------------------------------------------
//   Future<bool> confirmOrderToEcommerce(EcommerceConfirmOrderDto model) async {
//     try {
//       var url = 'api/A_Orders/ConfirmOrderInvoiceFromErp';

//       var result = await EcommerceApi.post(
//         action: url,
//         body: model.toJson(),
//       );
//       if (result != null) {
//         if (result.isSuccess) {
//           notifyListeners();
//           return true;
//         }
//       }
//       return false;
//     } catch (e) {
//       print(e);
//       return false;
//     }
//   }

  //---------------------------------------------------------------------------
  // Future<void> saveReturnToEcommerce(InventoryOperationModel model) async {
  //   try {
  //     var url = 'api/A_Orders/SaveReturnOrder';

  //     var mapedInvoice = mapInventoryOperationModelWithEcommerceDto(
  //         model, TransactionTypes.refund);

  //     var result = await EcommerceApi.post(
  //       action: url,
  //       body: mapedInvoice.toJson(),
  //     );
  //     if (result != null) {
  //       if (result.isSuccess) {
  //         notifyListeners();
  //       }
  //     }
  //   } catch (e) {
  //     print(e);
  //   }
  // }

  //---------------------------------------------------------------------------
  // Stocktaking Draft Management Methods

  // Save stocktaking draft to database
  Future<int> saveStocktakingDraft(Map<String, dynamic> draftData) async {
    try {
      final db = await DatabaseHelper().database;

      // Ensure items are properly JSON encoded
      if (draftData['items'] is List && draftData['items'] is! String) {
        draftData['items'] = jsonEncode(draftData['items']);
      }

      // Check if draft already exists
      var existing = await db.query(
        'StocktakingDrafts',
        where: 'stocktakingId = ?',
        whereArgs: [draftData['stocktakingId']],
      );

      if (existing.isNotEmpty) {
        // Update existing draft
        return await db.update(
          'StocktakingDrafts',
          draftData,
          where: 'stocktakingId = ?',
          whereArgs: [draftData['stocktakingId']],
        );
      } else {
        // Insert new draft
        return await db.insert('StocktakingDrafts', draftData);
      }
    } catch (e) {
      print("Error in saveStocktakingDraft: $e");
      rethrow; // Re-throw the error so it can be handled by the calling method
    }
  }

  // Get all stocktaking drafts
  Future<List<Map<String, dynamic>>> getStocktakingDrafts() async {
    final db = await DatabaseHelper().database;
    return await db.query(
      'StocktakingDrafts',
      orderBy: 'lastModified DESC',
    );
  }

  // Get stocktaking draft by ID
  Future<Map<String, dynamic>?> getStocktakingDraft(
      String stocktakingId) async {
    final db = await DatabaseHelper().database;
    var result = await db.query(
      'StocktakingDrafts',
      where: 'stocktakingId = ?',
      whereArgs: [stocktakingId],
    );
    return result.isNotEmpty ? result.first : null;
  }

  // Get items from stocktaking draft
  Future<List<ProductDTO>> getStocktakingDraftItems(
      String stocktakingId) async {
    final db = await DatabaseHelper().database;
    var result = await db.query(
      'StocktakingDrafts',
      where: 'stocktakingId = ?',
      whereArgs: [stocktakingId],
    );

    if (result.isEmpty) return [];

    var itemsJson = result.first['items'] as String;
    List<dynamic> itemsList = jsonDecode(itemsJson);

    return itemsList
        .map((item) => ProductDTO(
              id: item['id'],
              title: item['title'],
              barcode: item['barcode'],
              code: item['code'],
              quantity: (item['quantity'] as num?)?.toDouble(),
              virtualProductId: item['virtualProductId'],
              hasSelectedAttributes: item['hasSelectedAttributes'] ?? false,
              description: item['description'],
            ))
        .toList();
  }

  // Delete stocktaking draft
  Future<int> deleteStocktakingDraft(String stocktakingId) async {
    final db = await DatabaseHelper().database;
    return await db.delete(
      'StocktakingDrafts',
      where: 'stocktakingId = ?',
      whereArgs: [stocktakingId],
    );
  }

  Future<List<StocktakingBalanceDto>> getServerBalances(
      String storeId, List<int> itemIds) async {
    try {
      final requestBody = {
        "StoreId": storeId,
        "ItemIds": itemIds,
      };

      final result = await Api.post(
        action: "/InventoryOperation/GetBalaceForStckTaking",
        body: requestBody,
      );
      List<StocktakingBalanceDto> balances = [];
      if (result != null && result.isSuccess && result.data != null) {
        // Parse response and create map of ItemId -> Quantity
        for (var element in result.data) {
          balances.add(StocktakingBalanceDto.fromJson(element));
        }

        return balances;
      }

      return [];
    } catch (e) {
      throw Exception('Failed to get server balances: $e');
    }
  }
}
