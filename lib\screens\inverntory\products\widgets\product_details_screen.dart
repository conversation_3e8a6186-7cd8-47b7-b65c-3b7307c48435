import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/inverntory/products/widgets/details_tab_widget.dart';
import 'package:inventory_application/screens/inverntory/products/widgets/price_tab_widget.dart';
import 'package:inventory_application/screens/inverntory/products/widgets/warehouses_tab_widget.dart';

// ignore: must_be_immutable
class ProductDetailsScreen extends StatefulWidget {
  ProductDTO data;
  ProductDetailsScreen({super.key, required this.data});

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3, // Number of tabs
      child: ApplicationLayout(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Column(
            children: [
              CommonHeader(
                icon: Icons.blinds_closed,
                title: T("تفاصيل المنتج"),
              ),
              const SizedBox(height: 10),
              // TabBar(
              //   labelColor: Theme.of(context).primaryColor,
              //   unselectedLabelColor: Colors.grey,
              //   indicatorColor: Theme.of(context).primaryColor,
              //   onTap: (index) {
              //     setState(() {
              //       _selectedIndex = index;
              //     });
              //   },
              //   tabs: [
              //     _buildTab("معلومات المنتج", 0),
              //     _buildTab("المخازن", 1),
              //     _buildTab("الأسعار", 2),
              //   ],
              // ),
              const SizedBox(height: 15),
              Expanded(
                child: TabBarView(
                  children: [
                    DetailsTabbarWidget(data: widget.data),
                    WarehousesTab(data: widget.data),
                    PriceTabWidget(data: widget.data),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build the tab with custom background color for selected tab
  Widget _buildTab(String text, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Tab(
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 14),
        ),
      ),
    );
  }
}
