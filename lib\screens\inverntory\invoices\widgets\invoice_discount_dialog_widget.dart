import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/helpers/function.dart';

enum DiscountType { invoiceTotal, onProducts }

void invoiceDiscountDialog({
  required BuildContext context,
  required InvoiceDto invoice,
  required List<ProductDTO> selectedInvoiceProduct,
  required Function onSetProductDiscount,
  required Function onSetInvoiceDiscount,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return InvoiceDiscountDialogContent(
        model: invoice,
        selectedInvoiceProduct: selectedInvoiceProduct,
        onSetInvoiceDiscount: (InvoiceDiscountType type, double value) {
          onSetInvoiceDiscount(type, value);
        },
        onSetProductDiscount: (int id, double discount) {
          onSetProductDiscount(id, discount);
        },
      );
    },
  );
}

class InvoiceDiscountDialogContent extends StatefulWidget {
  const InvoiceDiscountDialogContent(
      {super.key,
      required this.model,
      required this.selectedInvoiceProduct,
      required this.onSetProductDiscount,
      required this.onSetInvoiceDiscount});

  final InvoiceDto model;
  final List<ProductDTO> selectedInvoiceProduct;
  final Function onSetProductDiscount;
  final Function onSetInvoiceDiscount;

  @override
  // ignore: library_private_types_in_public_api
  _InvoiceDiscountDialogContentState createState() =>
      _InvoiceDiscountDialogContentState();
}

class _InvoiceDiscountDialogContentState
    extends State<InvoiceDiscountDialogContent> {
  DiscountType? _selectedDiscountType = DiscountType.invoiceTotal;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      elevation: 5,
      child: SingleChildScrollView(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: context.width - 32,
            maxHeight: context.height * 0.85,
          ),
          padding: const EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with invoice info
              Container(
                width: context.width,
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                decoration: BoxDecoration(
                  color: context.newBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "تفاصيل الخصم",
                          style: TextStyle(
                            color: context.newSecondaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "اجمالي الفاتورة: ${widget.model.total?.covertDoubleToMoneyReturnString(widget.model.total ?? 0) ?? "0"}",
                          style: TextStyle(
                            color: context.newTextColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 15),

              // Discount type selection header
              Container(
                padding: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: context.newSecondaryColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                width: double.infinity,
                child: Text(
                  "نــوع الخصم",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),

              const SizedBox(height: 15),

              // Discount type radio buttons in a styled container
              Container(
                width: context.width,
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                decoration: BoxDecoration(
                  color: context.newBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Radio buttons
                    Row(
                      children: [
                        Radio<DiscountType>(
                          activeColor: context.newPrimaryColor,
                          value: DiscountType.invoiceTotal,
                          groupValue: _selectedDiscountType,
                          onChanged: (DiscountType? value) {
                            setState(() {
                              _selectedDiscountType = value;
                            });
                          },
                        ),
                        SizedBox(
                          width: context.width / 2,
                          child: Text(
                            "خصم على اجمالي الفاتورة",
                            style: TextStyle(
                              color: context.newTextColor,
                              fontSize: 16,
                              fontWeight: _selectedDiscountType ==
                                      DiscountType.invoiceTotal
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                            softWrap: true,
                          ),
                        ),
                      ],
                    ),
                    const Divider(thickness: 1),
                    Row(
                      children: [
                        Radio<DiscountType>(
                          activeColor: context.newPrimaryColor,
                          value: DiscountType.onProducts,
                          groupValue: _selectedDiscountType,
                          onChanged: (DiscountType? value) {
                            setState(() {
                              _selectedDiscountType = value;
                            });
                          },
                        ),
                        Text(
                          "خصم على المنتجات",
                          style: TextStyle(
                            color: context.newTextColor,
                            fontSize: 16,
                            fontWeight:
                                _selectedDiscountType == DiscountType.onProducts
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 15),

              // Discount details header
              Container(
                padding: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: context.newSecondaryColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                width: double.infinity,
                child: Text(
                  _selectedDiscountType == DiscountType.invoiceTotal
                      ? "خصم على اجمالي الفاتورة"
                      : "خصم على المنتجات",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),

              const SizedBox(height: 15),

              // Discount content
              Container(
                width: context.width,
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                decoration: BoxDecoration(
                  color: context.newBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: _selectedDiscountType == DiscountType.invoiceTotal
                    ? InvoiceDiscountDialogWidget(
                        invoice: widget.model,
                        onSetInvoiceDiscount:
                            (InvoiceDiscountType type, double value) {
                          widget.onSetInvoiceDiscount(type, value);
                        },
                      )
                    : _buildProductDiscount(
                        context, widget.selectedInvoiceProduct, (id, discount) {
                        widget.onSetProductDiscount(id, discount);
                      }),
              ),

              // Buttons
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.newPrimaryColor,
                      foregroundColor: Colors.white,
                      elevation: 3,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 25, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      T("تطبيق"),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: context.newSecondaryColor,
                      side: BorderSide(color: context.newSecondaryColor),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 25, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      T("إلغاء"),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  //--------------------------------------------------------------------------------

  // Content for "On Products Discount"
  Widget _buildProductDiscount(
    BuildContext context,
    List<ProductDTO> products,
    Function onSetDiscount,
  ) {
    return Column(
      children: [
        products.isEmpty
            ? Center(
                child: Text(
                  "لا يوجد منتجات!",
                  style: TextStyle(
                    color: context.newTextColor,
                    fontSize: 16,
                  ),
                ),
              )
            : ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.4,
                ),
                child: ListView.builder(
                  itemCount: products.length,
                  shrinkWrap: true,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return InvoiceDiscountDialogProductListItemsWidget(
                      id: products[index].id ?? 0,
                      products: products,
                      onSetDiscount: (int id, double discount) {
                        onSetDiscount(id, discount);
                      },
                    );
                  },
                ),
              ),
      ],
    );
  }
}

//--------------------------------------------------------------------------------------------
class InvoiceDiscountDialogProductListItemsWidget extends StatelessWidget {
  const InvoiceDiscountDialogProductListItemsWidget(
      {super.key,
      required this.id,
      required this.onSetDiscount,
      required this.products});

  final int id;
  final Function onSetDiscount;
  final List<ProductDTO> products;

  @override
  Widget build(BuildContext context) {
    var product = products.firstWhere(
      (element) => element.id == id,
    );
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 10),
          width: context.width - 40,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  "${product.title ?? ""} - ${product.code ?? ""}",
                  style: TextStyle(
                    color: context.newTextColor,
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(
                width: context.width / 3,
                child: CommonTextField(
                  initialValue: (product.discountValue ?? 0).toString(),
                  label: 'الخصم',
                  floatingLabelBehavior: FloatingLabelBehavior.never,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      try {
                        onSetDiscount(id, double.parse(value));
                      } catch (e) {
                        // Handle invalid number input
                        onSetDiscount(id, 0);
                      }
                    } else {
                      onSetDiscount(id, 0);
                    }
                  },
                ),
              ),
            ],
          ),
        ),
        Divider(
          color: Colors.grey.withOpacity(0.3),
          thickness: 1,
        ),
      ],
    );
  }
}

//--------------------------------------------------------------------------------------------
class InvoiceDiscountDialogWidget extends StatefulWidget {
  const InvoiceDiscountDialogWidget(
      {super.key, required this.invoice, required this.onSetInvoiceDiscount});
  final InvoiceDto invoice;
  final Function onSetInvoiceDiscount;

  @override
  State<InvoiceDiscountDialogWidget> createState() =>
      _InvoiceDiscountDialogWidgetState();
}

class _InvoiceDiscountDialogWidgetState
    extends State<InvoiceDiscountDialogWidget> {
  late TextEditingController valueController;
  late TextEditingController persantageController;

  @override
  void initState() {
    super.initState();
    valueController = TextEditingController(
        text: (widget.invoice.discountValue ?? 0).toString());
    persantageController = TextEditingController(
        text: (widget.invoice.discountPercentage ?? 0).toString());
  }

  @override
  void didUpdateWidget(InvoiceDiscountDialogWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.invoice != widget.invoice) {
      valueController.text = (widget.invoice.discountValue ?? 0).toString();
      persantageController.text =
          (widget.invoice.discountPercentage ?? 0).toString();
    }
  }

  @override
  void dispose() {
    valueController.dispose();
    persantageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildDiscountRow(
          context,
          "مبلغ الخصم",
          CommonTextField(
            controller: valueController,
            label: 'مبلغ ',
            keyboardType: TextInputType.number,
            onChanged: (value) {
              if (value.isNotEmpty) {
                try {
                  widget.onSetInvoiceDiscount(
                      InvoiceDiscountType.value, double.parse(value));
                } catch (e) {
                  // Handle invalid number input
                  widget.onSetInvoiceDiscount(InvoiceDiscountType.value, 0);
                }
              } else {
                widget.onSetInvoiceDiscount(InvoiceDiscountType.value, 0);
              }
            },
          ),
        ),
        _buildDiscountRow(
          context,
          "نسبة الخصم %",
          CommonTextField(
            controller: persantageController,
            label: 'نسبة % ',
            keyboardType: TextInputType.number,
            onChanged: (value) {
              if (value.isNotEmpty) {
                try {
                  widget.onSetInvoiceDiscount(
                      InvoiceDiscountType.persantage, double.parse(value));
                } catch (e) {
                  // Handle invalid number input
                  widget.onSetInvoiceDiscount(
                      InvoiceDiscountType.persantage, 0);
                }
              } else {
                widget.onSetInvoiceDiscount(InvoiceDiscountType.persantage, 0);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDiscountRow(BuildContext context, String label, Widget input) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: TextStyle(
                    color: context.newSecondaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(
                width: context.width / 2.5,
                child: input,
              ),
            ],
          ),
        ),
        Divider(
          color: Colors.grey.withOpacity(0.3),
          thickness: 1,
        ),
      ],
    );
  }
}
