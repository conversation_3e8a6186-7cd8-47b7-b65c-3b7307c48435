import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/continuous_barcode_scanner_dialog.dart';

import 'package:inventory_application/screens/inverntory/invoices/products/invoice_product_screen.dart';

class InvoiceSelectProductWidget extends StatefulWidget {
  const InvoiceSelectProductWidget(
      {super.key,
      required this.onChange,
      required this.onAddProduct,
      required this.onRemoveProduct,
      required this.selectedProducts,
      required this.onSearchByBarcode});
  final Function onChange;
  final Function onAddProduct;
  final Function onRemoveProduct;
  final Function onSearchByBarcode;
  final List<ProductDTO> selectedProducts;

  @override
  State<InvoiceSelectProductWidget> createState() =>
      _InvoiceSelectProductWidgetState();
}

class _InvoiceSelectProductWidgetState
    extends State<InvoiceSelectProductWidget> {
  bool _isScanning = false;
  bool _isLoading = false;
  String? _scanMessage;
  bool? _scanSuccess;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product action buttons
          Row(
            children: [
              // Add product button
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: Icons.add_circle_outline,
                  label: T("Add Product"),
                  color: context.newPrimaryColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => InvoiceProductScreen(
                          selectedProducts: widget.selectedProducts,
                          onAddProduct: (product) {
                            widget.onAddProduct(product);
                            widget.onChange();
                          },
                          onRemoveProduct: (id) {
                            widget.onRemoveProduct(id);
                            widget.onChange();
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              // Scan barcode button
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: Icons.qr_code_scanner,
                  label: _isLoading ? T("Scanning...") : T("Scan Barcode"),
                  color: _isLoading ? Colors.grey : context.newSecondaryColor,
                  onTap: _isLoading ? null : () => _scanBarcode(),
                  isLoading: _isLoading,
                ),
              ),
            ],
          ),

          if (_scanMessage != null)
            Container(
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: _scanSuccess == true
                    ? context.onPrimary.withOpacity(0.1)
                    : _scanSuccess == false
                        ? context.onPrimary.withOpacity(0.1)
                        : context.newInfoColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _scanSuccess == true
                      ? context.onPrimary
                      : _scanSuccess == false
                          ? context.onPrimary
                          : context.newInfoColor,
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Icon(
                      _scanSuccess == true
                          ? Icons.check_circle
                          : _scanSuccess == false
                              ? Icons.error
                              : Icons.info,
                      color: _scanSuccess == true
                          ? context.onPrimary
                          : _scanSuccess == false
                              ? context.onPrimary
                              : context.newInfoColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _scanMessage!,
                      style: TextStyle(
                        color: _scanSuccess == true
                            ? context.onPrimary
                            : _scanSuccess == false
                                ? context.onPrimary
                                : context.newInfoColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (_scanSuccess != null)
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _scanMessage = null;
                          _scanSuccess = null;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: Icon(
                          Icons.close,
                          color: Colors.grey.shade600,
                          size: 16,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onTap,
    bool isLoading = false,
  }) {
    return Material(
      color: color,
      elevation: 2,
      shadowColor: color.withOpacity(0.4),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (isLoading)
                SizedBox(
                  width: 18,
                  height: 18,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        const AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              else
                Icon(
                  icon,
                  color: Colors.white,
                  size: 18,
                ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _scanBarcode() async {
    try {
      setState(() {
        _isLoading = true;
        _scanMessage = T("Scanning barcode...");
        _scanSuccess = null;
      });

      // Show the continuous barcode scanner dialog
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return ContinuousBarcodeScannerDialog(
            onBarcodeDetected: (String barcode) async {
              // Process the barcode
              final result = await widget.onSearchByBarcode(barcode);

              // Call onChange to notify parent widget that products have changed
              if (result) {
                setState(() {
                  _scanMessage =
                      T("Product with barcode $barcode was added successfully");
                  _scanSuccess = true;
                });

                widget.onChange();

                // Force another UI refresh after a small delay
                Future.delayed(Duration.zero, () {
                  if (mounted) {
                    widget.onChange();
                  }
                });
              } else {
                setState(() {
                  _scanMessage = T("No product found with barcode $barcode");
                  _scanSuccess = false;
                });
              }

              return result;
            },
          );
        },
      );

      setState(() {
        _isLoading = false;
      });

      // Clear success message after a delay
      if (_scanSuccess == true) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _scanMessage = null;
              _scanSuccess = null;
            });
          }
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _scanMessage = T("Error scanning barcode: ") + e.toString();
        _scanSuccess = false;
      });

      // Clear error message after a delay
      Future.delayed(const Duration(seconds: 5), () {
        if (mounted) {
          setState(() {
            _scanMessage = null;
            _scanSuccess = null;
          });
        }
      });
    }
  }
}
