class LogInDto {
  int? id;
  String? usernameOrEmail;
  String? password;

  LogInDto({this.id, this.usernameOrEmail, this.password});

  LogInDto.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    usernameOrEmail = json['usernameOrEmail'];
    password = json['password'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['usernameOrEmail'] = usernameOrEmail;
    data['password'] = password;
    return data;
  }
}
