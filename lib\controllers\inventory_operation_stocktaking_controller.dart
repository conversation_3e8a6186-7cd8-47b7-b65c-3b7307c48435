import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';

import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';

import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';

import 'package:inventory_application/models/dto/response.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_order_dto.dart';

import 'package:inventory_application/models/model/inventory_operation_model.dart';

class InventoryOperationStocktakingController with ChangeNotifier {
  //---------------------------------------------------------------------------
  Future<String> getIncomingNumber() async {
    try {
      var number =
          await InventoryOperationCounterGenerator.getNextCounterByType(
              InventoryOperationType.Stocktaking);

      notifyListeners();
      return number;
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------
  //---------------------------------------------------------- -----------------
  Future<ResponseResultModel> saveInventoryStocktaking(
      {required InventoryOperationModel mode,
      required List<InventoryOperationItems> items}) async {
    try {
      var url = '/InventoryOperation/Manage?transactions_type=Stocktaking';
      final db = InventoryOperationController();

      mode.inventoryOperationItems = items;

      mode.operationType = InventoryOperationType.Stocktaking.index;
      mode.entryDateFormated =
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());
      if (mode.iD == null || (mode.iD ?? 0) == 0) {
        mode.entryDate = DateTime.now();
        mode.code = "*";
      }

      if (mode.aPPReferanceCode == null || mode.aPPReferanceCode == "") {
        mode.aPPReferanceCode = await getIncomingNumber();
      } else {
        if (await InventoryOperationCounterGenerator.checkIfCounterUsed(
            InventoryOperationType.Stocktaking, mode.aPPReferanceCode ?? "")) {
          mode.aPPReferanceCode = await getIncomingNumber();
        }
      }
      mode.iD ??= 0;
      // mapedInvoice.iD = 0;
      // mapedInvoice.vATPercent = 0;

      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateInventoryOperation(
            SqlLiteInvoiceModel(
                    data: jsonEncode(mode.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                    id: null,
                    localCode: mode.aPPReferanceCode,
                    type: InventoryOperationType.Stocktaking.name)
                .toJson());
        mode = InventoryOperationModel();
        items.clear();
        if (mode.iD == 0) {
          await InventoryOperationCounterGenerator.setCounterByTypeAuto(
              type: InventoryOperationType.Stocktaking);
        }
        notifyListeners();

        return ResponseResultModel(
            isSuccess: true, data: InvoiceResponseDto(localId: localId));
      }

      var result = await Api.post(
        action: url,
        body: mode.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          if (mode.iD == 0) {
            await InventoryOperationCounterGenerator
                .setInventoryOperationCounterInServer();
            await InventoryOperationCounterGenerator.setCounterByTypeAuto(
                type: InventoryOperationType.Stocktaking);
          }
          var response = InvoiceResponseDto.fromJson(result.data);
          mode.iD = response.id;
          mode.code = response.code;
          var localId = await db.insertOrUpdateInventoryOperation(
              SqlLiteInvoiceModel(
                      data: jsonEncode(mode.toJson()),
                      status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                      id: null,
                      localCode: mode.aPPReferanceCode,
                      type: InventoryOperationType.Stocktaking.name)
                  .toJson());
          response.localId = localId;
          await db.saveInventoryOperationToEcommerce(
              mode, TransactionTypes.StockTaking);
          mode = InventoryOperationModel();
          items.clear();
          // await db.saveReturnToEcommerce(mapedInvoice);
          notifyListeners();
          return ResponseResultModel(data: response, isSuccess: true);
        }
      }

      return ResponseResultModel(
          isSuccess: false, message: [result?.message ?? "Unknown error"]);
    } catch (e) {
      print("Error in saveInventoryIncoming: $e");
      return ResponseResultModel(isSuccess: false, message: [e.toString()]);
    }
  }
}
