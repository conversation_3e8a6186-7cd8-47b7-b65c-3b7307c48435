import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class FundSettlementScreen extends StatefulWidget {
  const FundSettlementScreen({super.key});

  @override
  State<FundSettlementScreen> createState() => _FundSettlementScreenState();
}

class _FundSettlementScreenState extends State<FundSettlementScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
