import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';

import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/invoices/invoice%20details/invoice_details_for_local_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/widgets/create_return_invoice_dialog_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/widgets/return_invoice_bottom_navbar_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:provider/provider.dart';

class CreateReturnInvoiceScreen extends StatefulWidget {
  const CreateReturnInvoiceScreen({super.key});

  @override
  State<CreateReturnInvoiceScreen> createState() =>
      _CreateReturnInvoiceScreenState();
}

class _CreateReturnInvoiceScreenState extends State<CreateReturnInvoiceScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).push(DialogRoute(
        context: context,
        builder: (context) => const CreateReturnInvoiceDialogWidget(),
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use Consumer to ensure we get the latest data from the controller
    var products = Provider.of<ReturnInvoiceController>(context)
        .selectedReturnInvoiceProduct;
    var provider = Provider.of<ReturnInvoiceController>(context, listen: false);
    return WillPopScope(
      onWillPop: () async {
        var result = await showConfirmDialog(
          title: T('Exit'),
          content: T("Are you sure?"),
          backText: T("back"),
          confirmText: T("exit"),
        );
        if (result == true) {
          if (provider.invoice.id != null) {
            provider.invoice = InvoiceDto();
            provider.selectedReturnInvoiceProduct = [];
          }
        }
        return result ?? false;
      },
      child: Scaffold(
        backgroundColor: context.newBackgroundColor,
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: 60,
          shadowColor: Colors.transparent,
          backgroundColor: Colors.white,
          automaticallyImplyLeading: false,
          title: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      InkWell(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: context.colors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.arrow_back,
                            color: context.colors.primary,
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        T("Return Invoice"),
                        style: TextStyle(
                          color: context.newTextColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      // _buildCompactButton(
                      //   label: T("Save & View"),
                      //   icon: Icons.visibility_outlined,
                      //   onPressed: () async {
                      //     pleaseWaitDialog(context: context, isShown: true);
                      //     var result = await provider.saveReturnInvoice();

                      //     if (result.isSuccess) {
                      //       var response = result.data;

                      //       // ignore: use_build_context_synchronously
                      //       pleaseWaitDialog(
                      //           context: context, isShown: false);
                      //       successSnackBar(
                      //           message: T("The operation has been saved"));

                      //       // ignore: use_build_context_synchronously
                      //       Navigator.of(context).push(
                      //         MaterialPageRoute(
                      //           builder: (context) =>
                      //               InoviceDetailsForLocalPage(
                      //             id: response.localId ?? 0,
                      //           ),
                      //         ),
                      //       );
                      //       return;
                      //     }

                      //     // ignore: use_build_context_synchronously
                      //     pleaseWaitDialog(
                      //         context: context, isShown: false);

                      //     errorSnackBar(
                      //       message: result.message != null
                      //           ? result.message!.first.toString()
                      //           : T("Not saved"),
                      //     );
                      //   },
                      // ),
                      const SizedBox(width: 8),
                      _buildCompactButton(
                        label: T("Save & New"),
                        icon: Icons.save_outlined,
                        isMain: true,
                        onPressed: () async {
                          pleaseWaitDialog(context: context, isShown: true);
                          var result = await provider.saveReturnInvoice();
                          if (result.isSuccess) {
                            // ignore: use_build_context_synchronously
                            pleaseWaitDialog(context: context, isShown: false);
                            successSnackBar(
                                message: T("The operation has been saved"));

                            // Reset the invoice and set default values
                            provider.selectedReturnInvoiceProduct.clear();
                            provider.invoice = InvoiceDto();

                            // Show dialog to create a new return invoice
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              Navigator.of(context).push(DialogRoute(
                                context: context,
                                builder: (context) =>
                                    const CreateReturnInvoiceDialogWidget(),
                              ));
                            });

                            return;
                          }
                          // ignore: use_build_context_synchronously
                          pleaseWaitDialog(context: context, isShown: false);
                          errorSnackBar(
                              message: result.message != null
                                  ? result.message!.first.toString()
                                  : T("Not saved"));
                        },
                      ),
                    ],
                  )
                ],
              ),
            ],
          ),
          toolbarOpacity: 1,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1.0),
            child: Container(
              color: Colors.grey.withOpacity(0.2),
              height: 1.0,
            ),
          ),
        ),
        bottomNavigationBar: const ReturnInvoiceBottomNavbarWidget(),
        body: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SingleChildScrollView(
            padding: EdgeInsets.zero,
            physics: const BouncingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InvoiceSelectProductWidget(
                  selectedProducts: products,
                  onChange: () {
                    // Force UI refresh when products change
                    setState(() {});
                  },
                  onAddProduct: (ProductDTO product) {
                    provider.addProductToSelectedList(product);
                    // Force UI refresh
                    setState(() {});
                  },
                  onRemoveProduct: (int id) {
                    provider.deleteProductFromSelectedList(id);
                    // Force UI refresh
                    setState(() {});
                  },
                  onSearchByBarcode: (String barcode) async {
                    // Get the controller
                    final returnInvoiceController =
                        Provider.of<ReturnInvoiceController>(
                      context,
                      listen: false,
                    );

                    // Process the barcode
                    var result = await returnInvoiceController.getItemByBarcode(
                      barcode: barcode,
                    );

                    // If result is a ProductDTO object rather than a boolean,
                    // it means we need to show the attribute selection dialog
                    if (result is ProductDTO) {
                      // Show attribute dialog for the product
                      await showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return _buildAttributeSelectionDialog(result);
                        },
                      );

                      // Return true to indicate success (dialog was shown)
                      return true;
                    }

                    // Force UI refresh immediately after barcode scan
                    setState(() {
                      // This will trigger a rebuild with the updated product list
                    });

                    // Add a small delay and refresh again to ensure UI updates
                    await Future.delayed(const Duration(milliseconds: 100));
                    setState(() {});

                    // Show error if product not found
                    if (result == false) {
                      errorSnackBar(
                        message: T(
                            "There is no product associated with the barcode"),
                        context: context,
                      );
                    }

                    return result;
                  },
                ),
                if (products.isNotEmpty) ...[
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.zero,
                    child: InvoiceProductListHeaderWidget(
                      backgroundColor: context.newPrimaryColor,
                      textColor: Colors.white,
                    ),
                  ),
                  Consumer<ReturnInvoiceController>(
                    builder: (context, returnInvoiceControllerUpdate, child) {
                      return ListView.separated(
                        itemCount: products.length,
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        separatorBuilder: (context, index) => Divider(
                          color: Colors.grey.withOpacity(0.2),
                          height: 1,
                          indent: 0,
                          endIndent: 0,
                        ),
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return InvoiceListItemsWidget(
                            id: products[index].id ?? 0,
                            barcode: products[index].barcode,
                            selectedInvoiceProduct:
                                returnInvoiceControllerUpdate
                                    .selectedReturnInvoiceProduct,
                            onChangeWarehouse: (int productId, int warehouseId,
                                String warehouseName,
                                [String? virtualProductId]) {
                              provider.setProductWarehouse(productId,
                                  warehouseId, warehouseName, virtualProductId);
                              setState(() {});
                            },
                            onDeleteProduct: (int productId,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.deleteProductFromSelectedList(
                                    productId, virtualProductId);
                              } else {
                                provider
                                    .deleteProductFromSelectedList(productId);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdatePrice: (int productId, double price,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.updateProductPrice(
                                    productId, price, virtualProductId);
                              } else {
                                provider.updateProductPrice(productId, price);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdateQuantity: (int productId, double quantity,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.updateProductQuantity(
                                    productId, quantity, virtualProductId);
                              } else {
                                provider.updateProductQuantity(
                                    productId, quantity);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdateUnit: (int productId, ItemPriceDTO unit,
                                [String? virtualProductId]) {
                              provider.updateProductUnit(
                                  productId, unit, virtualProductId);
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                          );
                        },
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    bool isMain = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isMain ? context.newPrimaryColor : Colors.white,
        foregroundColor: isMain ? Colors.white : context.newPrimaryColor,
        elevation: isMain ? 2 : 0,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isMain
                ? Colors.transparent
                : context.newPrimaryColor.withOpacity(0.5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: Text(
            T('Select Product Options'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: context.newPrimaryColor,
            ),
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.5,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.title ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Sort attributes by their order property
                  ...(() {
                    final sortedAttributes =
                        List<ItemAttribute>.from(product.itemAttributes ?? []);
                    sortedAttributes
                        .sort((a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
                    return sortedAttributes;
                  })()
                      .map((attribute) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          attribute.attributeName ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: context.newTextColor,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Wrap options in a grid
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: (attribute.itemsAttributeOptions ?? [])
                              .map((option) {
                            final isSelected =
                                selectedOptions[attribute.id] == option;

                            return InkWell(
                              onTap: () {
                                setState(() {
                                  if (attribute.id != null) {
                                    selectedOptions[attribute.id!] = option;
                                  }
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? context.newPrimaryColor
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: isSelected
                                        ? context.newPrimaryColor
                                        : Colors.grey.shade300,
                                  ),
                                ),
                                child: Text(
                                  option.optionName ?? '',
                                  style: TextStyle(
                                    color: isSelected
                                        ? Colors.white
                                        : context.newTextColor,
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  }),

                  // Preview of the selected options
                  if (selectedOptions.isNotEmpty) ...[
                    const Divider(),
                    const SizedBox(height: 8),
                    Text(
                      T('Selected Options:'),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getFormattedSelectedOptions(selectedOptions),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: context.newPrimaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                T('Cancel'),
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: context.newPrimaryColor,
                foregroundColor: Colors.white,
              ),
              onPressed: (product.itemAttributes != null &&
                      selectedOptions.length == product.itemAttributes!.length)
                  ? () {
                      // All attributes have been selected
                      Navigator.of(context).pop();
                      _addProductWithSelectedAttributes(
                          product, selectedOptions);
                    }
                  : null, // Disable button if not all attributes are selected
              child: Text(T('Add to Cart')),
            ),
          ],
        );
      },
    );
  }

  // Format the selected options as a string (e.g., "S/Red")
  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    if (selectedOptions.isEmpty) return '';

    // Sort by attribute ID (which should correspond to the attribute order)
    final sortedKeys = selectedOptions.keys.toList()..sort();

    // Join option names with '/'
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }

  // Add product to cart with selected attributes
  void _addProductWithSelectedAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Create a unique key for this product + attributes combination using option IDs
    String optionsHash = selectedOptions.entries
        .map((e) => '${e.value.optionId}-${e.key}')
        .join('_');

    String virtualProductId = '${product.id}_$optionsHash';

    // Get the ReturnInvoiceController
    final returnInvoiceController =
        Provider.of<ReturnInvoiceController>(context, listen: false);

    // Check if this product with the same attributes already exists in the cart
    int existingIndex = returnInvoiceController.selectedReturnInvoiceProduct
        .indexWhere((element) => element.virtualProductId == virtualProductId);

    // If the product already exists, just increase its quantity
    if (existingIndex >= 0) {
      // Create a new list to trigger UI update
      List<ProductDTO> updatedList =
          List.from(returnInvoiceController.selectedReturnInvoiceProduct);

      // Get the existing product
      var existingProduct = updatedList[existingIndex];
      double newQuantity =
          (existingProduct.quantity ?? 1) + (product.quantity ?? 1);

      // Update the quantity
      updatedList[existingIndex].quantity = newQuantity;

      // Update the total
      double price = updatedList[existingIndex].price ?? 0;
      updatedList[existingIndex].total = price * newQuantity;

      // Replace the list with the updated one
      returnInvoiceController.selectedReturnInvoiceProduct = updatedList;

      // Recalculate invoice total
      returnInvoiceController.calculateInvoiceTotal();

      // Notify listeners for UI update
      // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
      returnInvoiceController.notifyListeners();
      return;
    }

    // If the product doesn't exist, create a new one
    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: product.uniteId,
      uniteName: product.uniteName,
      category: product.category,
      quantity: product.quantity ?? 1.0, // Ensure a default quantity
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      itemAttributes: product.itemAttributes,
      virtualProductId: virtualProductId,
      hasSelectedAttributes: true,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - ${optionsString}';

      // Store selected options in the description field for reference
      // This way we can retrieve the information later if needed
      String attributeDetails = selectedOptions.entries
          .map((entry) =>
              '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
          .join(', ');

      if (productCopy.description == null || productCopy.description!.isEmpty) {
        productCopy.description = attributeDetails;
      } else {
        productCopy.description =
            '${productCopy.description}\n[${attributeDetails}]';
      }
    }

    // Add to invoice
    returnInvoiceController.addProductToSelectedList(productCopy);
  }
}
