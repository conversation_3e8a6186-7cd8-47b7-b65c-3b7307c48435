import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/base_report_screen.dart';
import 'package:inventory_application/screens/reports/widgets/daily_sales_report_widget.dart';
import 'package:provider/provider.dart';

class DailySalesReportScreen extends BaseReportScreen {
  const DailySalesReportScreen({Key? key}) : super(key: key);

  @override
  State<DailySalesReportScreen> createState() => _DailySalesReportScreenState();
}

class _DailySalesReportScreenState extends BaseReportScreenState<DailySalesReportScreen> {
  @override
  ReportType get reportType => ReportType.dailySales;

  @override
  String getReportTitle() => T('Daily Sales');

  @override
  IconData getReportIcon() => Icons.calendar_today;

  @override
  Color getHeaderColor() => const Color(0xFF4361EE);

  @override
  Color getAccentColor() => const Color(0xFF3A0CA3);

  @override
  Future<void> generateReport() async {
    setLoading(true);

    try {
      final reportController = Provider.of<ReportController>(context, listen: false);
      await reportController.generateDailySalesReport();
    } catch (e) {
      errorSnackBar(message: 'Error generating daily sales report: $e');
    } finally {
      setLoading(false);
    }
  }

  @override
  Widget buildReportContent(ReportController reportController) {
    final report = reportController.dailySalesReport;
    if (report == null) {
      return Center(
        child: Text(T('No daily report data available')),
      );
    }
    return DailySalesReportWidget(report: report);
  }
}
