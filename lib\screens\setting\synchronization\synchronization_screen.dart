import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class SynchronizationScreen extends StatefulWidget {
  const SynchronizationScreen({super.key});

  @override
  State<SynchronizationScreen> createState() => _SynchronizationScreenState();
}

class _SynchronizationScreenState extends State<SynchronizationScreen> {
  int products = 0;
  int customers = 0;
  int categories = 0;
  int warehouses = 0;
  int units = 0;
  int salesmen = 0;
  bool isSyncing = false;

  Future<void> _syncWithServer() async {
    if (isSyncing) {
      return;
    }
    try {
      setState(() {
        isSyncing = true;
      });

      // Store context in a local variable to avoid using it across async gaps
      final context = this.context;

      await Provider.of<CustomerController>(context, listen: false)
          .clearAndRefetchData();
      await Provider.of<WarehouseController>(context, listen: false)
          .clearAndRefetchData();
      await Provider.of<CategoryController>(context, listen: false)
          .clearAndRefetchData();
      await Provider.of<ProductController>(context, listen: false)
          .clearAndRefetchData();
      await Provider.of<UnitController>(context, listen: false)
          .clearAndRefetchData();
      await Provider.of<SalesmenController>(context, listen: false)
          .clearAndRefetchData();

      if (mounted) {
        successSnackBar(message: T("All data synchronized successfully"));
      }
    } catch (error) {
      if (mounted) {
        errorSnackBar(message: T("Error synchronizing data"));
      }
    } finally {
      if (mounted) {
        setState(() {
          isSyncing = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      fetchAndSetProductCount();
      fetchAndSetWarehousesCount();
      fetchAndSetCategoryCount();
      fetchAndSetCustomerCount();
      fetchAndSetUnitCount();
      fetchAndSetSalesmenCount();
    });
  }

  Future<void> fetchAndSetProductCount() async {
    if (isSyncing) {
      errorSnackBar(message: T("Product synchronization already in progress"));
      return;
    }
    try {
      setState(() {
        isSyncing = true;
      });

      products = await Provider.of<ProductController>(context, listen: false)
          .getProductCount();

      setState(() {
        products = products;
      });
    } catch (error) {
      errorSnackBar(message: T("Error synchronizing products"));
    }
  }

  Future<void> fetchAndSetWarehousesCount() async {
    try {
      warehouses =
          await Provider.of<WarehouseController>(context, listen: false)
              .getWarehouseCount();

      setState(() {
        warehouses = warehouses;
      });
    } catch (error) {
      errorSnackBar(message: T("Error synchronizing warehouses"));
    }
  }

  Future<void> fetchAndSetCategoryCount() async {
    try {
      categories = await Provider.of<CategoryController>(context, listen: false)
          .getCategoryCount();

      setState(() {
        categories = categories;
      });
    } catch (error) {
      errorSnackBar(message: T("Error synchronizing categories"));
    }
  }

  Future<void> fetchAndSetCustomerCount() async {
    try {
      customers = await Provider.of<CustomerController>(context, listen: false)
          .getCustomerCount();

      setState(() {
        customers = customers;
      });
    } catch (error) {
      errorSnackBar(message: T("Error synchronizing customers"));
    } finally {
      setState(() {
        isSyncing = false;
      });
    }
  }

  Future<void> fetchAndSetUnitCount() async {
    try {
      units = await Provider.of<UnitController>(context, listen: false)
          .getUnitCount();

      setState(() {
        units = units;
      });
    } catch (error) {
      errorSnackBar(message: T("Error synchronizing units"));
    }
  }

  Future<void> fetchAndSetSalesmenCount() async {
    try {
      salesmen = await Provider.of<SalesmenController>(context, listen: false)
          .getSalesmenCount();

      setState(() {
        salesmen = salesmen;
      });
    } catch (error) {
      errorSnackBar(message: T("Error synchronizing salesmen"));
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get current counts from providers
    products = Provider.of<ProductController>(context).fetchedProductCount == 0
        ? products
        : Provider.of<ProductController>(context).fetchedProductCount;
    customers =
        Provider.of<CustomerController>(context).fetchedCustomersCount == 0
            ? customers
            : Provider.of<CustomerController>(context).fetchedCustomersCount;
    categories =
        Provider.of<CategoryController>(context).fetchedCategoryCount == 0
            ? categories
            : Provider.of<CategoryController>(context).fetchedCategoryCount;
    warehouses =
        Provider.of<WarehouseController>(context).fetchedWarehouseCount == 0
            ? warehouses
            : Provider.of<WarehouseController>(context).fetchedWarehouseCount;
    units = Provider.of<UnitController>(context).fetchedUnitCount == 0
        ? units
        : Provider.of<UnitController>(context).fetchedUnitCount;
    salesmen =
        Provider.of<SalesmenController>(context).fetchedSalesmenCount == 0
            ? salesmen
            : Provider.of<SalesmenController>(context).fetchedSalesmenCount;

    return ApplicationLayout(
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildSyncAllButton(),
                    const SizedBox(height: 20),
                    _buildSyncStatusCard(),
                    const SizedBox(height: 24),
                    _buildSyncItem(
                      title: "Customers",
                      count: customers,
                      icon: Icons.people,
                      onSync: () async {
                        await _syncSingleItem(
                          title: "Customers",
                          action: () async =>
                              await Provider.of<CustomerController>(context,
                                      listen: false)
                                  .clearAndRefetchData(),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    _buildSyncItem(
                      title: "Products",
                      count: products,
                      icon: Icons.inventory,
                      onSync: () async {
                        await _syncSingleItem(
                          title: "Products",
                          action: () async =>
                              await Provider.of<ProductController>(context,
                                      listen: false)
                                  .clearAndRefetchData(),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    _buildSyncItem(
                      title: "Categories",
                      count: categories,
                      icon: Icons.category,
                      onSync: () async {
                        await _syncSingleItem(
                          title: "Categories",
                          action: () async =>
                              await Provider.of<CategoryController>(context,
                                      listen: false)
                                  .clearAndRefetchData(),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    _buildSyncItem(
                      title: "Warehouses",
                      count: warehouses,
                      icon: Icons.warehouse,
                      onSync: () async {
                        await _syncSingleItem(
                          title: "Warehouses",
                          action: () async =>
                              await Provider.of<WarehouseController>(context,
                                      listen: false)
                                  .clearAndRefetchData(),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    _buildSyncItem(
                      title: "Units",
                      count: units,
                      icon: Icons.straighten,
                      onSync: () async {
                        await _syncSingleItem(
                          title: "Units",
                          action: () async => await Provider.of<UnitController>(
                                  context,
                                  listen: false)
                              .clearAndRefetchData(),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    _buildSyncItem(
                      title: "Salesmen",
                      count: salesmen,
                      icon: Icons.people_alt,
                      onSync: () async {
                        await _syncSingleItem(
                          title: "Salesmen",
                          action: () async =>
                              await Provider.of<SalesmenController>(context,
                                      listen: false)
                                  .clearAndRefetchData(),
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: context.newPrimaryColor,
        boxShadow: [
          BoxShadow(
            color: context.newPrimaryColor.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 20,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.sync,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  T("Synchronization"),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 48),
              child: Text(
                T("Keep your data in sync with the server"),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncAllButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            if (isSyncing) {
              errorSnackBar(message: T("Sync already in progress"));
              return;
            }

            if (await isThereNetworkConnection() == false) {
              errorSnackBar(message: T("Internet connection required"));
              return;
            }

            var result = await showConfirmDialog(
              title: T("Sync with Server"),
              confirmText: T("Continue"),
              backText: T("Cancel"),
              content: T(
                  "All data will be synchronized with the server. Make sure you have an internet connection. Do you want to continue?"),
            );
            if (result == true) {
              await _syncWithServer();
            }
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: context.newPrimaryColor.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.sync,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T("Sync All Data"),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.newTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        T("Update all information from the server at once"),
                        style: TextStyle(
                          fontSize: 14,
                          color: context.newTextColor.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: context.newTextColor.withOpacity(0.5),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSyncStatusCard() {
    final int totalItems =
        products + customers + categories + warehouses + units + salesmen;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.newSecondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.newSecondaryColor.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: context.newSecondaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                T("Sync Status"),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: context.newSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatItem(
                title: T("Total Items"),
                value: totalItems.toString(),
                icon: Icons.inventory_2_outlined,
              ),
              _buildStatItem(
                title: T("Last Sync"),
                value: T("Now"),
                icon: Icons.update,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.newSecondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 16,
            color: context.newSecondaryColor,
          ),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: context.newTextColor.withOpacity(0.7),
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: context.newTextColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSyncItem({
    required String title,
    required int count,
    required IconData icon,
    required VoidCallback onSync,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: context.newPrimaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        icon,
                        color: context.newPrimaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          T(title),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: context.newTextColor,
                          ),
                        ),
                        Text(
                          "${T("Count")}: $count",
                          style: TextStyle(
                            fontSize: 14,
                            color: context.newTextColor.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.withOpacity(0.1),
                      ),
                      child: Center(
                        child: Text(
                          count.toString(),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.newTextColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Material(
              color: context.newPrimaryColor,
              child: InkWell(
                onTap: onSync,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.sync,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        T("Sync"),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _syncSingleItem({
    required String title,
    required Future<void> Function() action,
  }) async {
    if (isSyncing) {
      errorSnackBar(message: T("Sync already in progress"));
      return;
    }

    if (await isThereNetworkConnection() == false) {
      errorSnackBar(message: T("Internet connection required"));
      return;
    }

    try {
      setState(() {
        isSyncing = true;
      });

      await action();

      successSnackBar(
          message: "${T(title)} ${T("All data synchronized successfully")}");
    } catch (error) {
      print('Error syncing $title: $error');
      errorSnackBar(message: "${T("Error synchronizing data")} ${T(title)}");
    } finally {
      setState(() {
        isSyncing = false;
      });
    }
  }
}
