import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_operation_incoming_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/inverntory/InventoryOperation/inventory operation incoming/widgets/inventory_operation_incoming_base_info_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class InventoryOperationIncomingScreen extends StatefulWidget {
  const InventoryOperationIncomingScreen({super.key});

  @override
  State<InventoryOperationIncomingScreen> createState() =>
      _InventoryOperationIncomingScreenState();
}

class _InventoryOperationIncomingScreenState
    extends State<InventoryOperationIncomingScreen>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;
  bool _isCollapsed = false;
  double _lastScrollOffset = 0;

  @override
  void initState() {
    super.initState();

    // Initialize scroll controller
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Initialize height animation
    _heightAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<InventoryOperationIncomingController>(context, listen: false)
          .getIncomingNumber();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final currentOffset = _scrollController.offset;
    final scrollDelta = currentOffset - _lastScrollOffset;

    // Only trigger animation if scroll delta is significant (more than 5 pixels)
    if (scrollDelta.abs() > 5) {
      if (scrollDelta > 0 && !_isCollapsed) {
        // Scrolling down - collapse
        setState(() {
          _isCollapsed = true;
        });
        _animationController.forward();
      } else if (scrollDelta < 0 && _isCollapsed) {
        // Scrolling up - expand
        setState(() {
          _isCollapsed = false;
        });
        _animationController.reverse();
      }
    }

    _lastScrollOffset = currentOffset;
  }

  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<InventoryOperationIncomingController>(context);

    return ApplicationLayout(
      child: Scaffold(
        body: Column(
          children: [
            CommonHeader(
              icon: Icons.input,
              title: T("Incoming Operation"),
            ),

            // Collapsible Base Info Section
            AnimatedBuilder(
              animation: _heightAnimation,
              builder: (context, child) {
                return ClipRect(
                  child: Align(
                    alignment: Alignment.topCenter,
                    heightFactor: _heightAnimation.value,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: const InventoryOperationIncomingBaseInfoWidget(),
                    ),
                  ),
                );
              },
            ),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                    ),
                    child: Column(
                      children: [
                        // Product Selection Widget
                        InvoiceSelectProductWidget(
                          selectedProducts: provider.selectedIncomingProduct,
                          onChange: () {
                            // Force UI refresh when products change
                            setState(() {});
                          },
                          onAddProduct: (ProductDTO product) {
                            provider.addProductToSelectedList(product);
                            // Force UI refresh
                            setState(() {});
                          },
                          onRemoveProduct: (int id) {
                            provider.deleteProductFromSelectedList(id);
                            // Force UI refresh
                            setState(() {});
                          },
                          onSearchByBarcode: (String barcode) async {
                            // Get the controller
                            final inventoryIncomingController = Provider.of<
                                InventoryOperationIncomingController>(
                              context,
                              listen: false,
                            );

                            // Process the barcode
                            var result = await inventoryIncomingController
                                .getItemByBarcode(
                              barcode: barcode,
                            );

                            // If result is a ProductDTO object rather than a boolean,
                            // it means we need to show the attribute selection dialog
                            if (result is ProductDTO) {
                              // Show attribute dialog for the product
                              await showDialog(
                                // ignore: use_build_context_synchronously
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return _buildAttributeSelectionDialog(result);
                                },
                              );

                              // Return true to indicate success (dialog was shown)
                              return true;
                            }

                            // Force UI refresh immediately after barcode scan
                            setState(() {
                              // This will trigger a rebuild with the updated product list
                            });

                            // Add a small delay and refresh again to ensure UI updates
                            await Future.delayed(
                                const Duration(milliseconds: 100));
                            setState(() {});

                            // Show error if product not found
                            if (result == false) {
                              errorSnackBar(
                                message: T(
                                    "There is no product associated with the barcode"),
                                // ignore: use_build_context_synchronously
                                context: context,
                              );
                            }

                            return result;
                          },
                        ),

                        // Products List
                        if (provider.selectedIncomingProduct.isNotEmpty) ...[
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.list_alt,
                                      color: Theme.of(context).primaryColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      T('Selected Products'),
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount:
                                      provider.selectedIncomingProduct.length,
                                  separatorBuilder: (context, index) =>
                                      const SizedBox(height: 12),
                                  itemBuilder: (context, index) {
                                    final product =
                                        provider.selectedIncomingProduct[index];
                                    return InvoiceListItemsWidget(
                                      id: product.id ?? 0,
                                      barcode: product.barcode,
                                      virtualProductId:
                                          product.virtualProductId,
                                      selectedInvoiceProduct:
                                          provider.selectedIncomingProduct,
                                      onChangeWarehouse: (int productId,
                                          int warehouseId, String warehouseName,
                                          [String? virtualProductId]) {
                                        provider.setProductWarehouse(
                                            productId,
                                            warehouseId,
                                            warehouseName,
                                            virtualProductId);
                                        setState(() {});
                                      },
                                      onDeleteProduct: (int productId,
                                          [String? virtualProductId]) {
                                        if (virtualProductId != null) {
                                          provider
                                              .deleteProductFromSelectedList(
                                                  productId,
                                                  virtualProductId:
                                                      virtualProductId);
                                        } else {
                                          provider
                                              .deleteProductFromSelectedList(
                                                  productId);
                                        }
                                        provider.calculateInvoiceTotal();
                                        setState(() {});
                                      },
                                      onUpdatePrice:
                                          (int productId, double price,
                                              [String? virtualProductId]) {
                                        if (virtualProductId != null) {
                                          provider.updateProductPrice(productId,
                                              price, virtualProductId);
                                        } else {
                                          provider.updateProductPrice(
                                              productId, price);
                                        }
                                        provider.calculateInvoiceTotal();
                                        setState(() {});
                                      },
                                      onUpdateQuantity:
                                          (int productId, double quantity,
                                              [String? virtualProductId]) {
                                        if (virtualProductId != null) {
                                          provider.updateProductQuantity(
                                              productId,
                                              quantity,
                                              virtualProductId);
                                        } else {
                                          provider.updateProductQuantity(
                                              productId, quantity);
                                        }
                                        provider.calculateInvoiceTotal();
                                        setState(() {});
                                      },
                                      onUpdateUnit:
                                          (int productId, ItemPriceDTO unit,
                                              [String? virtualProductId]) {
                                        provider.updateProductUnit(
                                            productId, unit, virtualProductId);
                                        provider.calculateInvoiceTotal();
                                        setState(() {});
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ] else ...[
                          // Empty State
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(40),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.grey.shade200,
                                width: 1,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.inventory_2_outlined,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  T('No products added yet'),
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  T('Add products to start the incoming operation'),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Summary Section
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: _buildModernBottomBar(context, provider),
        floatingActionButton: _buildToggleButton(),
        floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
      ),
    );
  }

  Widget _buildToggleButton() {
    return Container(
      margin: const EdgeInsets.only(top: 80),
      child: FloatingActionButton.small(
        onPressed: () {
          if (_isCollapsed) {
            setState(() {
              _isCollapsed = false;
            });
            _animationController.reverse();
          } else {
            setState(() {
              _isCollapsed = true;
            });
            _animationController.forward();
          }
        },
        backgroundColor: Theme.of(context).primaryColor.withOpacity(0.9),
        foregroundColor: Colors.white,
        elevation: 4,
        child: AnimatedRotation(
          turns: _isCollapsed ? 0.5 : 0,
          duration: const Duration(milliseconds: 300),
          child: const Icon(
            Icons.keyboard_arrow_up,
            size: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(T('Select Product Options')),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              product.title ?? T('Unknown Product'),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            // Add attribute selection widgets here
            // This would be similar to the POS screen implementation
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(T('Cancel')),
        ),
        ElevatedButton(
          onPressed: () {
            // Add the product with selected attributes
            Provider.of<InventoryOperationIncomingController>(context,
                    listen: false)
                .addProductToSelectedList(product);
            Navigator.of(context).pop();
          },
          child: Text(T('Add to Cart')),
        ),
      ],
    );
  }

  Widget _buildModernBottomBar(
      BuildContext context, InventoryOperationIncomingController provider) {
    final products = provider.selectedIncomingProduct;
    final totalItems = products.length;
    final totalValue = provider.inventoryIncoming.total ?? 0;
    final hasItems = products.isNotEmpty;
    final canSave = hasItems && provider.inventoryIncoming.toStoreID != null;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Summary Row
              if (hasItems) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor.withOpacity(0.1),
                        Theme.of(context).primaryColor.withOpacity(0.05),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Items Count
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.inventory_2,
                          color: Theme.of(context).primaryColor,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              T('Items to Receive'),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '$totalItems ${T("items")}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Total Value
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.attach_money,
                          color: Colors.green,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            T('Total Value'),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            totalValue.toStringAsFixed(2),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Action Buttons Row
              Row(
                children: [
                  // Cancel/Clear Button
                  if (hasItems) ...[
                    Expanded(
                      flex: 1,
                      child: Container(
                        height: 50,
                        margin: const EdgeInsets.only(right: 12),
                        child: OutlinedButton.icon(
                          onPressed: () {
                            _showClearConfirmationDialog(context, provider);
                          },
                          style: OutlinedButton.styleFrom(
                            side:
                                BorderSide(color: Colors.red.withOpacity(0.5)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          icon: const Icon(
                            Icons.clear_all,
                            color: Colors.red,
                            size: 18,
                          ),
                          label: Text(
                            T('Clear'),
                            style: const TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],

                  // Save Button
                  Expanded(
                    flex: hasItems ? 2 : 1,
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: canSave
                            ? LinearGradient(
                                colors: [
                                  Theme.of(context).primaryColor,
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.8),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              )
                            : null,
                        color: canSave ? null : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: canSave
                            ? [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ]
                            : null,
                      ),
                      child: ElevatedButton.icon(
                        onPressed: canSave
                            ? () {
                                _saveIncomingOperation(context, provider);
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          shadowColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        icon: Icon(
                          canSave ? Icons.save : Icons.save_outlined,
                          size: 20,
                        ),
                        label: Text(
                          T('Save Incoming'),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color:
                                canSave ? Colors.white : Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // Status Indicator
              if (!canSave && hasItems) ...[
                const SizedBox(height: 12),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.warning_amber,
                        color: Colors.orange,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        T('Please select destination warehouse'),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showClearConfirmationDialog(
      BuildContext context, InventoryOperationIncomingController provider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                T('Clear All Items'),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          content: Text(
            T('Are you sure you want to clear all items from the incoming list?'),
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                T('Cancel'),
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                provider.selectedIncomingProduct.clear();
                provider.calculateInvoiceTotal();
                Navigator.of(context).pop();
                setState(() {});
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(T('Clear All')),
            ),
          ],
        );
      },
    );
  }

  void _saveIncomingOperation(BuildContext context,
      InventoryOperationIncomingController provider) async {
    try {
      final result = await provider.saveInventoryIncoming();

      if (mounted) {
        if (result.isSuccess) {
          successSnackBar(message: T('Incoming operation saved successfully!'));
          Navigator.of(context).pop();
        } else {
          errorSnackBar(
              message: result.message?.first ??
                  T('Failed to save incoming operation'));
        }
      }
    } catch (e) {
      if (mounted) {
        errorSnackBar(message: T('Error saving incoming operation: $e'));
      }
    }
  }
}
