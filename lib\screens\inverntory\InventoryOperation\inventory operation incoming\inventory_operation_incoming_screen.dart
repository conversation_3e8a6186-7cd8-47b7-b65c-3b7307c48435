import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_operation_incoming_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20incoming/widgets/inventory_operation_incoming_base_info_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_operation_no_products_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_opertaion_bottom_bar_widget.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';

import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class InventoryOperationIncomingScreen extends StatefulWidget {
  const InventoryOperationIncomingScreen({super.key});

  @override
  State<InventoryOperationIncomingScreen> createState() =>
      _InventoryOperationIncomingScreenState();
}

class _InventoryOperationIncomingScreenState
    extends State<InventoryOperationIncomingScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<InventoryOperationIncomingController>(context, listen: false)
          .getIncomingNumber();
    });
  }

  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<InventoryOperationIncomingController>(context);

    return ApplicationLayout(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              CommonHeader(
                icon: Icons.input,
                title: T("Incoming Operation"),
              ),

              // Base Info Section
              const InventoryOperationIncomingBaseInfoWidget(),

              // Content Section
              Column(
                children: [
                  // Product Selection Widget
                  InvoiceSelectProductWidget(
                    selectedProducts: provider.selectedIncomingProduct,
                    onChange: () {
                      // Force UI refresh when products change
                      setState(() {});
                    },
                    onAddProduct: (ProductDTO product) {
                      provider.addProductToSelectedList(product);
                      // Force UI refresh
                      setState(() {});
                    },
                    onRemoveProduct: (int id) {
                      provider.deleteProductFromSelectedList(id);
                      // Force UI refresh
                      setState(() {});
                    },
                    onSearchByBarcode: (String barcode) async {
                      // Get the controller
                      final inventoryIncomingController =
                          Provider.of<InventoryOperationIncomingController>(
                        context,
                        listen: false,
                      );

                      // Process the barcode
                      var result =
                          await inventoryIncomingController.getItemByBarcode(
                        barcode: barcode,
                      );

                      // If result is a ProductDTO object rather than a boolean,
                      // it means we need to show the attribute selection dialog
                      if (result is ProductDTO) {
                        // Show attribute dialog for the product
                        await showDialog(
                          // ignore: use_build_context_synchronously
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return _buildAttributeSelectionDialog(result);
                          },
                        );

                        // Return true to indicate success (dialog was shown)
                        return true;
                      }

                      // Force UI refresh immediately after barcode scan
                      setState(() {
                        // This will trigger a rebuild with the updated product list
                      });

                      // Add a small delay and refresh again to ensure UI updates
                      await Future.delayed(const Duration(milliseconds: 100));
                      setState(() {});

                      // Show error if product not found
                      if (result == false) {
                        errorSnackBar(
                          message: T(
                              "There is no product associated with the barcode"),
                          // ignore: use_build_context_synchronously
                          context: context,
                        );
                      }

                      return result;
                    },
                  ),

                  // Products List
                  if (provider.selectedIncomingProduct.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.zero,
                      child: InvoiceProductListHeaderWidget(
                        backgroundColor: context.primaryColor,
                        textColor: Colors.white,
                      ),
                    ),
                    Consumer<InventoryOperationIncomingController>(
                      builder: (context, inventoryOpertationOutgoingController,
                          child) {
                        return ListView.separated(
                          itemCount: provider.selectedIncomingProduct.length,
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          separatorBuilder: (context, index) => Divider(
                            color: Colors.grey.withOpacity(0.2),
                            height: 1,
                            indent: 0,
                            endIndent: 0,
                          ),
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return InvoiceListItemsWidget(
                              id: provider.selectedIncomingProduct[index].id ??
                                  0,
                              barcode: provider
                                  .selectedIncomingProduct[index].barcode,
                              virtualProductId: provider
                                  .selectedIncomingProduct[index]
                                  .virtualProductId,
                              selectedInvoiceProduct:
                                  inventoryOpertationOutgoingController
                                      .selectedIncomingProduct,
                              onChangeWarehouse: (int productId,
                                  int warehouseId, String warehouseName,
                                  [String? virtualProductId]) {
                                errorSnackBar(
                                    message: "لا يمكن تغير المستودع هنا");
                              },
                              onDeleteProduct: (int productId,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  // If we have a virtual ID, use it when deleting
                                  provider.deleteProductFromSelectedList(
                                      productId,
                                      virtualProductId: virtualProductId);
                                } else {
                                  // Otherwise use the regular product ID
                                  provider
                                      .deleteProductFromSelectedList(productId);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdatePrice: (int productId, double price,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  provider.updateProductPrice(
                                      productId, price, virtualProductId);
                                } else {
                                  provider.updateProductPrice(productId, price);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdateQuantity: (int productId, double quantity,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  provider.updateProductQuantity(
                                      productId, quantity, virtualProductId);
                                } else {
                                  provider.updateProductQuantity(
                                      productId, quantity);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdateUnit: (int productId, ItemPriceDTO unit,
                                  [String? virtualProductId]) {
                                provider.updateProductUnit(
                                    productId, unit, virtualProductId);
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                            );
                          },
                        );
                      },
                    ),
                  ] else ...[
                    // Empty State
                    const InventoryOperationNoProductsWidget(),
                  ],

                  // Summary Section
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: InventoryOpertaionBottomBarWidget(
            model: provider.inventoryIncoming,
            products: provider.selectedIncomingProduct,
            onclear: () {
              provider.selectedIncomingProduct.clear();
              provider.calculateInvoiceTotal();
            },
            onSave: () async {
              if (provider.inventoryIncoming.fromStoreID == null) {
                errorSnackBar(message: "يرجى اختيار المستودع ");
                return;
              }

              pleaseWaitDialog(context: context, isShown: true);

              var result = await provider.saveInventoryIncoming();
              if (result.isSuccess) {
                // ignore: use_build_context_synchronously
                pleaseWaitDialog(context: context, isShown: false);
                successSnackBar(message: T("The operation has been saved"));

                // Reset the invoice and set default values

                provider.selectedIncomingProduct.clear();
                provider.inventoryIncoming = InventoryOperationModel();
                // _setDefaultValues();
                setState(() {});
                return;
              }
              // ignore: use_build_context_synchronously
              pleaseWaitDialog(context: context, isShown: false);
              errorSnackBar(
                  message: result.message != null
                      ? result.message!.first.toString()
                      : T("Not saved"));
            }),
      ),
    );
  }

  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(T('Select Product Options')),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              product.title ?? T('Unknown Product'),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            // Add attribute selection widgets here
            // This would be similar to the POS screen implementation
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(T('Cancel')),
        ),
        ElevatedButton(
          onPressed: () {
            // Add the product with selected attributes
            Provider.of<InventoryOperationIncomingController>(context,
                    listen: false)
                .addProductToSelectedList(product);
            Navigator.of(context).pop();
          },
          child: Text(T('Add to Cart')),
        ),
      ],
    );
  }
}
