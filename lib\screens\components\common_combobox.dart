import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/theme/app_theme_light.dart';

class ComboBoxDataModel {
  int id;
  String name;

  // Constructor for creating an instance from JSON
  ComboBoxDataModel.fromJson(Map<String, dynamic> json)
      : id = json["id"],
        name = json["name"];

  // Regular constructor
  ComboBoxDataModel(this.id, this.name);

  // Method to convert instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

//****************************************************************************************************** */

class MyComboBox extends StatelessWidget {
  final String caption;
  final String labelText;
  final int? selectedValue;
  final String modalTitle;
  // final Future future;
  final List<ComboBoxDataModel> data;
  final Function(int id, String name) onSelect;
  final bool isSmallLookup;
  final double? width;
  final double? fontSize;
  final Color? backColor;
  final Color? fontColor;
  final double? height;
  final double? borderRadius;
  final bool? isShowLabel;
  final double? borderWidth;
  final Function? onRefresh;

  const MyComboBox({
    Key? key,
    required this.caption,
    // required this.future,
    required this.onSelect,
    required this.modalTitle,
    this.isSmallLookup = false,
    this.width,
    this.backColor,
    this.fontColor,
    this.fontSize = 15,
    this.borderRadius = 11,
    this.height,
    this.selectedValue,
    required this.data,
    required this.labelText,
    this.isShowLabel,
    this.borderWidth,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      //AppController.mq.size.width / 2 - 50,
      // padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
      child: InkWell(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            isShowLabel == null && isShowLabel != true
                ? Text(
                    labelText,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : const SizedBox.shrink(),
            Container(
              alignment: Alignment.center,
              width: width ?? context.width,
              height: height ?? 65,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: AppThemeLight.instance.theme.colorScheme.primary,
                    width: 1.2,
                  )),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: Center(
                      child: Text(
                        caption,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: fontSize,
                            fontWeight: FontWeight.bold,
                            color: fontColor ?? context.primaryColor),
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: fontColor ?? context.primaryColor,
                    size: isSmallLookup ? 18 : 20,
                  ),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          showModalBottomSheet<void>(
            shape: const RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(25.0))),
            isScrollControlled: true,
            backgroundColor: context.backgroundColor,
            context: context,
            builder: (_) {
              return ComboBoxModal(
                // future: future,
                data: data,
                selectedValue: selectedValue,
                title: modalTitle,
                onSelect: onSelect,
                onRefresh: () {
                  print("asdkljaslkdjaslkd");
                  onRefresh!();
                },
              );
            },
          );
        },
      ),
    );
  }
}

//****************************************************************************************************** */

class ComboBoxModal extends StatefulWidget {
  const ComboBoxModal(
      {Key? key,
      this.future,
      required this.title,
      required this.onSelect,
      this.selectedValue,
      this.isFilter,
      this.onRefresh,
      this.data})
      : super(key: key);
  final Future? future;
  final String title;
  final int? selectedValue;
  final Function onSelect;
  final Function? onRefresh;
  final bool? isFilter;

  final List<ComboBoxDataModel>? data;
  @override
  // ignore: library_private_types_in_public_api
  _ComboBoxModalState createState() => _ComboBoxModalState();
}

class _ComboBoxModalState extends State<ComboBoxModal> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 8,
        bottom: context.mediaQuery.viewInsets.bottom,
      ),
      height: context.mediaQuery.viewInsets.bottom + (context.height / 2.8),
      child: Column(
        children: [
          SizedBox(
            height: 30,
            child: Stack(
              alignment: Alignment.centerRight,
              children: [
                Center(
                  child: Text(
                    widget.title,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 18, color: context.colors.scrim),
                  ),
                ),
                IconButton(
                    alignment: Alignment.centerRight,
                    onPressed: () => {Navigator.of(context).pop()},
                    icon: const Icon(Icons.close))
              ],
            ),
          ),
          const Divider(thickness: 1),
          if (widget.future != null)
            FutureBuilder(
              future: widget.future,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  return Expanded(
                    child: ComboBoxListItems(
                      data: snapshot.data,
                      onSelect: widget.onSelect,
                      selectedValue: widget.selectedValue,
                      isFilter: widget.isFilter,
                    ),
                  );
                } else {
                  return CircularProgressIndicator(
                    color: context.primaryColor,
                  );
                }
              },
            )
          else
            Expanded(
              child: (widget.data?.length ?? 0) == 0
                  ? IconButton(
                      // alignment: Alignment.bottomLeft,
                      onPressed: () {
                        widget.onRefresh!();
                      },
                      icon: Icon(
                        Icons.refresh,
                        color: context.primaryColor,
                      ),
                    )
                  : ComboBoxListItems(
                      data: widget.data ?? [],
                      onSelect: widget.onSelect,
                      selectedValue: widget.selectedValue,
                    ),
            )
        ],
      ),
    );
  }
}

class ComboBoxListItems extends StatefulWidget {
  const ComboBoxListItems(
      {super.key,
      required this.data,
      required this.onSelect,
      this.isFilter,
      this.selectedValue});
  final List<ComboBoxDataModel> data;
  final Function onSelect;
  final int? selectedValue;
  final bool? isFilter;
  @override
  State<ComboBoxListItems> createState() => _ComboBoxListItemsState();
}

class _ComboBoxListItemsState extends State<ComboBoxListItems> {
  List<ComboBoxDataModel> myArr = [];
  ScrollController scrollController = ScrollController();
  @override
  void initState() {
    myArr = widget.data;
    if (widget.isFilter == true && myArr.isNotEmpty) {
      myArr.insert(0, ComboBoxDataModel(0, "All"));
    }
    super.initState();
    if (widget.selectedValue != null) {
      var selectedValue =
          myArr.firstWhere((element) => element.id == widget.selectedValue);
      var indexOfSelected = myArr.indexOf(selectedValue);
      print(indexOfSelected);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollController.animateTo(indexOfSelected * 40,
            duration: const Duration(milliseconds: 400), curve: Curves.easeIn);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 30,
          child: TextField(
            textAlign: TextAlign.center,
            decoration: InputDecoration(
              hintStyle: const TextStyle(fontSize: 14),
              hintText: 'Search',
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(
                  color: context.primaryColor,
                  width: 1,
                  style: BorderStyle.solid,
                ),
              ),
            ),
            onChanged: (val) {
              setState(() {
                myArr = widget.data
                    .where((element) => element.name
                        .trim()
                        .toLowerCase()
                        .contains(val.trim().toLowerCase()))
                    .toList();
              });
            },
          ),
        ),
        const SizedBox(height: 5),
        // Container(
        //   padding: const EdgeInsets.symmetric(horizontal: 15),
        //   child: InkWell(
        //     splashColor: myAccentColor,
        //     onTap: () {
        //       Navigator.of(context).pop();
        //       widget.onSelect(null, "All");
        //     },
        //     child: Card(
        //       child: Center(
        //         child: Padding(
        //           padding: const EdgeInsets.all(8.0),
        //           child: Text("All",
        //               style: TextStyle(
        //                   color: null == widget.selectedValue
        //                       ? myAccentColor
        //                       : myColor4)),
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
        Expanded(
          child: ListView.builder(
            controller: scrollController,
            itemCount: myArr.length,
            itemBuilder: (context, index) {
              return Container(
                color: context.backgroundColor,
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: InkWell(
                  splashColor: context.primaryColor,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onSelect(myArr[index].id, myArr[index].name);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(top: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: myArr[index].id == widget.selectedValue
                            ? context.primaryColor
                            : context.backgroundColor,
                        border: Border.all(color: context.primaryColor)),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(myArr[index].name,
                            style: TextStyle(
                                color: myArr[index].id == widget.selectedValue
                                    ? context.backgroundColor
                                    : context.primaryColor)),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
