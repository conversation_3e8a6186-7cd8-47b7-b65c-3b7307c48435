import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:provider/provider.dart';

void invoiceListItemDetailsDialog({
  required BuildContext context,
  required int id,
  required Function onSelect,
  required Function onDelete,
  required List<ProductDTO> selectedInvoiceProduct,
  String? virtualProductId,
  String? barcode,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      ProductDTO model;

      // Find the product in the list
      if (virtualProductId != null) {
        // If we have a virtual ID, use that to find the product
        model = selectedInvoiceProduct.firstWhere(
          (element) => element.virtualProductId == virtualProductId,
        );
      } else if (barcode != null) {
        // If we have a barcode, use that to find the product
        model = selectedInvoiceProduct.firstWhere(
          (element) => element.id == id && element.barcode == barcode,
        );
      } else {
        // Otherwise, just use the product ID
        model = selectedInvoiceProduct.firstWhere(
          (element) => element.id == id,
        );
      }

      var warehouses =
          Provider.of<WarehouseController>(context, listen: false).warehouses;
      return Dialog(
        backgroundColor: Colors.white,
        insetPadding: EdgeInsets.zero, // This removes default padding.
        child: Container(
          width: MediaQuery.of(context).size.width - 10, // Full screen width
          padding:
              const EdgeInsets.all(16.0), // Add some padding inside the dialog
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // Makes the dialog wrap its content
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(),
                  SizedBox(
                    width: context.width / 1.3,
                    child: Text(
                      model.title ?? "",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: context.colors.scrim,
                          overflow: TextOverflow.ellipsis,
                          fontSize: 16,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      onDelete();
                    },
                    child: Icon(
                      Icons.delete,
                      color: context.errorColor,
                      size: 25,
                    ),
                  )
                ],
              ),
              const SizedBox(height: 5),
              Container(
                width: MediaQuery.of(context).size.width - 20,
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 30),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                  color: context.colors.secondary,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "المحزن",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: context.colors.background,
                          fontSize: 15,
                          fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "المتبقي",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: context.colors.background,
                          fontSize: 15,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              ListView.builder(
                shrinkWrap: true,
                itemCount: warehouses.length,
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap: () {
                      onSelect(
                        warehouses[index].id,
                        warehouses[index].name,
                      );
                    },
                    child: Container(
                      margin: const EdgeInsets.only(top: 5),
                      width: MediaQuery.of(context).size.width - 20,
                      padding: const EdgeInsets.symmetric(
                          vertical: 5, horizontal: 30),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        border: Border.all(color: context.primaryColor),
                        color: model.warehouseId == warehouses[index].id
                            ? context.colors.primary
                            : context.backgroundColor,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            warehouses[index].name,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: model.warehouseId == warehouses[index].id
                                    ? context.colors.background
                                    : context.colors.scrim,
                                fontSize: 15,
                                fontWeight: FontWeight.bold),
                          ),
                          Text(
                            model.warehouse?[index].quantity.toString() ?? "0",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: model.warehouseId == warehouses[index].id
                                    ? context.colors.background
                                    : context.colors.scrim,
                                fontSize: 15,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              )
            ],
          ),
        ),
      );
    },
  );
}
