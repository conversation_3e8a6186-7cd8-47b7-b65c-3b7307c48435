import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:http/http.dart' as http;
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/base/network/ecommerce_api.dart';
import '../main.dart';

final appCtrl = AppController();

class AppController with ChangeNotifier {
  bool isRTL = true;
  static bool isAuth = false;
  String languageVal = "ar";
  static bool? isThereConnection;
  static String? deviceId;
  static String? baseUrl;
  static String? ecommerceBaseUrl;

  static int currentLangId = 1;
  int langId = 1;
  Locale currentLang = const Locale('ar');

  //************************************************************************* */
  Future<void> getDeviceDetails() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      print('Device ID: ${androidInfo.id}');
      deviceId = androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      print('Device Identifier: ${iosInfo.identifierForVendor}');
      deviceId = iosInfo.identifierForVendor;
    } else if (Platform.isWindows) {
      final windowsInfo = await deviceInfo.windowsInfo;
      print('Windows Device ID: ${windowsInfo.deviceId}');
      deviceId = windowsInfo.deviceId;
    }
  }

  //************************************************************************* */
  static void initApp() async {
    appCtrl.languageVal =
        EasyLocalization.of(navigatorKey.currentContext!)!.locale.languageCode;
  }

  //************************************************************************* */
  static Future<bool> checkFirstTime() async {
    var isFirstTime =
        LocaleManager.instance.getBool(PreferencesKeys.IsRuningFirstTime);

    if (isFirstTime) {
      LocaleManager.instance.setBool(PreferencesKeys.IsRuningFirstTime, true);
    }

    return isFirstTime;
  }

  static Future<void> setFirstTime() async {
    LocaleManager.instance.setBool(PreferencesKeys.IsRuningFirstTime, false);
  }

  //************************************************************************* */
  static String getBaseUrlFromShared() {
    var baseUrl =
        LocaleManager.instance.getStringValue(PreferencesKeys.BaseUrl);
    return baseUrl;
    // return "https://terp.pal4it.org/APIs";
  }

  //************************************************************************* */
  static String getEcommerceBaseUrlFromShared() {
    var ecommerceBaseUrl =
        LocaleManager.instance.getStringValue(PreferencesKeys.EcommerceBaseUrl);
    return ecommerceBaseUrl;
    // return "https://terp.pal4it.org/APIs";
  }

  //************************************************************************* */
  static bool getIsUsingEcommerceFromShared() {
    var isUsing =
        LocaleManager.instance.getBool(PreferencesKeys.IsUsingEcommerce);
    return isUsing;
    // return "https://terp.pal4it.org/APIs";
  }

  //************************************************************************* */
  void setInternetStatus(bool status) {
    isThereConnection = status;
    notifyListeners();
  }

  //************************************************************************* */
  Future<bool> testServerConnection(String url) async {
    try {
      if (!url.contains("https:")) {
        url = "https://$url/APIs";
      } else {
        url = url;
      }
      String requestUrl = '$url/Secure/ConnectionTest';
      var token = LocaleManager.instance.getStringValue(PreferencesKeys.TOKEN);
      var result = await http.get(Uri.parse(requestUrl), headers: {
        "Authorization": 'bearer $token',
      });
      if (result.statusCode == 200) {
        setBaseUrl(url);
        Api.baseUrl = url;
        setFirstTime();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  //************************************************************************* */
  Future<bool> testEcommerceServerConnection(String url) async {
    try {
      if (!url.contains("https:") && !url.contains("http:")) {
        url = "https://$url";
      } else {
        url = url;
      }
      String requestUrl = '$url/api/A_GenerateSetting/ConnectionTest';

      var result = await http.get(Uri.parse(requestUrl));
      if (result.statusCode == 200) {
        setEcommerceBaseUrl(url);
        EcommerceApi.baseUrl = url;
        EcommerceApi.isUsingEcommerce = true;

        setFirstTime();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  //************************************************************************* */
  Future<bool> setBaseUrl(String url) async {
    try {
      await LocaleManager.instance.setStringValue(PreferencesKeys.BaseUrl, url);
      return true;
    } catch (e) {
      // ignore: avoid_print
      print(e);
      return false;
    }
  }

  //************************************************************************* */
  Future<bool> setEcommerceBaseUrl(String url) async {
    try {
      await LocaleManager.instance
          .setStringValue(PreferencesKeys.EcommerceBaseUrl, url);
      LocaleManager.instance.setBool(PreferencesKeys.IsUsingEcommerce, true);

      return true;
    } catch (e) {
      // ignore: avoid_print
      print(e);
      return false;
    }
  }

  //************************************************************************* */
  Future<bool> setIsUsingEcommerceStatus(bool status) async {
    try {
      LocaleManager.instance.setBool(PreferencesKeys.IsUsingEcommerce, status);
      EcommerceApi.isUsingEcommerce = status;
      return true;
    } catch (e) {
      // ignore: avoid_print
      print(e);
      return false;
    }
  }
  //************************************************************************* */
}
