import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_draft_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/comparison/inventory_comparison_screen.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/components/continuous_barcode_scanner_dialog.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/stocktaking_product_selection_screen.dart';

class StocktakingScreen extends StatefulWidget {
  final String? existingStocktakingId; // For editing existing stocktaking
  final String? existingWarehouseId;
  final String? existingWarehouseName;

  const StocktakingScreen({
    super.key,
    this.existingStocktakingId,
    this.existingWarehouseId,
    this.existingWarehouseName,
  });

  @override
  _StocktakingScreenState createState() => _StocktakingScreenState();
}

class _StocktakingScreenState extends State<StocktakingScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final TextEditingController _barcodeController = TextEditingController();
  final TextEditingController _quantityController =
      TextEditingController(text: "1");
  final FocusNode _barcodeFocusNode = FocusNode();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  final InventoryOperationController _controller =
      InventoryOperationController();
  final List<ProductDTO> _scannedItems = [];

  bool _isSaving = false;
  int? _selectedWarehouseId;
  String? _selectedWarehouseName;
  String _selectedView = "scanner"; // scanner, manual, list

  String? _currentStocktakingId; // Current stocktaking session ID
  bool _isExistingStocktaking = false;
  DateTime? _stocktakingStartDate;
  Timer? _autoSaveTimer;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _barcodeFocusNode.requestFocus();
    _loadWarehouses();

    // Check if we're editing an existing stocktaking
    if (widget.existingStocktakingId != null) {
      _loadExistingStocktaking();
    } else {
      _createNewStocktaking();
    }
  }

  @override
  void dispose() {
    animationController?.dispose();
    _barcodeController.dispose();
    _quantityController.dispose();
    _barcodeFocusNode.dispose();
    _refreshController.dispose();
    _scrollController.dispose();
    _autoSaveTimer?.cancel();
    super.dispose();
  }

  void _loadWarehouses() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WarehouseController>(context, listen: false)
          .fetchWarehouses();
    });
  }

  void _onRefresh() async {
    // Refresh logic if needed
    _refreshController.refreshCompleted();
  }

  // Open continuous barcode scanner dialog
  Future<void> _openBarcodeScanner() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ContinuousBarcodeScannerDialog(
          onBarcodeDetected: (String barcode) async {
            try {
              print("Scanner detected barcode: $barcode");

              var result = await ProductController.getItemByBarcodeWithWriting(
                  barcode: barcode);

              if (result != null) {
                print("Product found for barcode $barcode: ${result.title}");

                // IMPORTANT: If the product has attributes but they haven't been selected yet,
                // we need to show the attribute selection dialog (same as sales invoice)
                if (result.itemAttributes != null &&
                    result.itemAttributes!.isNotEmpty &&
                    !result.hasSelectedAttributes) {
                  print("Product has attributes, showing selection dialog");

                  // Close the scanner dialog first
                  Navigator.of(context).pop();

                  // Show attribute dialog for the product
                  await showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      return _buildAttributeSelectionDialog(result);
                    },
                  );

                  return true; // Successfully handled
                }

                // Product has no attributes or already has selected attributes, add directly
                _addProductToList(
                    result, int.tryParse(_quantityController.text) ?? 1);
                return true;
              } else {
                print("Product not found for barcode: $barcode");
                // Product not found
                return false;
              }
            } catch (e) {
              print("Error processing barcode $barcode in scanner: $e");
              return false;
            }
          },
        );
      },
    );
  }

  Future<void> _processBarcode(String barcode) async {
    try {
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);

      if (result != null) {
        // IMPORTANT: If the product has attributes but they haven't been selected yet,
        // we need to show the attribute selection dialog (same as sales invoice)
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          print("Product has attributes, showing selection dialog");

          // Show attribute dialog for the product
          await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return _buildAttributeSelectionDialog(result);
            },
          );
          return; // Don't add directly, let the dialog handle it
        }

        // Product has no attributes or already has selected attributes, add directly
        _addProductToList(result, int.tryParse(_quantityController.text) ?? 1);
      } else {
        errorSnackBar(message: T("Product not found: $barcode"));
      }
    } catch (e) {
      errorSnackBar(message: T("Error finding product: $e"));
    }
  }

  void _addProductToList(ProductDTO product, int quantity) {
    // IMPORTANT: For products with final barcodes (hasSelectedAttributes = true)
    // that don't have a virtualProductId, generate one to ensure proper identification in the UI
    if (product.virtualProductId == null &&
        product.hasSelectedAttributes == true &&
        product.barcode != null &&
        product.barcode!.isNotEmpty) {
      // Generate a consistent virtual product ID based on barcode for final barcodes
      product.virtualProductId = '${product.id}_final_${product.barcode}';
    }

    // Debug: Print current list before adding
    _debugPrintScannedItems();

    // Handle products with virtual ID (products with attributes)
    int existingIndex = -1;

    if (product.virtualProductId != null) {
      existingIndex = _scannedItems.indexWhere(
        (element) => element.virtualProductId == product.virtualProductId,
      );
    } else {
      // Use the same logic as sales invoice:
      // Check for exact barcode match OR (same product ID AND no attributes)
      for (int i = 0; i < _scannedItems.length; i++) {
        var item = _scannedItems[i];

        // IMPORTANT: For FINAL barcodes, we need to match by exact barcode
        // regardless of hasSelectedAttributes status
        if (product.barcode != null &&
            item.barcode != null &&
            product.barcode!.isNotEmpty &&
            item.barcode!.isNotEmpty &&
            product.barcode == item.barcode) {
          // For final barcodes (hasSelectedAttributes = true), match by exact barcode
          if (product.hasSelectedAttributes == true &&
              item.hasSelectedAttributes == true) {
            print(
                "Found exact barcode match for FINAL barcode: ${product.barcode}");
            existingIndex = i;
            break;
          }
          // For non-final barcodes (hasSelectedAttributes = false), also match by exact barcode
          else if (product.hasSelectedAttributes != true &&
              item.hasSelectedAttributes != true) {
            print(
                "Found exact barcode match for NON-FINAL barcode: ${product.barcode}");
            existingIndex = i;
            break;
          }
        }
      }

      // If no barcode match found, check by product ID (only if both have no barcode)
      if (existingIndex == -1) {
        for (int i = 0; i < _scannedItems.length; i++) {
          var item = _scannedItems[i];

          // Skip items that have virtual IDs
          if (item.virtualProductId != null) {
            continue;
          }

          // Only match by ID if both products have no barcode or empty barcode
          bool bothHaveNoBarcode =
              (product.barcode == null || product.barcode!.isEmpty) &&
                  (item.barcode == null || item.barcode!.isEmpty);

          if (bothHaveNoBarcode &&
              product.id != null &&
              item.id != null &&
              product.id == item.id &&
              product.hasSelectedAttributes == item.hasSelectedAttributes) {
            print("Found match by ID (no barcodes): ${product.id}");
            existingIndex = i;
            break;
          }
        }
      }
    }

    setState(() {
      if (existingIndex != -1) {
        // Existing product found, increase quantity
        _scannedItems[existingIndex].quantity =
            (_scannedItems[existingIndex].quantity ?? 0) + quantity;

        print(
            "Updated quantity for existing item: ${_scannedItems[existingIndex].title}, new quantity: ${_scannedItems[existingIndex].quantity}");
      } else {
        // New product, add to list
        product.quantity = quantity.toDouble();
        _scannedItems.add(product);

        print(
            "Added new item: ${product.title}, quantity: ${product.quantity}");
      }
    });

    // Debug: Print current list after adding
    _debugPrintScannedItems();

    _barcodeController.clear();
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(existingIndex != -1
            ? T("Updated quantity: ${_scannedItems[existingIndex].title} (Qty: ${_scannedItems[existingIndex].quantity?.toInt()})")
            : T("Item added: ${product.title} (Qty: $quantity)")),
        duration: const Duration(seconds: 1),
        backgroundColor: existingIndex != -1 ? Colors.orange : Colors.green,
      ),
    );

    // Auto-save after adding item
    _autoSaveDraft();
  }

  // Create the attribute selection dialog for products with attributes
  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: Text(
            T('Select Product Options'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: context.newPrimaryColor,
            ),
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.8,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.title ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Display attributes
                  ...product.itemAttributes!.map((attribute) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          attribute.attributeName ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: context.newTextColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: attribute.itemsAttributeOptions
                                  ?.map((option) {
                                bool isSelected =
                                    selectedOptions[attribute.id!] == option;
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedOptions[attribute.id!] = option;
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? context.newPrimaryColor
                                          : Colors.grey.shade200,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: isSelected
                                            ? context.newPrimaryColor
                                            : Colors.grey.shade300,
                                      ),
                                    ),
                                    child: Text(
                                      option.optionName ?? '',
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : context.newTextColor,
                                        fontWeight: isSelected
                                            ? FontWeight.w600
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList() ??
                              [],
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  }).toList(),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                T('Cancel'),
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            ElevatedButton(
              onPressed:
                  selectedOptions.length == product.itemAttributes!.length
                      ? () {
                          Navigator.of(context).pop();
                          _addProductWithAttributes(product, selectedOptions);
                        }
                      : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: context.newPrimaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(T('Add to List')),
            ),
          ],
        );
      },
    );
  }

  // Add product with selected attributes
  void _addProductWithAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Generate a unique virtual product ID based on product ID and selected options
    // Use the same pattern as sales invoice for consistency
    String virtualProductId =
        "${product.id}_${selectedOptions.entries.map((e) => "${e.key}_${e.value.id}").join("_")}";

    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: product.uniteId,
      uniteName: product.uniteName,
      category: product.category,
      quantity: (int.tryParse(_quantityController.text) ?? 1).toDouble(),
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      itemAttributes: product.itemAttributes,
      hasSelectedAttributes: true,
      virtualProductId: virtualProductId,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - $optionsString';

      // Store selected options in the description field for reference
      String attributeDetails = selectedOptions.entries
          .map((entry) =>
              '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
          .join(', ');

      if (productCopy.description == null || productCopy.description!.isEmpty) {
        productCopy.description = attributeDetails;
      } else {
        productCopy.description =
            '${productCopy.description}\n[$attributeDetails]';
      }

      productCopy.selectedOptionIds = extractSelectedOptionIds(productCopy);
    }

    // Add to list with proper duplicate checking
    _addProductToList(
        productCopy, (int.tryParse(_quantityController.text) ?? 1));
  }

  // Format the selected options for display (e.g., "S/Red")
  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    return selectedOptions.values
        .map((option) => option.optionName ?? '')
        .join('/');
  }

  Future<void> _selectWarehouse() async {
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);

    if (warehouseController.warehouses.isEmpty) {
      await warehouseController.fetchWarehouses();
    }

    if (warehouseController.warehouses.isEmpty) {
      errorSnackBar(message: T("No warehouses available"));
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: double.maxFinite,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(T("Select Warehouse"),
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              ...warehouseController.warehouses
                  .map(
                    (warehouse) => ListTile(
                      title: Text(warehouse.name ?? ''),
                      leading:
                          Icon(Icons.warehouse, color: context.newPrimaryColor),
                      onTap: () {
                        setState(() {
                          _selectedWarehouseId = warehouse.id;
                          _selectedWarehouseName = warehouse.name;
                        });
                        Navigator.pop(context);
                        // Auto-save when warehouse is selected
                        _autoSaveDraft();
                      },
                    ),
                  )
                  .toList(),
            ],
          ),
        ),
      ),
    );
  }

  // Load existing stocktaking session
  Future<void> _loadExistingStocktaking() async {
    try {
      setState(() {
        _isExistingStocktaking = true;
        _currentStocktakingId = widget.existingStocktakingId;
        _selectedWarehouseId = int.tryParse(widget.existingWarehouseId ?? '');
        _selectedWarehouseName = widget.existingWarehouseName;
      });

      // Load existing items from database
      final existingItems =
          await _controller.getStocktakingDraftItems(_currentStocktakingId!);
      setState(() {
        _scannedItems.clear();
        _scannedItems.addAll(existingItems);
      });

      successSnackBar(message: T("Stocktaking session loaded successfully"));
    } catch (e) {
      errorSnackBar(message: T("Error loading stocktaking session: $e"));
    }
  }

  // Create new stocktaking session
  Future<void> _createNewStocktaking() async {
    try {
      _currentStocktakingId = DateTime.now().millisecondsSinceEpoch.toString();
      _stocktakingStartDate = DateTime.now();

      setState(() {
        _isExistingStocktaking = false;
      });

      // Only save initial draft if we have a warehouse selected
      if (_selectedWarehouseId != null) {
        await _saveDraft();
      }
    } catch (e) {
      errorSnackBar(message: T("Error creating stocktaking session: $e"));
    }
  }

  // Save current state as draft
  Future<void> _saveDraft() async {
    if (_currentStocktakingId == null) return;

    try {
      var data = StocktakingDraftDTO(
        lastModified: DateTime.now(),
        startDate: _stocktakingStartDate ?? DateTime.now(),
        stocktakingId: _currentStocktakingId,
        warehouseId: _selectedWarehouseId,
        warehouseName: _selectedWarehouseName,
        items: _scannedItems,
        status: 'draft',
      );

      final result = await _controller.saveStocktakingDraft(data.toJson());
      if (result > 0) {
        print("Draft saved successfully: $result");
      } else {
        print("Failed to save draft: $result");
      }
    } catch (e) {
      print("Error saving draft: $e");
      // Only show error to user if it's critical
      if (mounted) {
        errorSnackBar(message: T("Failed to save draft: ${e.toString()}"));
      }
    }
  }

  // Auto-save draft when items change
  void _autoSaveDraft() {
    // Cancel previous timer if exists
    _autoSaveTimer?.cancel();

    // Set new timer for auto-save
    _autoSaveTimer = Timer(const Duration(seconds: 2), () async {
      if (mounted && _currentStocktakingId != null) {
        await _saveDraft();
      }
    });
  }

  // Debug method to print current scanned items
  void _debugPrintScannedItems() {
    print("=== Current Scanned Items (${_scannedItems.length}) ===");
    for (int i = 0; i < _scannedItems.length; i++) {
      var item = _scannedItems[i];
      print(
          "[$i] ${item.title} - Barcode: ${item.barcode} - Code: ${item.code} - ID: ${item.id} - Qty: ${item.quantity} - HasAttrs: ${item.hasSelectedAttributes} - VirtualID: ${item.virtualProductId}");
    }
    print("=== End of List ===");
  }

  // Updated save method for final submission
  Future<void> _saveStocktaking() async {
    if (_selectedWarehouseId == null) {
      errorSnackBar(message: T("Please select a warehouse first"));
      return;
    }

    if (_scannedItems.isEmpty) {
      errorSnackBar(message: T("Please scan at least one item"));
      return;
    }

    // Show confirmation dialog
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T("Finalize Stocktaking")),
        content: Text(T(
            "Are you sure you want to finalize this stocktaking? This action cannot be undone.")),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(T("Cancel")),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(T("Finalize")),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final operationItems = _scannedItems
          .map((product) => InventoryOperationItems(
                itemCode: product.barcode ?? product.code ?? '',
                quantity: product.quantity ?? 1.0,
              ))
          .toList();

      final operation = InventoryOperationModel(
        operationType: InventoryOperationType.Stocktaking.index,
        entryDate: _stocktakingStartDate ?? DateTime.now(),
        entryDateFormated: (_stocktakingStartDate ?? DateTime.now()).toString(),
        inventoryOperationItems: operationItems,
        isPosted: false,
        fromStoreID: _selectedWarehouseId,
        storeName: _selectedWarehouseName,
      );

      final result = await _controller.insertOrUpdateInventoryOperation({
        'data': jsonEncode(operation.toJson()),
        'status': 'pending',
        'localCode': _currentStocktakingId,
        'type': 'Stocktaking',
      });

      if (result > 0) {
        // Delete draft after successful finalization
        await _controller.deleteStocktakingDraft(_currentStocktakingId!);

        successSnackBar(message: T("Stocktaking finalized successfully"));
        Navigator.pop(context); // Return to stocktaking list
      } else {
        errorSnackBar(message: T("Failed to finalize stocktaking"));
      }
    } catch (e) {
      errorSnackBar(message: T("Error finalizing stocktaking: $e"));
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  // Add method to delete current draft
  Future<void> _deleteDraft() async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T("Delete Stocktaking")),
        content: Text(T(
            "Are you sure you want to delete this stocktaking session? All data will be lost.")),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(T("Cancel")),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(T("Delete")),
          ),
        ],
      ),
    );

    if (confirm == true && _currentStocktakingId != null) {
      try {
        await _controller.deleteStocktakingDraft(_currentStocktakingId!);
        successSnackBar(message: T("Stocktaking session deleted"));
        Navigator.pop(context);
      } catch (e) {
        errorSnackBar(message: T("Error deleting stocktaking: $e"));
      }
    }
  }

  // Open product selection screen
  Future<void> _openProductSelection() async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StocktakingProductSelectionScreen(
          selectedProducts: _scannedItems,
          onAddProduct: (product) {
            _addProductToList(product, 1);
          },
          onRemoveProduct: (productId, virtualProductId) {
            setState(() {
              if (virtualProductId != null) {
                _scannedItems.removeWhere(
                  (item) => item.virtualProductId == virtualProductId,
                );
              } else {
                _scannedItems.removeWhere((item) => item.id == productId);
              }
            });
            _autoSaveDraft();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Header
          SliverToBoxAdapter(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, 2),
                    blurRadius: 6,
                  )
                ],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonHeader(
                    icon: Icons.inventory_2_outlined,
                    title: _isExistingStocktaking
                        ? T("Edit Stocktaking")
                        : T("New Stocktaking"),
                    actions: [
                      // Manual Save Draft Button

                      // Delete button (only for existing stocktaking)
                      if (_isExistingStocktaking)
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red,
                          onPressed: _deleteDraft,
                          tooltip: T("Delete Stocktaking"),
                        ),
                    ],
                  ),

                  // Stats Row
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            T("Items Scanned"),
                            _scannedItems.length.toString(),
                            Icons.qr_code_scanner,
                            context.newPrimaryColor,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            T("Total Quantity"),
                            _scannedItems
                                .fold<int>(
                                    0,
                                    (sum, item) =>
                                        sum + (item.quantity?.toInt() ?? 0))
                                .toString(),
                            Icons.inventory,
                            context.newSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Warehouse Selection
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: InkWell(
                onTap: _selectWarehouse,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedWarehouseId != null
                          ? context.newPrimaryColor
                          : Colors.grey.shade300,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warehouse,
                        color: _selectedWarehouseId != null
                            ? context.newPrimaryColor
                            : Colors.grey,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _selectedWarehouseName ?? T("Select Warehouse"),
                          style: TextStyle(
                            color: _selectedWarehouseName == null
                                ? Colors.grey
                                : context.newTextColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        color: _selectedWarehouseId != null
                            ? context.newPrimaryColor
                            : Colors.grey,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // View Toggle Buttons
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildViewButton(
                      "scanner",
                      T("Scanner"),
                      Icons.qr_code_scanner,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewButton(
                      "manual",
                      T("Manual"),
                      Icons.keyboard,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewButton(
                      "list",
                      T("Items List"),
                      Icons.list,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content based on selected view
          if (_selectedView == "scanner") ...[
            // Scanner View - Button to open continuous scanner
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Scanner Button
                    Container(
                      width: double.infinity,
                      height: 200,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            context.newPrimaryColor.withOpacity(0.8),
                            context.newPrimaryColor,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: context.newPrimaryColor.withOpacity(0.3),
                            spreadRadius: 2,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(16),
                          onTap: _openBarcodeScanner,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.qr_code_scanner,
                                size: 64,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                T("Open Barcode Scanner"),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                T("Tap to start continuous scanning"),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Quick scan button for single items
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _openBarcodeScanner,
                        icon: const Icon(Icons.camera_alt),
                        label: Text(T("Quick Scan")),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.newSecondaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Features info card
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Colors.blue.shade700,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                T("Scanner Features"),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "• ${T('Continuous scanning with audio feedback')}\n"
                            "• ${T('Support for external barcode devices')}\n"
                            "• ${T('Manual barcode entry')}\n"
                            "• ${T('Camera controls (flash, switch)')}\n"
                            "• ${T('Haptic feedback for successful scans')}",
                            style: TextStyle(
                              color: Colors.blue.shade600,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ] else if (_selectedView == "manual") ...[
            // Manual Input View
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      T("Manual Entry"),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.newTextColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _barcodeController,
                      focusNode: _barcodeFocusNode,
                      decoration: InputDecoration(
                        labelText: T("Item Code/Barcode"),
                        hintText: T("Enter or scan item code"),
                        prefixIcon: const Icon(Icons.qr_code),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      onSubmitted: (value) {
                        if (value.isNotEmpty) {
                          _processBarcode(value);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextField(
                            controller: _quantityController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: T("Quantity"),
                              prefixIcon: const Icon(Icons.numbers),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade50,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 3,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              if (_barcodeController.text.isNotEmpty) {
                                _processBarcode(_barcodeController.text);
                              }
                            },
                            icon: const Icon(Icons.add),
                            label: Text(T("Add Item")),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: context.newPrimaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Product Selection Button in Manual View
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _openProductSelection,
                        icon: const Icon(Icons.shopping_cart),
                        label: Text(T("Browse & Select Products")),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // Items List
          if (_selectedView == "list" || _scannedItems.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      T("Scanned Items (${_scannedItems.length})"),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.newTextColor,
                      ),
                    ),
                    if (_scannedItems.isNotEmpty)
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _scannedItems.clear();
                          });
                        },
                        icon: const Icon(Icons.clear_all, color: Colors.red),
                        label: Text(T("Clear All"),
                            style: TextStyle(color: Colors.red)),
                      ),
                  ],
                ),
              ),
            ),
            _scannedItems.isEmpty
                ? SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.all(16),
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.inventory_2_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            T("No items scanned yet"),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            T("Use scanner or manual entry to add items"),
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                : SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final product = _scannedItems[index];
                        return Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: context.newPrimaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.inventory,
                                color: context.newPrimaryColor,
                              ),
                            ),
                            title: Text(
                              product.title ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: context.newTextColor,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  T("Code: ${product.code ?? product.barcode}"),
                                  style: TextStyle(
                                    color:
                                        context.newTextColor.withOpacity(0.7),
                                  ),
                                ),
                                Text(
                                  T("Quantity: ${product.quantity?.toInt() ?? 0}"),
                                  style: TextStyle(
                                    color:
                                        context.newTextColor.withOpacity(0.7),
                                  ),
                                ),
                                if (product.barcodeName != null &&
                                    product.barcodeName!.isNotEmpty)
                                  Text(
                                    T("Attributes: ${product.barcodeName}"),
                                    style: TextStyle(
                                      color: Colors.blue,
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.remove_circle_outline),
                                  color: Colors.orange,
                                  onPressed: () {
                                    setState(() {
                                      if ((product.quantity ?? 0) > 1) {
                                        _scannedItems[index].quantity =
                                            (_scannedItems[index].quantity ??
                                                    0) -
                                                1;
                                      } else {
                                        _scannedItems.removeAt(index);
                                      }
                                    });
                                    // Auto-save after quantity change
                                    _autoSaveDraft();
                                  },
                                ),
                                IconButton(
                                  icon: const Icon(Icons.add_circle_outline),
                                  color: Colors.green,
                                  onPressed: () {
                                    setState(() {
                                      _scannedItems[index].quantity =
                                          (_scannedItems[index].quantity ?? 0) +
                                              1;
                                    });
                                    // Auto-save after quantity change
                                    _autoSaveDraft();
                                  },
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete_outline),
                                  color: Colors.red,
                                  onPressed: () {
                                    setState(() {
                                      _scannedItems.removeAt(index);
                                    });
                                    // Auto-save after item removal
                                    _autoSaveDraft();
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                      childCount: _scannedItems.length,
                    ),
                  ),
          ],

          // Save Button
          if (_scannedItems.isNotEmpty)
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => InventoryComparisonScreen(
                        stocktakingData: StocktakingDraftDTO(
                          lastModified: DateTime.now(),
                          startDate: _stocktakingStartDate ?? DateTime.now(),
                          stocktakingId: _currentStocktakingId,
                          warehouseId: _selectedWarehouseId,
                          warehouseName: _selectedWarehouseName,
                          items: _scannedItems,
                          status: 'draft',
                        ),
                      ),
                    ));
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.compare),
                      const SizedBox(width: 8),
                      Text(
                        T("Compare Stocktaking"),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: context.newTextColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(String view, String label, IconData icon) {
    final isSelected = _selectedView == view;
    return InkWell(
      onTap: () {
        setState(() {
          _selectedView = view;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? context.newPrimaryColor : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? context.newPrimaryColor : Colors.grey.shade300,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: context.newPrimaryColor.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ]
              : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Colors.white
                  : context.newTextColor.withOpacity(0.7),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : context.newTextColor.withOpacity(0.7),
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
