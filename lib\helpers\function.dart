import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_order_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:inventory_application/models/model/language/translate_model.dart';
import 'package:inventory_application/models/model/product_model.dart';

Timer? _internetCheckTimer;
final Duration _checkInterval = Duration(seconds: 5);
String defulteImage() {
  return "https://waterstation.com.tr/img/default.jpg";
}

//------------------------------------------------------
String T(String key) => tr(key);

String? getNameTranslated(List<TranslateModel> data) {
  var lanId = AppController.currentLangId;

  if (data.isNotEmpty) {
    if (data.any((element) =>
        element.languageId == lanId && element.localeKey == "Name")) {
      return data
          .firstWhere((element) =>
              element.languageId == lanId && element.localeKey == "Name")
          .localeValue;
    } else {
      return null;
    }
  }
  return null;
}

//------------------------------------------------------
ProductDTO mapProductModel(ProductModel model) {
  // Set default barcode if available
  String? defaultBarcode;
  String? defaultBarcodeName;
  if (model.barcodes != null && model.barcodes!.isNotEmpty) {
    defaultBarcode = model.barcodes!.first.barCode;
    defaultBarcodeName = model.barcodes!.first.barCodeName;
  }

  return ProductDTO(
      category: model.parentName,
      code: model.code,
      description: model.name,
      price:
          model.itemPrice!.length > 0 ? model.itemPrice?.first.salesPrice : 0,
      id: model.iD,
      stock: double.tryParse(
              model.inventory?.first.quantityBalance?.toString() ?? '0') ??
          0.0,
      warehouse: model.inventory
          ?.map(
            (e) => ProductWarehousesDTO(
              id: e.iD,
              name: e.storeName,
              quantity: double.tryParse(e.quantityBalance.toString()) ?? 0,
            ),
          )
          .toList(),
      title: model.name,
      uniteName:
          model.itemPrice!.isNotEmpty ? model.itemPrice!.first.unitName : "",
      uniteId: model.itemPrice!.isNotEmpty ? model.itemPrice?.first.unitID : 0,
      barcode: defaultBarcode,
      barcodeName: defaultBarcodeName,
      barcodes: model.barcodes,
      categoryId: model.parentID,
      itemAttributes: model.itemAttributes,
      hasSelectedAttributes: false,
      units: model.itemPrice
          ?.map(
            (e) => ItemPriceDTO(
              iD: e.iD,
              isDefult: e.isDefult,
              itemID: e.itemID,
              salesPrice: e.salesPrice,
              unitID: e.unitID,
              unitName: e.unitName,
            ),
          )
          .toList());
}
//------------------------------------------------------

InvoiceModel mapInvoiceDto(InvoiceDto invoice, List<ProductDTO> products) {
  var items = products
      .map(
        (e) => SalesItems(
          discountValue: e.discountValue,
          itemID: e.id,
          storeID: e.warehouseId,
          barcode: e.barcode,
          inventory: e.warehouse
              ?.map(
                (i) => Inventory(
                    iD: i.id, quantityBalance: i.quantity, storeName: i.name),
              )
              .toList(),
          unitID: e.uniteId,
          quantity: e.quantity,
          unitPrice: e.price,
          itemCode: e.code,
          unitName: e.uniteName,
          storeName: e.warehouseName,
          itemName: e.title,
          hasSelectedAttributes: e.hasSelectedAttributes,
          virtualProductId: e.virtualProductId,
          itemAttributes: e.itemAttributes,
          selectedAttributesMap: _extractAttributesFromDescription(e),
          selectedOptionIds: extractSelectedOptionIds(e),
        ),
      )
      .toList();

  var invoiceModel = InvoiceModel(
    code: invoice.invoiceCode,
    localId: invoice.localId,
    iD: invoice.id ?? 0,
    entryDate: invoice.invoiceDate ?? DateTime.now(),
    appReferanceCode: invoice.appReferanceCode,
    customerID: invoice.customerId,
    storeID: invoice.warehouseId,
    storeName: invoice.warehouseName,
    discountValue: invoice.totalDiscount,
    isFullyPaid: invoice.paymentValue == invoice.totalAfterDiscount,
    notes: invoice.note,
    salesInvoiceID: invoice.connectedToInvoiceCode,
    paidAmount: invoice.paymentValue,
    salesItems: items,
    salesOrderID: invoice.salesOrderID,
    customerName: invoice.custoemrName,
    salesmanId: invoice.salesmanId,
    salesmanName: invoice.salesmanName,
    total: invoice.total ?? 0,
  );
  return invoiceModel;
}

//------------------------------------------------------

List<InventoryOperationItems> mapListProductDtoToInventoryOperationItem(
    List<ProductDTO> product) {
  var result = product
      .map(
        (e) => InventoryOperationItems(
          quantity: e.quantity,
          unitID: e.uniteId,
          unitPrice: e.price,
          itemName: e.title,
          itemCode: e.code,
          itemID: e.id,
          storeID: e.warehouseId,
        ),
      )
      .toList();
  return result;
}

//------------------------------------------------------

// Helper function to extract attributes from the product description
Map<String, String>? _extractAttributesFromDescription(ProductDTO product) {
  // If the product doesn't have selected attributes, return null
  if (!product.hasSelectedAttributes || product.description == null) {
    return null;
  }

  try {
    // The attributes are stored in the description in the format:
    // "Original description\n[AttributeName1: Value1, AttributeName2: Value2]"
    String description = product.description!;

    // Find the attributes section which is enclosed in square brackets
    int startIdx = description.lastIndexOf('[');
    int endIdx = description.lastIndexOf(']');

    if (startIdx != -1 && endIdx != -1 && endIdx > startIdx) {
      // Extract the attributes string between the brackets
      String attributesStr = description.substring(startIdx + 1, endIdx);

      // Split the attributes by commas and create a map
      Map<String, String> attributesMap = {};
      List<String> attributes = attributesStr.split(', ');

      for (String attr in attributes) {
        List<String> keyValue = attr.split(': ');
        if (keyValue.length == 2) {
          attributesMap[keyValue[0]] = keyValue[1];
        }
      }

      return attributesMap;
    }
  } catch (e) {
    print("Error extracting attributes from description: $e");
  }

  return null;
}

// Helper function to extract selected option IDs from the product attributes
List<int>? extractSelectedOptionIds(ProductDTO product) {
  // If the product doesn't have selected attributes, return null
  if (!product.hasSelectedAttributes ||
      product.itemAttributes == null ||
      product.itemAttributes!.isEmpty) {
    return null;
  }

  try {
    List<int> optionIds = [];

    // If we have a virtualProductId, try to parse it to extract option IDs
    if (product.virtualProductId != null &&
        product.virtualProductId!.contains('_')) {
      // virtualProductId format: '${product.id}_${optionId1}-${attributeId1}_${optionId2}-${attributeId2}...'
      String optionsSection =
          product.virtualProductId!.split('_').sublist(1).join('_');
      List<String> optionPairs = optionsSection.split('_');

      for (String pair in optionPairs) {
        // Each pair is in the format: '${optionId}-${attributeId}'
        List<String> parts = pair.split('-');
        if (parts.length == 2) {
          try {
            int optionId = int.parse(parts[0]);
            optionIds.add(optionId);
          } catch (e) {
            // Skip if cannot parse as integer
          }
        }
      }

      // If we successfully extracted option IDs, return them
      if (optionIds.isNotEmpty) {
        return optionIds;
      }
    }

    // If virtualProductId parsing didn't work, or we didn't get any option IDs,
    // try to extract from the description
    if (product.description != null) {
      String description = product.description!;

      // The description might contain the attribute details in brackets
      int startIdx = description.lastIndexOf('[');
      int endIdx = description.lastIndexOf(']');

      if (startIdx != -1 && endIdx != -1 && endIdx > startIdx) {
        // For each attribute in the product
        for (var attribute in product.itemAttributes!) {
          if (attribute.itemsAttributeOptions != null) {
            // Find the selected option based on the description
            for (var option in attribute.itemsAttributeOptions!) {
              // If the option name appears in the description, consider it selected
              if (description.contains(option.optionName ?? '')) {
                optionIds.add(option.optionId ?? 0);
                break; // Only one option can be selected per attribute
              }
            }
          }
        }

        if (optionIds.isNotEmpty) {
          return optionIds;
        }
      }
    }
  } catch (e) {
    print("Error extracting option IDs: $e");
  }

  return null;
}

//------------------------------------------------------
InvoiceDto mapInvoiceModel(InvoiceModel invoiceModel) {
  // Map the InvoiceModel back to InvoiceDto
  var invoiceDto = InvoiceDto(
    id: invoiceModel.iD ?? 0,
    invoiceCode: invoiceModel.code,
    appReferanceCode: invoiceModel.appReferanceCode,
    customerId: invoiceModel.customerID,
    custoemrName: invoiceModel.customerName,
    warehouseId: invoiceModel.storeID,
    warehouseName: invoiceModel.storeName ?? "",
    totalDiscount: invoiceModel.discountValue,
    paymentValue: invoiceModel.paidAmount,
    invoiceDate: invoiceModel.entryDate,
    localId: invoiceModel.localId,
    discountValue: invoiceModel.discountValue,
    note: invoiceModel.notes,
    salesItems: invoiceModel.salesItems
        ?.map(
          (e) => _salesItemToProductDTO(e),
        )
        .toList(),
    connectedToInvoiceCode: invoiceModel.salesInvoiceID,
  );

  return invoiceDto;
}

// Helper function to convert a SalesItem to ProductDTO
ProductDTO _salesItemToProductDTO(SalesItems salesItem) {
  // Create the base ProductDTO
  ProductDTO product = ProductDTO(
    code: salesItem.itemCode,
    id: salesItem.itemID,
    price: salesItem.unitPrice,
    discountValue: salesItem.discountValue,
    quantity: salesItem.quantity,
    title: salesItem.itemName,
    uniteId: salesItem.unitID,
    uniteName: salesItem.unitName,
    warehouseId: salesItem.storeID,
    warehouse: salesItem.inventory
        ?.map(
          (e) => ProductWarehousesDTO(
              id: e.iD, name: e.storeName, quantity: e.quantityBalance),
        )
        .toList(),
    warehouseName: salesItem.inventory?.isNotEmpty == true
        ? salesItem.inventory!.any(
            (element) => element.iD == salesItem.storeID,
          )
            ? salesItem.inventory
                    ?.firstWhere(
                      (i) => i.iD == salesItem.storeID,
                    )
                    .storeName ??
                ""
            : ""
        : "",
    hasSelectedAttributes: salesItem.hasSelectedAttributes ?? false,
    virtualProductId: salesItem.virtualProductId,
    itemAttributes: salesItem.itemAttributes,
    description: _buildDescriptionFromAttributes(salesItem),
  );

  // Apply the selected options to the attributes
  if (product.hasSelectedAttributes &&
      salesItem.selectedOptionIds != null &&
      salesItem.selectedOptionIds!.isNotEmpty &&
      product.itemAttributes != null) {
    // Mark the options as selected based on the option IDs
    _applySelectedOptionsToAttributes(product, salesItem.selectedOptionIds!);
  }

  return product;
}

// Helper function to mark options as selected in the attributes
void _applySelectedOptionsToAttributes(
    ProductDTO product, List<int> optionIds) {
  if (product.itemAttributes == null) return;

  // For each attribute
  for (var attribute in product.itemAttributes!) {
    if (attribute.itemsAttributeOptions == null) continue;

    // For each option in the attribute
    for (var option in attribute.itemsAttributeOptions!) {
      // If the option ID is in the list of selected option IDs, mark it as selected
      if (option.optionId != null && optionIds.contains(option.optionId)) {
        // We don't have a direct way to mark options as selected in the model
        // This information is usually handled in the UI
        // But we can ensure the virtualProductId is correctly set
        if (product.virtualProductId == null) {
          // If no virtualProductId exists, create one
          product.virtualProductId =
              '${product.id}_${option.optionId}-${attribute.id}';
        } else if (!product.virtualProductId!
            .contains('${option.optionId}-${attribute.id}')) {
          // If virtualProductId exists but doesn't contain this option, append it
          product.virtualProductId =
              '${product.virtualProductId}_${option.optionId}-${attribute.id}';
        }
      }
    }
  }
}

// Helper function to rebuild description from attributes
String? _buildDescriptionFromAttributes(SalesItems item) {
  // Keep original description if there are no attributes
  if ((item.selectedAttributesMap == null ||
          item.selectedAttributesMap!.isEmpty) &&
      (item.selectedOptionIds == null || item.selectedOptionIds!.isEmpty)) {
    return null;
  }

  // If we have a attributes map, use it
  if (item.selectedAttributesMap != null &&
      item.selectedAttributesMap!.isNotEmpty) {
    String attributeDetails = item.selectedAttributesMap!.entries
        .map((entry) => '${entry.key}: ${entry.value}')
        .join(', ');

    return '[${attributeDetails}]';
  }

  // If we have option IDs but no map, try to build the description from the attributes
  if (item.selectedOptionIds != null &&
      item.selectedOptionIds!.isNotEmpty &&
      item.itemAttributes != null) {
    List<String> attributePairs = [];

    // For each attribute
    for (var attribute in item.itemAttributes!) {
      if (attribute.itemsAttributeOptions == null) continue;

      // For each option in the attribute
      for (var option in attribute.itemsAttributeOptions!) {
        // If the option ID is in the list of selected option IDs
        if (option.optionId != null &&
            item.selectedOptionIds!.contains(option.optionId) &&
            attribute.attributeName != null &&
            option.optionName != null) {
          attributePairs
              .add('${attribute.attributeName}: ${option.optionName}');
          break; // Only one option can be selected per attribute
        }
      }
    }

    if (attributePairs.isNotEmpty) {
      return '[${attributePairs.join(', ')}]';
    }
  }

  return null;
}

//------------------------------------------------------
List<ProductDTO> mapInvoiceModelItems(InvoiceModel invoiceModel) {
  // Map the list of SalesItems to ProductDTO objects
  var products = invoiceModel.salesItems?.map((item) {
        return _salesItemToProductDTO(item);
      }).toList() ??
      [];

  return products;
}

//----------------------------------------------
EcommerceOrderDto mapInvoiceModelWithEcommerceDto(
    InvoiceModel invoiceModel, TransactionTypes type) {
  return EcommerceOrderDto(
      date: invoiceModel.entryDate,
      erpReferenceCode: invoiceModel.code,
      erpReferenceId: invoiceModel.iD.toString(),
      transactionType: type,
      customerId: invoiceModel.customerID,
      total: invoiceModel.total,
      eRPStoreId: invoiceModel.storeID,
      salesReferenceCode: invoiceModel.salesInvoiceID,
      totalDiscount: invoiceModel.discountValue,
      note: invoiceModel.notes,
      items: invoiceModel.salesItems
          ?.map(
            (e) => EcommerceItemTransactionDto(
              isHaveCombination: e.hasSelectedAttributes,
              selectedOptionIds: e.selectedOptionIds,
              erpProductId: e.itemID,
              finalBarcode: e.barcode,
              price: e.unitPrice,
              quantity: int.parse(e.quantity.toString().split(".")[0]),
            ),
          )
          .toList());
}

//----------------------------------------------
DateTime formatDate(String date) {
  // Extract the milliseconds since epoch (inside the parentheses)
  int millisecondsSinceEpoch =
      int.parse(date.replaceAll(RegExp(r'[^0-9]'), ''));

// Convert to DateTime
  DateTime dateTime =
      DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  return dateTime;
}

//----------------------------------------------
String getInvoiceTypeName(SalesType salesType) {
  return salesType.toString().split('.').last;
}

//----------------------------------------------
String getInvoiceSyncStatus(InvoiceSyncStatus status) {
  return status.toString().split('.').last;
}

Future<bool> isThereNetworkConnection() async {
  var internet = AppController.isThereConnection ?? false;
  return internet;
}
