import 'package:flutter/material.dart';
import 'package:inventory_application/screens/accounting/accounts/accounting_home_screen.dart';
import 'package:inventory_application/screens/accounting/financialfund/fund_movement_screen.dart';
import 'package:inventory_application/screens/accounting/financialfund/fund_settlement_screen.dart';
import 'package:inventory_application/screens/accounting/financialfund/opening_box_value_screen.dart';
import 'package:inventory_application/screens/components/back_button_header.dart';
import 'package:inventory_application/screens/home/<USER>';

class FinancialFundPage extends StatefulWidget {
  const FinancialFundPage({super.key});

  @override
  State<FinancialFundPage> createState() => _FinancialFundPageState();
}

class _FinancialFundPageState extends State<FinancialFundPage> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          const BackButtonHeader(
            title: 'Financial Fund',
            icon: Icons.account_balance_wallet,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16.0,
                mainAxisSpacing: 16.0,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const FundMovementScreen(),
                      ));
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'حركة الصندوق',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const FundSettlementScreen(),
                      ));
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'تسوية الصندوق واصلاحه',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const OpeningBoxValueScreen(),
                      ));
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.yellow,
                      title: 'قيمة الصندوق الافتتاحية',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
