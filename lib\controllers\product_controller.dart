import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:sqflite/sqflite.dart';
import 'dart:async';

class ProductController with ChangeNotifier {
  List<ProductDTO> productList = [];
  List<ProductDTO> realProductList = [];
  bool runningSyncization = false;
  int fetchedProductCount = 0;

  int _getCurrentPage() {
    var currentPage = ((realProductList.length) / _pageSize).ceil() + 1;
    if (realProductList.isEmpty) currentPage = 1;
    return currentPage;
  }

  final int _pageSize = 10;

  //---------------------------------------------------------------------------------
  Future<void> getItems({
    bool resetAndRefresh = false,
    int? categoryId,
    String? search,
  }) async {
    try {
      final db = await DatabaseHelper().database;
      if (resetAndRefresh) {
        realProductList.clear();
      }

      // Define skip and take for pagination
      int skip = realProductList.length;
      int take = _pageSize;

      // Build a query for the local database with filtering and pagination
      List<String> whereClauses = [];
      List<dynamic> whereArgs = [];

      if (categoryId != null && categoryId > 0) {
        whereClauses.add('ProductModel.Parent_ID = ?');
        whereArgs.add(categoryId);
      }
      if (search != null && search.isNotEmpty) {
        whereClauses.add('ProductModel.Name LIKE ?');
        whereArgs.add('%$search%');
      }

      String whereClause =
          whereClauses.isNotEmpty ? whereClauses.join(' AND ') : '';

      // Query for unique products only (without duplicates)
      List<Map<String, dynamic>> products = await db.query(
        'ProductModel',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        limit: take,
        offset: skip,
      );

      if (products.isNotEmpty) {
        // Local data is available, now fetch inventory and prices separately
        for (var productData in products) {
          var productModel = ProductModel.fromJson(productData);

          // Fetch Inventory data for this product
          List<Map<String, dynamic>> inventoryData = await db.query(
            'Inventory',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch ItemPrice data for this product
          List<Map<String, dynamic>> priceData = await db.query(
            'ItemPrice',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          List<Map<String, dynamic>> barcodes = await db.query(
            'Barcodes',
            where: 'ItemID = ?',
            whereArgs: [productModel.iD],
          );
          List<Map<String, dynamic>> itemAttributesData = await db.query(
            'ItemAttributes',
            where: 'Item_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch and attach attribute options
          List<ItemAttribute> itemAttributes = [];
          for (var attributeData in itemAttributesData) {
            int? attributeId = attributeData['ID'];

            if (attributeId != null) {
              List<Map<String, dynamic>> optionsData = await db.query(
                'ItemsAttributeOptions',
                where: 'Attribute_ID = ?',
                whereArgs: [attributeId],
              );

              List<ItemAttributeOption> options = optionsData
                  .map((option) => ItemAttributeOption.fromJson(option))
                  .toList();

              ItemAttribute attribute = ItemAttribute.fromJson(attributeData);
              attribute.itemsAttributeOptions = options;

              itemAttributes.add(attribute);
            }
          }

          // Map and attach inventory and price details to the product model
          productModel.inventory =
              inventoryData.map((inv) => Inventory.fromJson(inv)).toList();
          productModel.itemPrice =
              priceData.map((price) => ItemPrice.fromJson(price)).toList();
          productModel.barcodes =
              barcodes.map((barcodes) => Barcodes.fromJson(barcodes)).toList();
          productModel.itemAttributes = itemAttributes;

          realProductList.add(mapProductModel(productModel));
        }
      } else {
        // Fetch from backend if no local data is available
        await fetchProduct();
      }

      notifyListeners();
    } catch (e) {
      print("Error fetching items: $e");
    }
  }

  //---------------------------------------------------------------------------
  static Future<ProductDTO?> getItemByBarcode({String? barcode}) async {
    try {
      final player = AudioPlayer();
      player.play(AssetSource('sounds/barcodeRead.m4a'));

      final db = await DatabaseHelper().database;

      // First check if the barcode exists in the Barcodes table
      List<Map<String, dynamic>> barcodeResults = await db.query(
        'Barcodes',
        where: "BarCode = ?",
        whereArgs: [barcode],
      );

      if (barcodeResults.isNotEmpty) {
        // Found a matching barcode, get the product
        int productId = barcodeResults.first['ItemID'];
        String barcodeValue = barcodeResults.first['BarCode'];
        String barcodeName = barcodeResults.first['BarCodeName'];

        List<Map<String, dynamic>> products = await db.query(
          'ProductModel',
          where: "ID = ?",
          whereArgs: [productId],
        );

        if (products.isNotEmpty) {
          var productModel = ProductModel.fromJson(products.first);
          ProductDTO productDTO = mapProductModel(productModel);
          // Set the specific barcode that was scanned
          productDTO.barcode = barcodeValue;
          productDTO.barcodeName = barcodeName;
          return productDTO;
        }
      }

      // Fallback to searching by product code if no barcode match
      List<Map<String, dynamic>> products = await db.query(
        'ProductModel',
        where: "ProductModel.Code LIKE ?",
        whereArgs: ['%$barcode%'],
      );

      if (products.isNotEmpty) {
        var productModel = ProductModel.fromJson(products.first);
        return mapProductModel(productModel);
      }

      return null;
    } catch (e) {
      print(e);
      return null;
    }
  }

  //-----------------------------------------------------
  static Future<ProductDTO?> getItemByBarcodeWithWriting(
      {String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) return null;

      final player = AudioPlayer();
      player.play(AssetSource('sounds/barcodeRead.m4a'));

      final db = await DatabaseHelper().database;

      // Get all barcodes matching the scanned barcode
      List<Map<String, dynamic>> barcodeResults = await db.query(
        'Barcodes',
        where: "BarCode = ?",
        whereArgs: [barcode],
      );

      if (barcodeResults.isNotEmpty) {
        // Extract the first matched barcode
        var firstMatch = barcodeResults.first;
        int productId = firstMatch['ItemID'];
        String barcodeValue = firstMatch['BarCode'];
        String barcodeName = firstMatch['BarCodeName'] ?? '';
        bool isFinalBarcode = firstMatch['IsFinalBarcode'] == 1;

        // Fetch the corresponding product from ProductModel
        List<Map<String, dynamic>> products = await db.query(
          'ProductModel',
          where: "ID = ?",
          whereArgs: [productId],
        );

        if (products.isNotEmpty) {
          var productModel = ProductModel.fromJson(products.first);

          // Now fetch all related data for the product

          // Fetch Inventory data for this product
          List<Map<String, dynamic>> inventoryData = await db.query(
            'Inventory',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch ItemPrice data for this product
          List<Map<String, dynamic>> priceData = await db.query(
            'ItemPrice',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch all barcodes for this product
          List<Map<String, dynamic>> allBarcodes = await db.query(
            'Barcodes',
            where: 'ItemID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch attribute data only if not a final barcode
          List<ItemAttribute> itemAttributes = [];
          if (!isFinalBarcode) {
            List<Map<String, dynamic>> itemAttributesData = await db.query(
              'ItemAttributes',
              where: 'Item_ID = ?',
              whereArgs: [productModel.iD],
            );

            // Fetch and attach attribute options
            for (var attributeData in itemAttributesData) {
              int? attributeId = attributeData['ID'];

              if (attributeId != null) {
                List<Map<String, dynamic>> optionsData = await db.query(
                  'ItemsAttributeOptions',
                  where: 'Attribute_ID = ?',
                  whereArgs: [attributeId],
                );

                List<ItemAttributeOption> options = optionsData
                    .map((option) => ItemAttributeOption.fromJson(option))
                    .toList();

                ItemAttribute attribute = ItemAttribute.fromJson(attributeData);
                attribute.itemsAttributeOptions = options;

                itemAttributes.add(attribute);
              }
            }
          }

          // Map and attach all data to the product model
          productModel.inventory =
              inventoryData.map((inv) => Inventory.fromJson(inv)).toList();
          productModel.itemPrice =
              priceData.map((price) => ItemPrice.fromJson(price)).toList();
          productModel.barcodes =
              allBarcodes.map((b) => Barcodes.fromJson(b)).toList();
          productModel.itemAttributes = itemAttributes;

          // Convert to ProductDTO for use in the app
          ProductDTO productDTO = mapProductModel(productModel);

          // Set the specific barcode that was scanned
          productDTO.barcode = barcodeValue;
          productDTO.barcodeName = barcodeName;

          // If this is a final barcode, append the barcodeName (which contains attribute info)
          // to the product title
          if (isFinalBarcode && barcodeName.isNotEmpty) {
            productDTO.title = '${productDTO.title} - $barcodeName';

            // Set a flag to indicate this product already has attributes selected
            productDTO.hasSelectedAttributes = true;
          }

          return productDTO;
        }
      }

      return null;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return null;
    }
  }

  //------------------------------------------------------------------------------
  Future<void> fetchProduct() async {
    // if (await checkIfProductDataExists()) {
    //   return;
    // }

    if (await isThereNetworkConnection() == false) {
      errorSnackBar(
          message: "يجب توفير اتصال بالانترنت لاول مرة للحصول على المنتجات");
      return;
    }
    productList.clear();
    bool isStillThereProducts = true;

    while (isStillThereProducts) {
      runningSyncization = true;
      String url =
          '/ItemsCategory/GetAllItems?skip=$fetchedProductCount&take=500';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          var productModel = ProductModel.fromJson(element);
          await insertProduct(productModel);
          // productList.add(productModel);
          fetchedProductCount++;
          notifyListeners();
        }

        //notifyListeners();
        if (result.data.length < 500) {
          isStillThereProducts = false;
          runningSyncization = false;
        }
      } else {
        isStillThereProducts = false;
        runningSyncization = false;
      }
    }
    notifyListeners();
    successSnackBar(message: "تمت مزامنة كافة المنتجات");
  }

  //------------------------------------------------------------------------------
  Future<void> fetchProductAfterCertineId() async {
    // if (await checkIfProductDataExists()) {
    //   return;
    // }

    if (await isThereNetworkConnection() == false) {
      errorSnackBar(
          message: "يجب توفير اتصال بالانترنت لاول مرة للحصول على المنتجات");
      return;
    }
    var maxId = await getMaxProductId();

    if (maxId == 0) {
      await fetchProduct();
      return;
    }
    bool isStillThereProducts = true;
    var skip = 0;
    while (isStillThereProducts) {
      runningSyncization = true;
      String url = '/ItemsCategory/GetAllItems?id=$maxId&skip=$skip&take=200';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          var productModel = ProductModel.fromJson(element);
          await insertProduct(productModel);
          fetchedProductCount++;
          skip++;
          notifyListeners();
        }

        //notifyListeners();
        if (result.data.length < 200) {
          isStillThereProducts = false;
          runningSyncization = false;
        }
      } else {
        isStillThereProducts = false;
        runningSyncization = false;
      }
    }
    notifyListeners();
    successSnackBar(message: "تمت مزامنة كافة المنتجات");
  }

  //------------------------------------------------------------------------------
  Future<void> insertProduct(ProductModel productModel) async {
    try {
      final db = await DatabaseHelper().database;
      await db.insert('ProductModel', {
        'ID': productModel.iD,
        'Name': productModel.name,
        'Code': productModel.code,
        'Parent_ID': productModel.parentID,
        'Parent_Name': productModel.parentName,
        'Level_Type': productModel.levelType,
        'isParent': productModel.isParent == true ? 1 : 0,
      });

      // Insert associated Inventory data into Inventory table
      if (productModel.inventory != null) {
        for (var inventory in productModel.inventory!) {
          await db.insert('Inventory', {
            'ID': inventory.iD,
            'Store_Name': inventory.storeName,
            'Quantity_Balance': inventory.quantityBalance,
            'Product_ID': productModel.iD, // Foreign key to ProductModel
          });
        }
      }

      if (productModel.barcodes != null) {
        for (var barcode in productModel.barcodes!) {
          await db.insert('Barcodes', {
            'BarCode': barcode.barCode,
            'BarCodeName': barcode.barCodeName,
            'IsFinalBarcode': barcode.isFinalBarcode == true ? 1 : 0,
            'ItemID': productModel.iD, // Foreign key to ProductModel
          });
        }
      }

      if (productModel.itemPrice != null) {
        for (var price in productModel.itemPrice!) {
          await db.insert('ItemPrice', {
            'ID': price.iD,
            'Item_ID': price.itemID,
            'Unit_ID': price.unitID,
            'Unit_Name': price.unitName,
            'Is_Defult': price.isDefult == true ? 1 : 0,
            'Sales_Price': price.salesPrice,
            'Product_ID': productModel.iD, // Foreign key to ProductModel
          });
        }
      }
      if (productModel.itemAttributes != null) {
        for (var attribute in productModel.itemAttributes!) {
          // Insert the attribute record
          await db.insert('ItemAttributes', {
            'ID': attribute.id,
            'Attribute_Type_Id': attribute.attributeTypeId,
            'Attribute_Name': attribute.attributeName,
            'Item_ID': productModel.iD,
          });

          // Insert each option into the ItemsAttributeOptions table
          if (attribute.itemsAttributeOptions != null) {
            for (var option in attribute.itemsAttributeOptions!) {
              await db.insert('ItemsAttributeOptions', {
                'ID': option.id,
                'Attribute_ID': option.attributeId ?? attribute.id,
                'Option_ID': option.optionId,
                'Option_Name': option.optionName,
              });
            }
          }
        }
      }

      print(productModel.iD);
      //  print(productModel.code);
    } catch (e) {
      print(e);
    }
  }

  //------------------------------------------------------------------------------
  Future<bool> checkIfProductDataExists() async {
    try {
      final db = await DatabaseHelper().database;
      final List<Map<String, dynamic>> result =
          await db.rawQuery('SELECT COUNT(*) as count FROM ProductModel');

      if (result.isNotEmpty && result[0]['count'] > 0) {
        // There is data in the table
        return true;
      }

      // No data in the table
      return false;
    } catch (e) {
      print("Error checking ProductModel data: $e");
      return false;
    }
  }

  //------------------------------------------------------------------------------
  Future<int> getMaxProductId() async {
    final db = await DatabaseHelper().database;
    var result =
        await db.rawQuery('SELECT MAX(id) as max_id FROM ProductModel');

    int maxId = Sqflite.firstIntValue(result) ?? 0;
    return maxId;
  }

  //------------------------------------------------------------------------------
  Future<int> getProductCount() async {
    final db = await DatabaseHelper().database;
    var result =
        await db.rawQuery('SELECT COUNT(*) as count FROM ProductModel');
    int count = Sqflite.firstIntValue(result) ?? 0;
    fetchedProductCount = count;
    return count;
  }

  //------------------------------------------------------------------------------
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await DatabaseHelper().database;

      await db.transaction((txn) async {
        await txn.delete('ProductModel');
        await txn.delete('Barcodes');
        await txn.delete('Inventory');
        await txn.delete('ItemPrice');
        await txn.delete('ItemAttributes');
        await txn.delete('ItemsAttributeOptions');
      });
      fetchedProductCount = 0;
      notifyListeners();
      await fetchProduct();
      return true;
    } catch (e) {
      return false;
    }
  }
}
