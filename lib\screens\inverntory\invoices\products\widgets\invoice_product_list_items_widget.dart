import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:provider/provider.dart';

class InvoiceProductListItemsWidget extends StatefulWidget {
  const InvoiceProductListItemsWidget(
      {super.key,
      required this.product,
      required this.onAddProduct,
      required this.onDeleteProduct,
      required this.selectedProducts});
  final ProductDTO product;
  final Function onAddProduct;
  final Function onDeleteProduct;
  final List<ProductDTO> selectedProducts;

  @override
  State<InvoiceProductListItemsWidget> createState() =>
      _InvoiceProductListItemsWidgetState();
}

class _InvoiceProductListItemsWidgetState
    extends State<InvoiceProductListItemsWidget> {
  @override
  Widget build(BuildContext context) {
    var provider = widget.selectedProducts;
    return InkWell(
      onTap: () {
        setState(() {
          // Apply default unit if product doesn't have a unit and default is set
          final invoiceSettingsController =
              Provider.of<InvoiceSettingsController>(
            context,
            listen: false,
          );

          ProductDTO productToAdd = widget.product;

          if (productToAdd.uniteId == null &&
              invoiceSettingsController.defaultUnitId != null) {
            // Create a copy of the product with the default unit
            productToAdd = ProductDTO(
                id: widget.product.id,
                title: widget.product.title,
                barcode: widget.product.barcode,
                barcodeName: widget.product.barcodeName,
                code: widget.product.code,
                description: widget.product.description,
                price: widget.product.price,
                total: widget.product.total,
                discountValue: widget.product.discountValue,
                stock: widget.product.stock,
                uniteId: invoiceSettingsController.defaultUnitId,
                uniteName: invoiceSettingsController.defaultUnitName,
                category: widget.product.category,
                quantity: widget.product.quantity,
                warehouseId: widget.product.warehouseId,
                warehouseName: widget.product.warehouseName,
                thumbnail: widget.product.thumbnail,
                warehouse: widget.product.warehouse,
                units: widget.product.units,
                barcodes: widget.product.barcodes,
                hasSelectedAttributes: widget.product.hasSelectedAttributes,
                virtualProductId: widget.product.virtualProductId,
                itemAttributes: widget.product.itemAttributes);
          }

          widget.onAddProduct(productToAdd);
        });
      },
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            decoration: BoxDecoration(
              color: context.onBackground,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  blurRadius: 4,
                  offset: Offset(4, 8), // Shadow position
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: CircleAvatar(
                    backgroundColor: context.backgroundColor,
                    maxRadius: 35,
                    minRadius: 35,
                    foregroundImage: NetworkImage(
                        widget.product.thumbnail ?? defulteImage()),
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  widget.product.title ?? "",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: context.colors.scrim,
                      fontSize: 15,
                      fontWeight: FontWeight.bold),
                ),
                // Text(
                //   widget.product.barcodes
                //           ?.map((e) => (e.barCodeName ?? ""))
                //           .join(",") ??
                //       "",
                //   maxLines: 1,
                //   overflow: TextOverflow.ellipsis,
                //   style: TextStyle(
                //       color: context.colors.scrim,
                //       fontSize: 15,
                //       fontWeight: FontWeight.bold),
                // ),
                const SizedBox(height: 10),
                Center(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                    decoration: BoxDecoration(
                        color: context.colors.secondary,
                        borderRadius: BorderRadius.circular(10)),
                    child: Text(
                      widget.product.code ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: context.colors.background,
                          fontSize: 15,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.warehouse,
                          size: 20,
                          color: context.primaryColor,
                        ),
                        const SizedBox(width: 5),
                        Text(
                          widget.product.stock.toString(),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: context.colors.scrim,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        const SizedBox(width: 5),
                        Text(
                          widget.product.price.toString(),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: context.colors.scrim,
                            fontSize: 14,
                          ),
                        ),
                        Icon(
                          Icons.attach_money,
                          size: 20,
                          color: context.primaryColor,
                        ),
                      ],
                    )
                  ],
                )
              ],
            ),
          ),
          Positioned(
            top: 10,
            left: 5,
            child: provider.any(
              (element) => element.id == widget.product.id,
            )
                ? InkWell(
                    onTap: () {
                      widget.onDeleteProduct(widget.product.id ?? 0);
                      errorSnackBar(
                          title: "تمت الازالة",
                          message: "تمت ازالة ${widget.product.title}");
                    },
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      // alignment: Alignment.center,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: context.errorColor,
                      ),
                      child: Center(
                        child: Icon(
                          Icons.remove_circle,
                          color: context.backgroundColor,
                          size: 25,
                        ),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
