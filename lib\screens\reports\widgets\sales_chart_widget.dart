import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/daily_sales_report_dto.dart';
import 'package:intl/intl.dart';

class SalesChartWidget extends StatefulWidget {
  final DailySalesReportDTO report;
  final bool showTitle;

  const SalesChartWidget({
    Key? key,
    required this.report,
    this.showTitle = true,
  }) : super(key: key);

  @override
  State<SalesChartWidget> createState() => _SalesChartWidgetState();
}

class _SalesChartWidgetState extends State<SalesChartWidget> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.showTitle)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              T('Sales Distribution'),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        _buildChartSelector(),
      ],
    );
  }

  Widget _buildChartSelector() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          // Responsive tab bar
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 500;

              return TabBar(
                tabs: [
                  Tab(text: isSmallScreen ? T('Customer') : T('By Customer')),
                  Tab(text: isSmallScreen ? T('Warehouse') : T('By Warehouse')),
                  Tab(text: isSmallScreen ? T('Payment') : T('By Payment')),
                ],
                labelColor: context.newPrimaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: context.newPrimaryColor,
                labelStyle: TextStyle(
                  fontSize: isSmallScreen ? 12 : 14,
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          // Responsive height for the chart
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 500;

              return SizedBox(
                height: isSmallScreen
                    ? 400
                    : 300, // Taller on mobile for better layout
                child: TabBarView(
                  children: [
                    _buildCustomerChart(),
                    _buildWarehouseChart(),
                    _buildPaymentMethodChart(),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerChart() {
    final salesByCustomer = widget.report.calculateSalesByCustomer();

    if (salesByCustomer.isEmpty) {
      return _buildEmptyChart(T('No customer data available'));
    }

    // Check if we're on a small screen (mobile)
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 500;

        if (isSmallScreen) {
          // Mobile layout - chart on top, legend below
          return Column(
            children: [
              SizedBox(
                height: 200,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 30,
                    sections: _showingSections(salesByCustomer),
                  ),
                ),
              ),
              Expanded(
                child: _buildLegend(salesByCustomer),
              ),
            ],
          );
        } else {
          // Desktop layout - chart and legend side by side
          return Row(
            children: [
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    sections: _showingSections(salesByCustomer),
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: _buildLegend(salesByCustomer),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildWarehouseChart() {
    final salesByWarehouse = widget.report.calculateSalesByWarehouse();

    if (salesByWarehouse.isEmpty) {
      return _buildEmptyChart(T('No warehouse data available'));
    }

    // Check if we're on a small screen (mobile)
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 500;

        if (isSmallScreen) {
          // Mobile layout - chart on top, legend below
          return Column(
            children: [
              SizedBox(
                height: 200,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 30,
                    sections: _showingSections(salesByWarehouse),
                  ),
                ),
              ),
              Expanded(
                child: _buildLegend(salesByWarehouse),
              ),
            ],
          );
        } else {
          // Desktop layout - chart and legend side by side
          return Row(
            children: [
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    sections: _showingSections(salesByWarehouse),
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: _buildLegend(salesByWarehouse),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildPaymentMethodChart() {
    final salesByPaymentMethod = widget.report.calculateSalesByPaymentMethod();

    if (salesByPaymentMethod.isEmpty) {
      return _buildEmptyChart(T('No payment method data available'));
    }

    // Check if we're on a small screen (mobile)
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 500;

        if (isSmallScreen) {
          // Mobile layout - chart on top, legend below
          return Column(
            children: [
              SizedBox(
                height: 200,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 30,
                    sections: _showingSections(salesByPaymentMethod),
                  ),
                ),
              ),
              Expanded(
                child: _buildLegend(salesByPaymentMethod),
              ),
            ],
          );
        } else {
          // Desktop layout - chart and legend side by side
          return Row(
            children: [
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    sections: _showingSections(salesByPaymentMethod),
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: _buildLegend(salesByPaymentMethod),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildEmptyChart(String message) {
    return Center(
      child: Text(
        message,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      ),
    );
  }

  Widget _buildLegend(Map<String, double> data) {
    final total = data.values.fold(0.0, (sum, value) => sum + value);
    final formatter = NumberFormat('#,##0.00');

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.entries.map((entry) {
          final index = data.keys.toList().indexOf(entry.key);
          final percentage = (entry.value / total * 100).toStringAsFixed(1);
          final isSelected = touchedIndex == index;

          return Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: isSelected
                  ? _getChartColors()[index % _getChartColors().length]
                      .withOpacity(0.2)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: _getChartColors()[index % _getChartColors().length],
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    entry.key,
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '${formatter.format(entry.value)} ($percentage%)',
                  style: TextStyle(
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  List<PieChartSectionData> _showingSections(Map<String, double> data) {
    final total = data.values.fold(0.0, (sum, value) => sum + value);

    return List.generate(data.length, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 20.0 : 16.0;
      final radius = isTouched ? 60.0 : 50.0;
      final value = data.values.elementAt(i);
      final percentage = (value / total * 100).toStringAsFixed(1);

      return PieChartSectionData(
        color: _getChartColors()[i % _getChartColors().length],
        value: value,
        title: '$percentage%',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    });
  }

  List<Color> _getChartColors() {
    return [
      const Color(0xFF2196F3), // Blue
      const Color(0xFFFFA726), // Orange
      const Color(0xFF66BB6A), // Green
      const Color(0xFFEF5350), // Red
      const Color(0xFF9C27B0), // Purple
      const Color(0xFF29B6F6), // Light Blue
      const Color(0xFFFFEE58), // Yellow
      const Color(0xFF78909C), // Blue Grey
      const Color(0xFFFF7043), // Deep Orange
      const Color(0xFF26A69A), // Teal
    ];
  }
}
