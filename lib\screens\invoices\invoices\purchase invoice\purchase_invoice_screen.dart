import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20invoice/widgets/create_sale_invoice_dialog_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20invoice/widgets/sale_invoice_bottom_navbar_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:provider/provider.dart';

class PurchaseInvoiceScreen extends StatefulWidget {
  const PurchaseInvoiceScreen({super.key});

  @override
  State<PurchaseInvoiceScreen> createState() => _PurchaseInvoiceScreenState();
}

class _PurchaseInvoiceScreenState extends State<PurchaseInvoiceScreen> {
  final Color primaryColor = const Color(0xFF667EEA);
  final Color secondaryColor = const Color(0xFF764BA2);
  final Color backgroundColor = const Color(0xFFF8F9FD);
  final Color textColor = const Color(0xFF2C3E50);
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setDefaultValues();
    });
  }

  void _setDefaultValues() async {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);
    final customerController =
        Provider.of<CustomerController>(context, listen: false);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    // Get invoice number
    await saleInvoiceController.getSaleInvoiceNumber();

    // Try to load warehouses with retry logic
    int retryCount = 0;
    while (warehouseController.warehouses.isEmpty && retryCount < 3) {
      try {
        await warehouseController.fetchWarehouses();
        retryCount++;
        if (warehouseController.warehouses.isEmpty && retryCount < 3) {
          // Wait a moment before retrying
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        retryCount++;
      }
    }

    // Try to load customers with retry logic
    retryCount = 0;
    while (customerController.customers.isEmpty && retryCount < 3) {
      try {
        await customerController.fetchCustomers();
        retryCount++;
        if (customerController.customers.isEmpty && retryCount < 3) {
          // Wait a moment before retrying
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        print("Error loading customers: $e");
        retryCount++;
      }
    }

    // First check if we have a default warehouse in settings
    if (invoiceSettingsController.defaultWarehouseId != null) {
      saleInvoiceController.invoice.warehouseId =
          invoiceSettingsController.defaultWarehouseId;
      saleInvoiceController.invoice.warehouseName =
          invoiceSettingsController.defaultWarehouseName;
    }
    // If not, set default warehouse from the first available warehouse
    else if (warehouseController.warehouses.isNotEmpty) {
      try {
        final firstWarehouse = warehouseController.warehouses.first;
        saleInvoiceController.invoice.warehouseId = firstWarehouse.id;
        saleInvoiceController.invoice.warehouseName = firstWarehouse.name;
      } catch (e) {
        print("Error setting default warehouse: $e");
      }
    } else {
      // Handle case where no warehouses are available
      print("Warning: No warehouses available");
      errorSnackBar(
        message:
            T("No warehouses available. Please add warehouses to the system."),
        context: context,
      );
    }

    // First check if we have a default customer in settings
    if (invoiceSettingsController.defaultCustomerId != null) {
      saleInvoiceController.invoice.customerId =
          invoiceSettingsController.defaultCustomerId;
      saleInvoiceController.invoice.custoemrName =
          invoiceSettingsController.defaultCustomerName;
    }
    // If not, set default customer from the first available customer
    else if (customerController.customers.isNotEmpty) {
      try {
        final firstCustomer = customerController.customers.first;
        if (firstCustomer.iD != null) {
          saleInvoiceController.invoice.customerId = firstCustomer.iD;
          saleInvoiceController.invoice.custoemrName = firstCustomer.name;
        } else {
          // Handle case where customer data is incomplete
          print("Warning: First customer has null id or name");
          errorSnackBar(
            message:
                T("Could not set default customer. Please select manually."),
            // ignore: use_build_context_synchronously
            context: context,
          );
        }
      } catch (e) {
        print("Error setting default customer: $e");
      }
    } else {
      // Handle case where no customers are available
      print("Warning: No customers available");
      errorSnackBar(
        message:
            T("No customers available. Please add customers to the system."),
        context: context,
      );
    }

    // Set today's date
    saleInvoiceController.invoice.invoiceDate = DateTime.now();

    // Set payment type to cash
    saleInvoiceController.invoice.invoicePaymentType = InvoicePaymentType.chash;

    // Calculate the invoice total (this will set the total and totalAfterDiscount)
    saleInvoiceController.calculateInvoiceTotal();

    // Set payment value to the total after discount (full payment)
    if (saleInvoiceController.invoice.totalAfterDiscount != null) {
      double totalAfterDiscount =
          saleInvoiceController.invoice.totalAfterDiscount ?? 0;

      // Use the controller method to set the payment value
      saleInvoiceController.setPaymentValue(totalAfterDiscount);
    }

    // Notify listeners to update UI
    // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
    saleInvoiceController.notifyListeners();

    // If critical data is missing, show dialog to let user select manually
    if (saleInvoiceController.invoice.warehouseId == null ||
        saleInvoiceController.invoice.customerId == null) {
      // Show dialog for manual selection
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).push(DialogRoute(
          context: context,
          barrierDismissible: false,
          builder: (context) => CreateSaleInvoiceDialogWidget(
            title: T("Create purchase Invoice"),
          ),
        ));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var products =
        Provider.of<SaleInvoiceController>(context).selectedSaleInvoiceProduct;

    var provider = Provider.of<SaleInvoiceController>(context, listen: false);

    return WillPopScope(
        onWillPop: () async {
          var result = await showConfirmDialog(
            title: T('Exit'),
            content: T('Are you sure?'),
            backText: T("back"),
            confirmText: T("exit"),
          );
          if (result == true) {
            if (provider.invoice.id != null) {
              provider.invoice = InvoiceDto();
              provider.selectedSaleInvoiceProduct = [];
            }
          }

          return result ?? false;
        },
        child: Scaffold(
          backgroundColor: backgroundColor,
          appBar: AppBar(
            elevation: 0,
            toolbarHeight: 60,
            shadowColor: Colors.transparent,
            backgroundColor: Colors.white,
            automaticallyImplyLeading: false,
            title: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      T("Purchase Invoice"),
                      style: TextStyle(
                        color: textColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        _buildCompactButton(
                          label: T("Save & View"),
                          icon: Icons.visibility_outlined,
                          onPressed: () async {},
                        ),
                        const SizedBox(width: 8),
                        _buildCompactButton(
                          label: T("Save & New"),
                          icon: Icons.save_outlined,
                          isMain: true,
                          onPressed: () async {},
                        ),
                      ],
                    )
                  ],
                ),
              ],
            ),
            toolbarOpacity: 1,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(1.0),
              child: Container(
                color: Colors.grey.withOpacity(0.2),
                height: 1.0,
              ),
            ),
          ),
          bottomNavigationBar: const SaleInvoiceBottomNavbarWidget(),
          body: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InvoiceSelectProductWidget(
                    selectedProducts: products,
                    onChange: () {
                      // Force UI refresh when products change
                      setState(() {});
                    },
                    onAddProduct: (ProductDTO product) {
                      provider.addProductToSelectedList(product);
                      // Force UI refresh
                      setState(() {});
                    },
                    onRemoveProduct: (int id) {
                      provider.deleteProductFromSelectedList(id);
                      // Force UI refresh
                      setState(() {});
                    },
                    onSearchByBarcode: (String barcode) async {
                      // Get the controller
                      final saleInvoiceController =
                          Provider.of<SaleInvoiceController>(
                        context,
                        listen: false,
                      );

                      // Process the barcode
                      var result = await saleInvoiceController.getItemByBarcode(
                        barcode: barcode,
                      );

                      // If result is a ProductDTO object rather than a boolean,
                      // it means we need to show the attribute selection dialog
                      if (result is ProductDTO) {
                        // Get reference to the product screen
                        Provider.of<ProductController>(
                            // ignore: use_build_context_synchronously
                            context,
                            listen: false);

                        // Show attribute dialog for the product
                        await showDialog(
                          // ignore: use_build_context_synchronously
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return _buildAttributeSelectionDialog(result);
                          },
                        );

                        // Return true to indicate success (dialog was shown)
                        return true;
                      }

                      // Force UI refresh immediately after barcode scan
                      setState(() {
                        // This will trigger a rebuild with the updated product list
                      });

                      // Add a small delay and refresh again to ensure UI updates
                      await Future.delayed(const Duration(milliseconds: 100));
                      setState(() {});

                      // Show error if product not found
                      if (result == false) {
                        errorSnackBar(
                          message: T(
                              "There is no product associated with the barcode"),
                          // ignore: use_build_context_synchronously
                          context: context,
                        );
                      }

                      return result;
                    },
                  ),
                  if (products.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.zero,
                      child: InvoiceProductListHeaderWidget(
                        backgroundColor: primaryColor,
                        textColor: Colors.white,
                      ),
                    ),
                    Consumer<SaleInvoiceController>(
                      builder: (context, saleInvoiceController, child) {
                        return ListView.separated(
                          itemCount: products.length,
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          separatorBuilder: (context, index) => Divider(
                            color: Colors.grey.withOpacity(0.2),
                            height: 1,
                            indent: 0,
                            endIndent: 0,
                          ),
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return InvoiceListItemsWidget(
                              id: products[index].id ?? 0,
                              barcode: products[index].barcode,
                              virtualProductId:
                                  products[index].virtualProductId,
                              selectedInvoiceProduct: saleInvoiceController
                                  .selectedSaleInvoiceProduct,
                              onChangeWarehouse: (int productId,
                                  int warehouseId, String warehouseName) {
                                provider.setProductWarehouse(
                                    productId, warehouseId, warehouseName);
                                setState(() {});
                              },
                              onDeleteProduct: (int productId,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  // If we have a virtual ID, use it when deleting
                                  provider.deleteProductFromSelectedList(
                                      productId,
                                      virtualProductId: virtualProductId);
                                } else {
                                  // Otherwise use the regular product ID
                                  provider
                                      .deleteProductFromSelectedList(productId);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdatePrice: (int productId, double price) {
                                provider.updateProductPrice(productId, price);
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdateQuantity:
                                  (int productId, double quantity) {
                                provider.updateProductQuantity(
                                    productId, quantity);
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdateUnit: () {},
                            );
                          },
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),
        ));
  }

  Widget _buildCompactButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    bool isMain = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isMain ? primaryColor : Colors.white,
        foregroundColor: isMain ? Colors.white : primaryColor,
        elevation: isMain ? 2 : 0,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isMain ? Colors.transparent : primaryColor.withOpacity(0.5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Create the attribute selection dialog for products with attributes
  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: Text(
            T('Select Product Options'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.5,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.title ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Sort attributes by their order property
                  ...(() {
                    final sortedAttributes =
                        List<ItemAttribute>.from(product.itemAttributes ?? []);
                    sortedAttributes
                        .sort((a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
                    return sortedAttributes;
                  })()
                      .map((attribute) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          attribute.attributeName ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: textColor,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Wrap options in a grid
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: (attribute.itemsAttributeOptions ?? [])
                              .map((option) {
                            final isSelected =
                                selectedOptions[attribute.id] == option;

                            return InkWell(
                              onTap: () {
                                setState(() {
                                  if (attribute.id != null) {
                                    selectedOptions[attribute.id!] = option;
                                  }
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? primaryColor
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: isSelected
                                        ? primaryColor
                                        : Colors.grey.shade300,
                                  ),
                                ),
                                child: Text(
                                  option.optionName ?? '',
                                  style: TextStyle(
                                    color:
                                        isSelected ? Colors.white : textColor,
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  }).toList(),

                  // Preview of the selected options
                  if (selectedOptions.isNotEmpty) ...[
                    const Divider(),
                    const SizedBox(height: 8),
                    Text(
                      T('Selected Options:'),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getFormattedSelectedOptions(selectedOptions),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                T('Cancel'),
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
              ),
              onPressed: (product.itemAttributes != null &&
                      selectedOptions.length == product.itemAttributes!.length)
                  ? () {
                      // All attributes have been selected
                      Navigator.of(context).pop();
                      _addProductWithSelectedAttributes(
                          product, selectedOptions);
                    }
                  : null, // Disable button if not all attributes are selected
              child: Text(T('Add to Cart')),
            ),
          ],
        );
      },
    );
  }

  // Format the selected options as a string (e.g., "S/Red")
  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    if (selectedOptions.isEmpty) return '';

    // Sort by attribute ID (which should correspond to the attribute order)
    final sortedKeys = selectedOptions.keys.toList()..sort();

    // Join option names with '/'
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }

  // Add product to cart with selected attributes
  void _addProductWithSelectedAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Create a unique key for this product + attributes combination
    // We'll use the selected option IDs to create a "virtual" product ID
    String optionsHash = selectedOptions.entries
        .map((e) => '${e.value.optionId}-${e.key}')
        .join('_');

    String virtualProductId = '${product.id}_$optionsHash';

    // Get the SaleInvoiceController
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);

    // Check if this product with the same attributes already exists in the cart
    int existingIndex = saleInvoiceController.selectedSaleInvoiceProduct
        .indexWhere((element) => element.virtualProductId == virtualProductId);

    // If the product already exists, just increase its quantity
    if (existingIndex >= 0) {
      var existingProduct =
          saleInvoiceController.selectedSaleInvoiceProduct[existingIndex];
      double newQuantity =
          (existingProduct.quantity ?? 1) + (product.quantity ?? 1);

      // Update the quantity
      saleInvoiceController.selectedSaleInvoiceProduct[existingIndex].quantity =
          newQuantity;

      // Update the total
      double price = saleInvoiceController
              .selectedSaleInvoiceProduct[existingIndex].price ??
          0;
      saleInvoiceController.selectedSaleInvoiceProduct[existingIndex].total =
          price * newQuantity;

      // Recalculate invoice total
      saleInvoiceController.calculateInvoiceTotal();

      // Notify listeners for UI update
      // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
      saleInvoiceController.notifyListeners();
      return;
    }

    // If the product doesn't exist, create a new one
    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: product.uniteId,
      uniteName: product.uniteName,
      category: product.category,
      quantity: product.quantity ?? 1.0, // Ensure a default quantity
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      itemAttributes: product.itemAttributes,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - ${optionsString}';
      productCopy.hasSelectedAttributes = true;

      // Store selected options in the description field for reference
      // This way we can retrieve the information later if needed
      String attributeDetails = selectedOptions.entries
          .map((entry) =>
              '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
          .join(', ');

      if (productCopy.description == null || productCopy.description!.isEmpty) {
        productCopy.description = attributeDetails;
      } else {
        productCopy.description =
            '${productCopy.description}\n[$attributeDetails]';
      }

      // Set the virtual product ID
      productCopy.virtualProductId = virtualProductId;
    }

    // Add to invoice
    saleInvoiceController.addProductToSelectedList(productCopy);
  }
}
