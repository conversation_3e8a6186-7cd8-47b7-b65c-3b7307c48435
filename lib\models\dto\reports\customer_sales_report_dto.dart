import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class CustomerSalesReportDTO {
  final DateTime fromDate;
  final DateTime toDate;
  final double totalSales;
  final int invoiceCount;
  final List<InvoiceModel> invoices;
  final List<CustomerSalesSummary> customerSummaries;

  CustomerSalesReportDTO({
    required this.fromDate,
    required this.toDate,
    required this.totalSales,
    required this.invoiceCount,
    required this.invoices,
    required this.customerSummaries,
  });

  // Calculate sales by customer
  Map<String, double> calculateSalesByCustomer() {
    final Map<String, double> salesByCustomer = {};

    for (var customer in customerSummaries) {
      salesByCustomer[customer.customerName] = customer.totalSales;
    }

    return salesByCustomer;
  }

  // Calculate invoice count by customer
  Map<String, int> calculateInvoiceCountByCustomer() {
    final Map<String, int> invoiceCountByCustomer = {};

    for (var customer in customerSummaries) {
      invoiceCountByCustomer[customer.customerName] = customer.invoiceCount;
    }

    return invoiceCountByCustomer;
  }

  // Calculate sales by date
  Map<String, double> calculateSalesByDate() {
    final Map<String, double> salesByDate = {};

    for (var invoice in invoices) {
      if (invoice.entryDate != null) {
        final dateKey = DateFormat('yyyy-MM-dd').format(invoice.entryDate!);

        if (salesByDate.containsKey(dateKey)) {
          salesByDate[dateKey] = salesByDate[dateKey]! + (invoice.total ?? 0);
        } else {
          salesByDate[dateKey] = invoice.total ?? 0;
        }
      }
    }

    return salesByDate;
  }

  // Calculate top customers
  List<CustomerSalesSummary> getTopCustomers({int limit = 10}) {
    final sortedCustomers = List<CustomerSalesSummary>.from(customerSummaries);
    sortedCustomers.sort((a, b) => b.totalSales.compareTo(a.totalSales));

    return sortedCustomers.take(limit).toList();
  }

  // Calculate top customers by invoice count
  List<CustomerSalesSummary> getTopCustomersByInvoiceCount({int limit = 10}) {
    final sortedCustomers = List<CustomerSalesSummary>.from(customerSummaries);
    sortedCustomers.sort((a, b) => b.invoiceCount.compareTo(a.invoiceCount));

    return sortedCustomers.take(limit).toList();
  }
}

class CustomerSalesSummary {
  final int customerId;
  final String customerName;
  final String customerCode;
  final int invoiceCount;
  final double totalSales;
  final double averageSale;

  CustomerSalesSummary({
    required this.customerId,
    required this.customerName,
    required this.customerCode,
    required this.invoiceCount,
    required this.totalSales,
    required this.averageSale,
  });

  // Create from invoices
  static List<CustomerSalesSummary> createFromInvoices(
      List<InvoiceModel> invoices) {
    final Map<int, CustomerSalesSummary> customerMap = {};

    for (var invoice in invoices) {
      final customerId = invoice.customerID ?? 0;
      final customerName = invoice.customerName ?? 'Unknown';
      // Use customer ID as code since there's no specific customer code in InvoiceModel
      final customerCode = invoice.customerID?.toString() ?? '';
      final invoiceTotal = invoice.total ?? 0.0;

      if (customerMap.containsKey(customerId)) {
        // Update existing customer summary
        final existing = customerMap[customerId]!;
        final newInvoiceCount = existing.invoiceCount + 1;
        final newTotalSales = existing.totalSales + invoiceTotal;

        customerMap[customerId] = CustomerSalesSummary(
          customerId: customerId,
          customerName: customerName,
          customerCode: customerCode,
          invoiceCount: newInvoiceCount,
          totalSales: newTotalSales,
          averageSale: newTotalSales / newInvoiceCount,
        );
      } else {
        // Create new customer summary
        customerMap[customerId] = CustomerSalesSummary(
          customerId: customerId,
          customerName: customerName,
          customerCode: customerCode,
          invoiceCount: 1,
          totalSales: invoiceTotal,
          averageSale: invoiceTotal,
        );
      }
    }

    return customerMap.values.toList();
  }
}
