import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/InventoryOperation/Home/inventory_home_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/accounting_home_screen.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/home/<USER>';
import 'package:inventory_application/screens/reports/reports_menu_screen.dart';
import 'package:inventory_application/screens/setting/setting_page.dart';
import './layout.dart' hide BottomNavbarItems;

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  BottomNavbarItems selectedIndex = BottomNavbarItems.invoices;
  Widget body = const InvoicesHomeScreen();

  @override
  Widget build(BuildContext context) {
    return HomeLayout(
      child: SizedBox(
        height: context.height,
        child: Stack(
          children: [
            body,
            Positioned(
              bottom: 0,
              child: SizedBox(
                height: 80,
                width: context.width,
                child: BottomNavbarWidget(
                  onChange: (index) {
                    selectedIndex = BottomNavbarItems.values[index];
                    if (index == 0) {
                      setState(() {
                        body = const InvoicesHomeScreen();
                      });
                    } else if (index == 1) {
                      setState(() {
                        body = const InventoryHomeScreen();
                      });
                    } else if (index == 2) {
                      setState(() {
                        body = const AccountingHomeScreen();
                      });
                    } else if (index == 3) {
                      setState(() {
                        body = const ReportsMenuScreen();
                      });
                    }
                  },
                  selectedBottomNavbarItem: selectedIndex,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
