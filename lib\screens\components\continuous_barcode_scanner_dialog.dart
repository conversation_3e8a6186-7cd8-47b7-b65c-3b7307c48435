import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/barcode_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:provider/provider.dart';

class ContinuousBarcodeScannerDialog extends StatefulWidget {
  final Function(String) onBarcodeDetected;

  const ContinuousBarcodeScannerDialog({
    super.key,
    required this.onBarcodeDetected,
  });

  @override
  // ignore: library_private_types_in_public_api
  _ContinuousBarcodeScannerDialogState createState() =>
      _ContinuousBarcodeScannerDialogState();
}

class _ContinuousBarcodeScannerDialogState
    extends State<ContinuousBarcodeScannerDialog> {
  final MobileScannerController _scannerController = MobileScannerController();
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isFlashOn = false;
  bool _isFrontCamera = false;
  String? _lastScannedBarcode;
  String? _statusMessage;
  bool _isProcessing = false;
  int _scannedCount = 0;
  final TextEditingController _barcodeInputController = TextEditingController();
  late BarcodeController _barcodeController;
  bool _useExternalDevice = false;
  String? _activeDeviceName;
  final FocusNode _inputFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initBarcodeController();
    });
  }

  Future<void> _initBarcodeController() async {
    _barcodeController = Provider.of<BarcodeController>(context, listen: false);
    await _barcodeController.loadSettings();
    setState(() {
      _useExternalDevice = _barcodeController.shouldUseExternalReader();
      _activeDeviceName = _barcodeController.selectedDevice;
    });

    // If using external device, focus on the text field immediately
    if (_useExternalDevice) {
      FocusScope.of(context).requestFocus(_inputFocusNode);
    }
  }

  @override
  void dispose() {
    if (!Platform.isWindows && !_useExternalDevice) {
      _scannerController.dispose();
    }
    _barcodeInputController.dispose();
    _audioPlayer.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  // Play beep sound when a barcode is scanned successfully
  Future<void> _playBeepSound() async {
    try {
      await _audioPlayer.play(AssetSource('sounds/beep.mp3'));
    } catch (e) {
      debugPrint('Error playing beep sound: $e');
    }
  }

  // Provide haptic feedback when a barcode is scanned
  void _vibrateSuccess() {
    if (!Platform.isWindows) {
      HapticFeedback.heavyImpact();
    }
  }

  // Process a barcode (used by both camera scanner and manual input)
  Future<void> _processBarcode(String code) async {
    if (code.isEmpty || _isProcessing) return;

    // Check if this is the same barcode we just scanned
    if (code == _lastScannedBarcode && _isProcessing) {
      return;
    }

    setState(() {
      _isProcessing = true;
      _lastScannedBarcode = code;
      _statusMessage = T('Processing barcode: ') + code;
    });

    try {
      // Call the callback function to process the barcode
      final result = await widget.onBarcodeDetected(code);

      if (result == true) {
        setState(() {
          _scannedCount++;
          _statusMessage = T('Product added successfully!');
        });

        // Provide haptic feedback when a barcode is scanned
        _vibrateSuccess();

        // Play beep sound for successful scan
        await _playBeepSound();

        // Clear the input field if using external device
        if (_useExternalDevice) {
          _barcodeInputController.clear();
        }
      } else {
        setState(() {
          _statusMessage = T('Product not found for barcode: ') + code;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = T('Error processing barcode: ') + e.toString();
      });
    } finally {
      // Reset processing flag after a delay to prevent multiple scans
      // but keep the last scanned barcode to prevent immediate rescans
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          setState(() {
            _isProcessing = false;
            // Refocus the input field if using external device
            if (_useExternalDevice) {
              FocusScope.of(context).requestFocus(_inputFocusNode);
            }
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if external barcode reader is enabled
    if (_useExternalDevice) {
      return _buildExternalDeviceDialog();
    }

    // Windows-specific implementation without external device
    if (Platform.isWindows) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        backgroundColor: Colors.white,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T('Barcode Input'),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                          color: Colors.black87,
                        ),
                      ),
                      if (_scannedCount > 0)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: context.onPrimary,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                T('Scanned: ') + _scannedCount.toString(),
                                style: TextStyle(
                                  color: context.onPrimary,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: T('Close Scanner'),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Manual Input Field
              TextField(
                controller: _barcodeInputController,
                decoration: InputDecoration(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: context.newPrimaryColor,
                      width: 2,
                    ),
                  ),
                  labelText: T('Barcode'),
                  hintText: T('Enter barcode number'),
                  labelStyle: TextStyle(
                    color: Colors.grey.shade700,
                    fontSize: 16,
                  ),
                  prefixIcon: Icon(
                    Icons.qr_code,
                    color: context.newPrimaryColor.withOpacity(0.7),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      Icons.clear,
                      color: Colors.grey.shade600,
                      size: 18,
                    ),
                    onPressed: () => _barcodeInputController.clear(),
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                ),
                keyboardType: TextInputType.text,
                autofocus: true,
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _processBarcode(value);
                    _barcodeInputController.clear();
                  }
                },
              ),

              const SizedBox(height: 16),

              // Status Message
              if (_statusMessage != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: _statusMessage!.contains('successfully')
                        ? context.onPrimary.withOpacity(0.1)
                        : _statusMessage!.contains('Processing')
                            ? context.newInfoColor.withOpacity(0.1)
                            : context.onPrimary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _statusMessage!.contains('successfully')
                          ? context.onPrimary
                          : _statusMessage!.contains('Processing')
                              ? context.newInfoColor
                              : context.onPrimary,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _statusMessage!.contains('successfully')
                            ? Icons.check_circle
                            : _statusMessage!.contains('Processing')
                                ? Icons.hourglass_top
                                : Icons.error,
                        color: _statusMessage!.contains('successfully')
                            ? context.onPrimary
                            : _statusMessage!.contains('Processing')
                                ? context.newInfoColor
                                : context.onPrimary,
                        size: 20,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          _statusMessage!,
                          style: TextStyle(
                            color: _statusMessage!.contains('successfully')
                                ? context.onPrimary
                                : _statusMessage!.contains('Processing')
                                    ? context.newInfoColor
                                    : context.onPrimary,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade200,
                        foregroundColor: Colors.black87,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: Text(
                        T('Cancel'),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (_barcodeInputController.text.isNotEmpty) {
                          _processBarcode(_barcodeInputController.text);
                          _barcodeInputController.clear();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.newPrimaryColor,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: Text(
                        T('Add Product'),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    // Mobile implementation (camera-based)
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: Container(
        padding: const EdgeInsets.all(0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 12),
              decoration: BoxDecoration(
                color: context.newPrimaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T('Scan Barcode'),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                          color: Colors.white,
                        ),
                      ),
                      if (_scannedCount > 0)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.check_circle,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                T('Products added: ') +
                                    _scannedCount.toString(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  // Camera controls
                  Row(
                    children: [
                      // Flash toggle
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _isFlashOn = !_isFlashOn;
                            _scannerController.toggleTorch();
                          });
                        },
                        icon: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _isFlashOn ? Icons.flash_on : Icons.flash_off,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        tooltip: T("Toggle Flash"),
                      ),
                      // Camera toggle
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _isFrontCamera = !_isFrontCamera;
                            _scannerController.switchCamera();
                          });
                        },
                        icon: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.cameraswitch,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        tooltip: T("Switch Camera"),
                      ),
                      // Close button
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Camera Preview
            Container(
              height: 300,
              width: double.infinity,
              child: Stack(
                children: [
                  // Camera Scanner
                  MobileScanner(
                    controller: _scannerController,
                    fit: BoxFit.cover,
                    onDetect: (capture) {
                      final List<Barcode> barcodes = capture.barcodes;
                      if (barcodes.isNotEmpty && !_isProcessing) {
                        final String? code = barcodes[0].rawValue;
                        if (code != null) {
                          _processBarcode(code);
                        }
                      }
                    },
                  ),
                  // Overlay with scanning animation
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: context.newPrimaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                  // Scanning indicator
                  Center(
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _isProcessing
                              ? context.onPrimary
                              : context.newPrimaryColor,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (_isProcessing)
                            Icon(
                              Icons.check_circle,
                              color: context.onPrimary,
                              size: 36,
                            )
                          else
                            SizedBox(
                              width: 36,
                              height: 36,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  context.newPrimaryColor,
                                ),
                                strokeWidth: 2,
                              ),
                            ),
                          const SizedBox(height: 12),
                          Text(
                            _isProcessing
                                ? T('Processing...')
                                : T('Scanning...'),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.5),
                                  blurRadius: 4,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Status and Input Section
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Status message
                  if (_statusMessage != null)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: _statusMessage!.contains('successfully')
                            ? context.onPrimary.withOpacity(0.1)
                            : _statusMessage!.contains('Processing')
                                ? context.newInfoColor.withOpacity(0.1)
                                : context.onPrimary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _statusMessage!.contains('successfully')
                              ? context.onPrimary
                              : _statusMessage!.contains('Processing')
                                  ? context.newInfoColor
                                  : context.onPrimary,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _statusMessage!.contains('successfully')
                                ? Icons.check_circle
                                : _statusMessage!.contains('Processing')
                                    ? Icons.hourglass_top
                                    : Icons.error,
                            color: _statusMessage!.contains('successfully')
                                ? context.onPrimary
                                : _statusMessage!.contains('Processing')
                                    ? context.newInfoColor
                                    : context.onPrimary,
                            size: 20,
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              _statusMessage!,
                              style: TextStyle(
                                color: _statusMessage!.contains('successfully')
                                    ? context.onPrimary
                                    : _statusMessage!.contains('Processing')
                                        ? context.newInfoColor
                                        : context.onPrimary,
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Manual input section
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _barcodeInputController,
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                                width: 1,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                                width: 1,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: context.newPrimaryColor,
                                width: 2,
                              ),
                            ),
                            hintText: T('Enter barcode manually'),
                            prefixIcon: Icon(
                              Icons.qr_code,
                              color: context.newPrimaryColor.withOpacity(0.7),
                            ),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                          ),
                          onSubmitted: (value) {
                            if (value.isNotEmpty) {
                              _processBarcode(value);
                              _barcodeInputController.clear();
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          if (_barcodeInputController.text.isNotEmpty) {
                            _processBarcode(_barcodeInputController.text);
                            _barcodeInputController.clear();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.newPrimaryColor,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 16, horizontal: 16),
                        ),
                        child: const Icon(Icons.search),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Done button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.newSecondaryColor,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: Text(
                        T('Done'),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // New method to build the dialog for external barcode device
  Widget _buildExternalDeviceDialog() {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      backgroundColor: Colors.white,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with device info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T('External Barcode Reader'),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.usb,
                            size: 16,
                            color: Colors.green.shade700,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _activeDeviceName ?? T('Unknown Device'),
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                                fontStyle: FontStyle.italic,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: T('Close Scanner'),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Status message
            if (_statusMessage != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isProcessing
                      ? Colors.blue.shade50
                      : _scannedCount > 0
                          ? Colors.green.shade50
                          : Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isProcessing
                        ? Colors.blue.shade200
                        : _scannedCount > 0
                            ? Colors.green.shade200
                            : Colors.orange.shade200,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isProcessing
                          ? Icons.hourglass_top
                          : _scannedCount > 0
                              ? Icons.check_circle_outline
                              : Icons.info_outline,
                      color: _isProcessing
                          ? Colors.blue.shade700
                          : _scannedCount > 0
                              ? Colors.green.shade700
                              : Colors.orange.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _statusMessage!,
                        style: TextStyle(
                          fontSize: 14,
                          color: _isProcessing
                              ? Colors.blue.shade700
                              : _scannedCount > 0
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // Barcode input field (focused to capture external scanner input)
            TextField(
              controller: _barcodeInputController,
              focusNode: _inputFocusNode,
              autofocus: true,
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  _processBarcode(value);
                }
              },
              decoration: InputDecoration(
                labelText: T('Scan barcode with external reader'),
                hintText: T('Barcode will appear here'),
                prefixIcon: const Icon(Icons.qr_code_scanner),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: context.newPrimaryColor,
                    width: 2,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Count display
            if (_scannedCount > 0)
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: context.newPrimaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      T('Items scanned: ') + _scannedCount.toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: context.newPrimaryColor,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // Help text
            Text(
              T('Point your external barcode reader at a barcode. The input will be processed automatically.'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
