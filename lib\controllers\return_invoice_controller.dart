import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/main.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

class ReturnInvoiceController with ChangeNotifier {
  List<ProductDTO> selectedReturnInvoiceProduct = [];
  String? returnInvoiceCode;

  InvoiceDto invoice = InvoiceDto();
//---------------------------------------------------------------------------
// List<int>
  void addProductToSelectedList(ProductDTO model) {
    // IMPORTANT: Apply default unit first, before any other processing
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
      navigatorKey.currentContext!,
      listen: false,
    );

    // Force apply default unit if default is set (regardless of whether product already has a unit)
    if (invoiceSettingsController.defaultUnitId != null) {
      model.uniteId = invoiceSettingsController.defaultUnitId;
      model.uniteName = invoiceSettingsController.defaultUnitName;
    }

    // Apply default warehouse if available
    if (invoice.warehouseId != null) {
      model.warehouseId = invoice.warehouseId;
      model.warehouseName = invoice.warehouseName ?? "";
    }

    // Create a copy of the model to ensure it's a completely separate object
    ProductDTO newProduct = ProductDTO(
        id: model.id,
        title: model.title,
        barcode: model.barcode,
        barcodeName: model.barcodeName,
        code: model.code,
        description: model.description,
        price: model.price,
        total: model.total,
        discountValue: model.discountValue,
        stock: model.stock,
        uniteId: model.uniteId,
        uniteName: model.uniteName,
        category: model.category,
        quantity: model.quantity,
        warehouseId: model.warehouseId,
        warehouseName: model.warehouseName,
        thumbnail: model.thumbnail,
        warehouse: model.warehouse,
        units: model.units,
        barcodes: model.barcodes,
        hasSelectedAttributes: model.hasSelectedAttributes,
        virtualProductId: model.virtualProductId,
        itemAttributes: model.itemAttributes);

    // Check if this exact product + attributes combination already exists
    int existingIndex = -1;
    if (model.virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      existingIndex = selectedReturnInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == model.virtualProductId,
      );
    } else {
      // Otherwise, fall back to checking by regular ID
      existingIndex = selectedReturnInvoiceProduct.indexWhere(
        (element) =>
            element.id == model.id &&
            element.barcode == model.barcode &&
            !element.hasSelectedAttributes,
      );
    }

    // If we found the exact same product, increase its quantity instead of adding a new one
    if (existingIndex >= 0) {
      // Create a new list to trigger UI update
      List<ProductDTO> updatedList = List.from(selectedReturnInvoiceProduct);

      double newQuantity = (updatedList[existingIndex].quantity ?? 1) +
          (newProduct.quantity ?? 1);

      // Update the quantity
      updatedList[existingIndex].quantity = newQuantity;

      // Update the total
      double price = updatedList[existingIndex].price ?? 0;
      updatedList[existingIndex].total = price * newQuantity;

      // Replace the list with the updated one
      selectedReturnInvoiceProduct = updatedList;
    } else {
      // Add as a new item
      selectedReturnInvoiceProduct.add(newProduct);
    }

    calculateInvoiceTotal();
    notifyListeners();
  }

//---------------------------------------------------------------------------
  void deleteProductFromSelectedList(int id, [String? virtualProductId]) {
    // Create a new list without the product to be deleted
    List<ProductDTO> updatedList;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find and remove the product
      updatedList = selectedReturnInvoiceProduct
          .where((element) => element.virtualProductId != virtualProductId)
          .toList();
    } else {
      // Otherwise use the regular product ID
      updatedList = selectedReturnInvoiceProduct
          .where((element) => element.id != id)
          .toList();
    }

    // Replace the list with the updated one
    selectedReturnInvoiceProduct = updatedList;

    // Recalculate invoice total
    calculateInvoiceTotal();

    // Notify listeners
    notifyListeners();
  }

//---------------------------------------------------------------------------
  void updateProductQuantity(int id, double quantity,
      [String? virtualProductId]) async {
    // Create a new list to trigger UI update
    List<ProductDTO> updatedList = List.from(selectedReturnInvoiceProduct);

    // Find the product index
    int productIndex;
    if (virtualProductId != null) {
      productIndex = updatedList.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      productIndex = updatedList.indexWhere((element) => element.id == id);
    }

    if (productIndex >= 0) {
      // Update the quantity
      updatedList[productIndex].quantity = quantity;

      // Calculate and update the total immediately
      double price = updatedList[productIndex].price ?? 0;
      updatedList[productIndex].total = price * quantity;

      // Replace the list with the updated one
      selectedReturnInvoiceProduct = updatedList;

      // Calculate invoice total
      calculateInvoiceTotal();

      // Notify listeners for UI update
      notifyListeners();

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 100));

      // Notify again to ensure UI is updated
      notifyListeners();
    }
  }

//---------------------------------------------------------------------------
  void updateProductPrice(int id, double price, [String? virtualProductId]) {
    // Create a new list to trigger UI update
    List<ProductDTO> updatedList = List.from(selectedReturnInvoiceProduct);

    // Find the product index
    int productIndex;
    if (virtualProductId != null) {
      productIndex = updatedList.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      productIndex = updatedList.indexWhere((element) => element.id == id);
    }

    if (productIndex >= 0) {
      // Update the price
      updatedList[productIndex].price = price;

      // Update the total
      updatedList[productIndex].total =
          price * (updatedList[productIndex].quantity ?? 1);

      // Replace the list with the updated one
      selectedReturnInvoiceProduct = updatedList;

      // Recalculate invoice total
      calculateInvoiceTotal();

      // Notify listeners
      notifyListeners();
    }
  }

//---------------------------------------------------------------------------
  void updateProductUnit(int id, ItemPriceDTO unit,
      [String? virtualProductId]) {
    // Create a new list to trigger UI update
    List<ProductDTO> updatedList = List.from(selectedReturnInvoiceProduct);

    // Find the product index
    int productIndex;
    if (virtualProductId != null) {
      productIndex = updatedList.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      productIndex = updatedList.indexWhere((element) => element.id == id);
    }

    if (productIndex >= 0) {
      // Only update the unit ID and name, not the price
      updatedList[productIndex].uniteId = unit.unitID;
      updatedList[productIndex].uniteName = unit.unitName;

      // Keep the existing price and total
      double price = updatedList[productIndex].price ?? 0;
      double quantity = updatedList[productIndex].quantity ?? 1;
      updatedList[productIndex].total = price * quantity;

      // Replace the list with the updated one
      selectedReturnInvoiceProduct = updatedList;

      // Recalculate invoice total
      calculateInvoiceTotal();

      // Notify listeners
      notifyListeners();
    }
  }

//---------------------------------------------------------------------------
  Future<dynamic> getItemByBarcode({String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) {
        return false;
      }

      // Notify that we're starting to process
      notifyListeners();

      // Use getItemByBarcodeWithWriting instead of getItemByBarcode to handle attributes and final barcodes
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (result != null) {
        // If the product has attributes but they haven't been selected yet,
        // and it's not a final barcode, return the product so the UI can show the attribute dialog
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          return result; // Return the product so the UI can show the attribute selection dialog
        }

        // First check if we already have this product with the same barcode
        int existingIndex = -1;

        // Handle products with virtual ID (products with attributes)
        if (result.virtualProductId != null) {
          existingIndex = selectedReturnInvoiceProduct.indexWhere(
            (element) => element.virtualProductId == result.virtualProductId,
          );
        } else {
          // UPDATED LOGIC: Match by exact barcode for both final and non-final barcodes
          for (int i = 0; i < selectedReturnInvoiceProduct.length; i++) {
            var product = selectedReturnInvoiceProduct[i];

            // IMPORTANT: For FINAL barcodes, we need to match by exact barcode
            // regardless of hasSelectedAttributes status
            if (product.barcode != null &&
                result.barcode != null &&
                product.barcode!.isNotEmpty &&
                result.barcode!.isNotEmpty &&
                product.barcode == barcode) {
              // For final barcodes (hasSelectedAttributes = true), match by exact barcode
              if (result.hasSelectedAttributes == true &&
                  product.hasSelectedAttributes == true) {
                existingIndex = i;
                break;
              }
              // For non-final barcodes (hasSelectedAttributes = false), also match by exact barcode
              else if (result.hasSelectedAttributes != true &&
                  product.hasSelectedAttributes != true) {
                existingIndex = i;
                break;
              }
            }
          }

          // If no barcode match found, check by product ID (only if both have no barcode)
          if (existingIndex == -1) {
            for (int i = 0; i < selectedReturnInvoiceProduct.length; i++) {
              var product = selectedReturnInvoiceProduct[i];

              // Skip items that have virtual IDs
              if (product.virtualProductId != null) {
                continue;
              }

              // Only match by ID if both products have no barcode or empty barcode
              bool bothHaveNoBarcode =
                  (result.barcode == null || result.barcode!.isEmpty) &&
                      (product.barcode == null || product.barcode!.isEmpty);

              if (bothHaveNoBarcode &&
                  product.id != null &&
                  result.id != null &&
                  product.id == result.id &&
                  result.hasSelectedAttributes ==
                      product.hasSelectedAttributes) {
                existingIndex = i;
                break;
              }
            }
          }
        }

        // If we found an existing product, increase its quantity
        if (existingIndex >= 0) {
          // Create a new list to trigger UI update
          List<ProductDTO> updatedList =
              List.from(selectedReturnInvoiceProduct);

          // Update the quantity
          double newQuantity = (updatedList[existingIndex].quantity ?? 1) + 1;
          updatedList[existingIndex].quantity = newQuantity;

          // Update the total
          double price = updatedList[existingIndex].price ?? 0;
          updatedList[existingIndex].total = price * newQuantity;

          // Replace the list with the updated one
          selectedReturnInvoiceProduct = updatedList;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        } else {
          // This is a new product, add it to the list
          // Apply default unit if default is set
          final invoiceSettingsController =
              Provider.of<InvoiceSettingsController>(
            navigatorKey.currentContext!,
            listen: false,
          );

          // Force apply default unit if default is set
          if (invoiceSettingsController.defaultUnitId != null) {
            result.uniteId = invoiceSettingsController.defaultUnitId;
            result.uniteName = invoiceSettingsController.defaultUnitName;
          }

          // Set warehouse if available
          if (invoice.warehouseId != null) {
            result.warehouseId = invoice.warehouseId;
            result.warehouseName = invoice.warehouseName ?? "";
          }

          // IMPORTANT: For products with final barcodes (hasSelectedAttributes = true)
          // that don't have a virtualProductId, generate one to ensure proper identification in the UI
          if (result.virtualProductId == null &&
              result.hasSelectedAttributes == true &&
              result.barcode != null &&
              result.barcode!.isNotEmpty) {
            // Generate a consistent virtual product ID based on barcode for final barcodes
            result.virtualProductId = '${result.id}_final_${result.barcode}';
          }

          // Ensure quantity is at least 1
          result.quantity = result.quantity ?? 1;

          // Calculate total
          result.total = (result.price ?? 0) * result.quantity!;

          // Create a new list with the new product
          List<ProductDTO> updatedList =
              List.from(selectedReturnInvoiceProduct);
          updatedList.add(result);

          // Replace the list with the updated one
          selectedReturnInvoiceProduct = updatedList;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        }
      }

      return false;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return false;
    }
  }

//---------------------------------------------------------------------------
  void setProductDiscount(int id, double value) {
    var product = selectedReturnInvoiceProduct.firstWhere(
      (element) => element.id == id,
    );
    product.discountValue = value;
    calculateInvoiceTotal();
    notifyListeners();
  }

//---------------------------------------------------------------------------
  void setInvoiceDiscount(InvoiceDiscountType type, double value) {
    if (type == InvoiceDiscountType.persantage) {
      invoice.discountType = InvoiceDiscountType.persantage;
      invoice.discountValue = 0;
      invoice.discountPercentage = value;
    } else {
      invoice.discountType = InvoiceDiscountType.value;
      invoice.discountValue = value;
      invoice.discountPercentage = 0;
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

//---------------------------------------------------------------------------

  void calculateInvoiceTotal() {
    double total = 0;
    double totalQuantity = 0;
    double totalDiscount = 0;

    for (var product in selectedReturnInvoiceProduct) {
      total += product.total ?? 0;
      totalQuantity += product.quantity ?? 0;
    }

    // Update the invoice values
    invoice.total = total;
    invoice.productsTotalCount = totalQuantity;

    // Calculate discount
    if (invoice.discountType == InvoiceDiscountType.persantage) {
      double discountPercentage = invoice.discountPercentage ?? 0;
      totalDiscount = (total * discountPercentage) / 100;
    } else {
      totalDiscount = invoice.discountValue ?? 0;
    }

    invoice.totalDiscount = totalDiscount;
    invoice.totalAfterDiscount = total - totalDiscount;

    // Set payment amount to match the net total
    invoice.paymentValue = invoice.totalAfterDiscount;

    // Ensure UI updates with immediate notification
    notifyListeners();

    // Add a small delay and notify again to ensure UI updates
    Future.delayed(const Duration(milliseconds: 100), () {
      notifyListeners();
    });
  }

//---------------------------------------------------------------------------
  void setProductWarehouse(int id, int warehouseId, String warehouseName,
      [String? virtualProductId]) {
    ProductDTO product;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      product = selectedReturnInvoiceProduct.firstWhere(
        (element) => element.virtualProductId == virtualProductId,
        orElse: () => selectedReturnInvoiceProduct.firstWhere(
          (element) => element.id == id,
          orElse: () => ProductDTO(),
        ),
      );
    } else {
      // Otherwise use the regular product ID
      product = selectedReturnInvoiceProduct.firstWhere(
        (element) => element.id == id,
        orElse: () => ProductDTO(),
      );
    }

    if (product.id != null) {
      product.warehouseId = warehouseId;
      product.warehouseName = warehouseName;
      notifyListeners();
    }
  }

//---------------------------------------------------------------------------
  Future<String> getRetrunInvoiceNumber() async {
    try {
      var number = await CounterGenerator.getReturnInvoiceNextCounter();
      invoice.appReferanceCode ??= number;

      notifyListeners();
      // var url = '/Sales/GetCode?transactions_type=RetrunInvoice';

      // var result = await Api.getOne(action: url);
      // if (result != null) {
      //   if (result.isSuccess) {
      //     returnInvoiceCode = result.data;
      //     invoice.invoiceCode = result.data;
      //     notifyListeners();
      //   }
      // }
      return invoice.appReferanceCode ?? "";
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------
  bool setSaleInvoiceToReturnModel(InvoiceDto model) {
    model.connectedToInvoiceCode = model.invoiceCode;
    model.connectedToInvoiceLocalCode = model.appReferanceCode;
    model.appReferanceCode = invoice.appReferanceCode;
    model.invoiceCode = "*";
    model.id = 0;

    invoice = model;

    selectedReturnInvoiceProduct = model.salesItems ?? [];
    notifyListeners();
    calculateInvoiceTotal();
    return true;
  }

  //---------------------------------------------------------- -----------------
  Future<ResponseResultModel> saveReturnInvoice() async {
    try {
      var url = '/Sales/Manage';
      final db = InvoiceController();

      var mapedInvoice = mapInvoiceDto(invoice, selectedReturnInvoiceProduct);

      // mapedInvoice.paidAmount ??= 0.0;

      mapedInvoice.transactionsType = "RetrunInvoice";
      mapedInvoice.entryDateFormated =
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());
      if (mapedInvoice.iD == null || (mapedInvoice.iD ?? 0) == 0) {
        mapedInvoice.entryDate = DateTime.now();
        mapedInvoice.code = "*";
      }

      if (mapedInvoice.appReferanceCode == null ||
          mapedInvoice.appReferanceCode == "") {
        mapedInvoice.appReferanceCode = await getRetrunInvoiceNumber();
      } else {
        if (await CounterGenerator.checkIfSaleInvoiceCounterUsed(
            mapedInvoice.appReferanceCode ?? "")) {
          mapedInvoice.appReferanceCode = await getRetrunInvoiceNumber();
        }
      }

      // mapedInvoice.iD = 0;
      // mapedInvoice.vATPercent = 0;

      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateInvoice(SqlLiteInvoiceModel(
                data: jsonEncode(mapedInvoice.toJson()),
                status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                id: null,
                localCode: mapedInvoice.appReferanceCode,
                type: getInvoiceTypeName(SalesType.RetrunInvoice))
            .toJson());
        invoice = InvoiceDto();
        selectedReturnInvoiceProduct.clear();
        if (mapedInvoice.iD == 0) {
          await CounterGenerator.setReturnInvoiceCounter();
        }
        notifyListeners();

        return ResponseResultModel(
            isSuccess: true, data: InvoiceResponseDto(localId: localId));
      }

      var result = await Api.post(
        action: url,
        body: mapedInvoice.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          if (mapedInvoice.iD == 0) {
            await CounterGenerator.setInvoicesCounterInServer();
            await CounterGenerator.setReturnInvoiceCounter();
          }
          var response = InvoiceResponseDto.fromJson(result.data);
          mapedInvoice.iD = response.id;
          mapedInvoice.code = response.code;
          var localId = await db.insertOrUpdateInvoice(SqlLiteInvoiceModel(
                  data: jsonEncode(mapedInvoice.toJson()),
                  status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                  id: null,
                  localCode: mapedInvoice.appReferanceCode,
                  type: getInvoiceTypeName(SalesType.RetrunInvoice))
              .toJson());
          response.localId = localId;
          invoice = InvoiceDto();
          selectedReturnInvoiceProduct.clear();
          await db.saveReturnToEcommerce(mapedInvoice);
          notifyListeners();
          return ResponseResultModel(data: response, isSuccess: true);
        }
      }
      return result ?? ResponseResultModel(isSuccess: false);
    } catch (e) {
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }

//---------------------------------------------------------------------------

  Future<bool> repeatReturnInvoice() async {
    invoice.id = 0;
    invoice.appReferanceCode =
        await CounterGenerator.getReturnInvoiceNextCounter();
    var result = await saveReturnInvoice();
    invoice = InvoiceDto();
    selectedReturnInvoiceProduct = [];
    return result.isSuccess;
  }

//---------------------------------------------------------------------------
  void notifyListenersFuntion() {
    notifyListeners();
  }
}
