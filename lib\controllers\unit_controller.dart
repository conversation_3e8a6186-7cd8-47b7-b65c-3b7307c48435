import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:sqflite/sqflite.dart';

class UnitController with ChangeNotifier {
  List<ComboBoxDataModel> units = [];
  int _fetchedUnitCount = 0;
  bool runningSyncization = false;

  // Getter for the fetchedUnitCount
  int get fetchedUnitCount => _fetchedUnitCount;

  /// Fetches units from local database first, if not available then from server
  Future<void> fetchUnits() async {
    try {
      var fromLocalDatabase = await getUnits();
      if (fromLocalDatabase.isNotEmpty) {
        units.clear();
        for (var element in fromLocalDatabase) {
          units.add(ComboBoxDataModel.fromJson(element));
        }
        notifyListeners();
        return;
      }

      units.clear();
      var url = '/ItemsCategory/GetAllUnits';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            setUnit(element["ID"], element["Name"]);
            units.add(ComboBoxDataModel(element["ID"], element["Name"]));
          }
        }
      }

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print("Error fetching units: $e");
      }
    }
  }

  /// Fetches units from server with pagination
  Future<void> fetchUnitsFromServer() async {
    try {
      units.clear();
      runningSyncization = true;
      _fetchedUnitCount = 0;

      var url = '/ItemsCategory/GetAllUnits';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          await setUnit(element["ID"], element["Name"]);
          units.add(ComboBoxDataModel(element["ID"], element["Name"]));
          _fetchedUnitCount++;
        }
      }

      runningSyncization = false;
      notifyListeners();
    } catch (e) {
      runningSyncization = false;
      if (kDebugMode) {
        print("Error fetching units from server: $e");
      }
    }
  }

  /// Saves a unit to the local database
  Future<int> setUnit(int id, String name) async {
    final db = await DatabaseHelper().database;

    // Use insert with conflict resolution to replace if id exists
    final result = await db.insert(
      'Units',
      {'id': id, 'name': name},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return result;
  }

  /// Gets all units from the local database
  Future<List<Map<String, dynamic>>> getUnits() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('Units');
    return result;
  }

  /// Gets the count of units in the local database
  Future<int> getUnitCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery('SELECT COUNT(*) as count FROM Units');
    int count = Sqflite.firstIntValue(result) ?? 0;
    return count;
  }

  /// Clears the units table and refetches data from server
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await DatabaseHelper().database;

      await db.transaction((txn) async {
        await txn.delete('Units');
      });

      await fetchUnitsFromServer();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print("Error clearing and refetching units: $e");
      }
      return false;
    }
  }
}
