import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/inverntory/invoices/products/widgets/invoice_product_categories_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/products/widgets/invoice_product_search_widget.dart';
import 'package:inventory_application/screens/inverntory/products/widgets/product_details_screen.dart';
import 'package:inventory_application/screens/inverntory/products/widgets/product_list_items_widget.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ProductsListScreen extends StatefulWidget {
  const ProductsListScreen({super.key});

  @override
  State<ProductsListScreen> createState() => _ProductsListScreenState();
}

class _ProductsListScreenState extends State<ProductsListScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  int categoryId = 0;
  String _searchQuery = "";

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
  }

  //==================================================//
  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  //==================================================//
  void _onRefresh() async {
    await Provider.of<ProductController>(context, listen: false).getItems(
        resetAndRefresh: true, categoryId: categoryId, search: _searchQuery);
    _refreshController.refreshCompleted();
  }

  //==================================================//
  void _onLoading() async {
    await Provider.of<ProductController>(context, listen: false)
        .getItems(categoryId: categoryId, search: _searchQuery);
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<ProductController>(context).realProductList;

    return ApplicationLayout(
      child: Column(
        children: [
          // Header
          Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and Counter
                CommonHeader(
                  icon: Icons.receipt_long,
                  title: T("Products"),
                ),

                // Search Box
                invoiceProductSearchWidget(
                  context: context,
                  onchange: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    if (_searchQuery.length > 2) _onRefresh();
                  },
                ),

                // Categories Filter
                InvoiceProductCategoriesWidget(
                  cateogryId: categoryId,
                  onSelecte: (id) {
                    setState(() {
                      categoryId = id;
                    });
                    _onRefresh();
                  },
                ),
              ],
            ),
          ),

          // Products List
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: data.isEmpty
                    ? _buildEmptyState()
                    : SmartRefresher(
                        enablePullDown: true,
                        enablePullUp: true,
                        header: WaterDropHeader(
                          waterDropColor: context.newPrimaryColor,
                        ),
                        footer: CustomFooter(
                          builder: (context, mode) {
                            Widget body;
                            if (mode == LoadStatus.loading) {
                              body = const CupertinoActivityIndicator();
                            } else if (mode == LoadStatus.idle) {
                              body = Text(
                                T("Pull up to load more"),
                                style: TextStyle(color: context.newTextColor),
                              );
                            } else if (mode == LoadStatus.failed) {
                              body = Text(
                                T("Load failed! Click to retry"),
                                style: TextStyle(color: context.newTextColor),
                              );
                            } else if (mode == LoadStatus.canLoading) {
                              body = Text(
                                T("Release to load more"),
                                style: TextStyle(color: context.newTextColor),
                              );
                            } else {
                              body = Text(
                                T("No more products"),
                                style: TextStyle(color: context.newTextColor),
                              );
                            }
                            return SizedBox(
                              height: 55.0,
                              child: Center(child: body),
                            );
                          },
                        ),
                        controller: _refreshController,
                        onRefresh: _onRefresh,
                        onLoading: _onLoading,
                        child: ListView.separated(
                          padding: const EdgeInsets.all(12),
                          scrollDirection: Axis.vertical,
                          itemCount: data.length,
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 8),
                          itemBuilder: (context, index) {
                            final animation =
                                Tween<double>(begin: 0.0, end: 1.0).animate(
                              CurvedAnimation(
                                parent: animationController!,
                                curve: Interval((1 / data.length) * index, 1.0,
                                    curve: Curves.fastOutSlowIn),
                              ),
                            );
                            animationController!.forward();

                            return FadeTransition(
                              opacity: animation,
                              child: Transform(
                                transform: Matrix4.translationValues(
                                    0.0, 30 * (1.0 - animation.value), 0.0),
                                child: InkWell(
                                  onTap: () {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            ProductDetailsScreen(
                                          data: data[index],
                                        ),
                                      ),
                                    );
                                  },
                                  child: ProductListItemsWidget(
                                    data: data[index],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 70,
            color: context.newPrimaryColor.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            T("No products found"),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.newTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("Try changing your search or category filter"),
            style: TextStyle(
              color: context.newTextColor.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
