import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/inventory_operation_stocktaking_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_balance_dto.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_comparison_item_dto.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_draft_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';

import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:flutter/services.dart';

class InventoryComparisonScreen extends StatefulWidget {
  final StocktakingDraftDTO stocktakingData;

  const InventoryComparisonScreen({
    super.key,
    required this.stocktakingData,
  });

  @override
  _InventoryComparisonScreenState createState() =>
      _InventoryComparisonScreenState();
}

class _InventoryComparisonScreenState extends State<InventoryComparisonScreen> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<ComparisonItemDto> _comparisonItems = [];
  bool _isLoading = true;
  String _loadingMessage = "Loading...";
  bool _isProcessing = false;

  // Selection state
  Set<int> _selectedItemIds = {};
  bool _isAllSelected = false;

  String get _warehouseName =>
      widget.stocktakingData.warehouseName ?? 'Unknown';
  String get _warehouseId =>
      widget.stocktakingData.warehouseId?.toString() ?? '';

  @override
  void initState() {
    super.initState();
    _loadComparisonData();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadComparisonData() async {
    try {
      setState(() {
        _isLoading = true;
        _loadingMessage = T("Loading items...");
      });

      // Get stocktaking items directly from the DTO
      final stocktakingItems = widget.stocktakingData.items ?? [];

      if (stocktakingItems.isEmpty) {
        setState(() {
          _comparisonItems = [];
          _isLoading = false;
        });
        return;
      }

      // Extract item IDs for API call
      List<int> itemIds = stocktakingItems
          .where((item) => item.id != null)
          .map((item) => item.id!)
          .toList();

      if (itemIds.isEmpty) {
        setState(() {
          _comparisonItems = [];
          _isLoading = false;
        });
        return;
      }

      // Split item IDs into batches of 200
      const int batchSize = 200;
      List<List<int>> batches = [];
      for (int i = 0; i < itemIds.length; i += batchSize) {
        int end =
            (i + batchSize < itemIds.length) ? i + batchSize : itemIds.length;
        batches.add(itemIds.sublist(i, end));
      }

      setState(() {
        _loadingMessage =
            T("Loading server data: 0 of ${batches.length} batches...");
      });

      // Get server balances using batched requests
      final controller =
          Provider.of<InventoryOperationController>(context, listen: false);
      List<StocktakingBalanceDto> allServerBalances = [];

      // Process each batch
      for (int i = 0; i < batches.length; i++) {
        try {
          setState(() {
            _loadingMessage = T(
                "Loading server data: ${i + 1} of ${batches.length} batches...");
          });

          List<StocktakingBalanceDto> batchBalances =
              await controller.getServerBalances(_warehouseId, batches[i]);
          allServerBalances.addAll(batchBalances);

          print(
              "Processed batch ${i + 1} of ${batches.length} (${batches[i].length} items)");
        } catch (e) {
          print("Error processing batch ${i + 1}: $e");
          // Continue with other batches even if one fails
        }
      }

      setState(() {
        _loadingMessage = T("Processing comparison...");
      });

      // Create comparison items
      List<ComparisonItemDto> items = [];
      for (var stockItem in stocktakingItems) {
        if (stockItem.id == null) continue;

        final stockQuantity = stockItem.quantity ?? 0.0;
        final serverBalance = allServerBalances.firstWhere(
          (balance) => balance.itemId == stockItem.id,
          orElse: () =>
              StocktakingBalanceDto(itemId: stockItem.id, quantity: 0.0),
        );
        final serverQuantity = serverBalance.quantity ?? 0.0;

        items.add(ComparisonItemDto(
          itemId: stockItem.id!,
          itemPrice: stockItem.price ?? 0.0,
          itemName: stockItem.title ?? 'Unknown Item',
          itemCode: stockItem.code ?? '',
          stocktakingQuantity: stockQuantity,
          barcode: stockItem.barcode,
          hasSelectedAttributes: stockItem.hasSelectedAttributes,
          selectedOptionIds: stockItem.selectedOptionIds,
          serverQuantity: serverQuantity,
          difference: stockQuantity - serverQuantity,
        ));
      }

      setState(() {
        _comparisonItems = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(message: T("Error loading comparison data: $e"));
    }
  }

  void _onRefresh() async {
    await _loadComparisonData();
    _refreshController.refreshCompleted();
  }

  // Selection methods
  void _toggleItemSelection(int itemId) {
    setState(() {
      if (_selectedItemIds.contains(itemId)) {
        _selectedItemIds.remove(itemId);
      } else {
        _selectedItemIds.add(itemId);
      }
      _updateSelectAllState();
    });
  }

  void _toggleSelectAll() {
    setState(() {
      if (_isAllSelected) {
        _selectedItemIds.clear();
        _isAllSelected = false;
      } else {
        _selectedItemIds.clear();
        _selectedItemIds.addAll(_comparisonItems.map((item) => item.itemId));
        _isAllSelected = true;
      }
    });
  }

  void _updateSelectAllState() {
    _isAllSelected = _comparisonItems.isNotEmpty &&
        _selectedItemIds.length == _comparisonItems.length;
  }

  // Get selected items with differences
  List<ComparisonItemDto> _getSelectedItemsWithDifferences() {
    return _comparisonItems
        .where((item) =>
            _selectedItemIds.contains(item.itemId) && item.difference != 0)
        .toList();
  }

  // Apply quantity adjustments to server
  Future<void> _applyQuantityAdjustments() async {
    final selectedItems = _getSelectedItemsWithDifferences();
    var defultUnit =
        await Provider.of<InvoiceSettingsController>(context, listen: false)
            .defaultUnitId;

    if (selectedItems.isEmpty) {
      errorSnackBar(message: T("No items with differences selected"));
      return;
    }

    // Show confirmation dialog
    bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T("Confirm Quantity Adjustments")),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(T(
                "This will adjust quantities for ${selectedItems.length} items:")),
            const SizedBox(height: 12),
            ...selectedItems.take(5).map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    "• ${item.itemName}: ${item.difference >= 0 ? '+' : ''}${item.difference}",
                    style: const TextStyle(fontSize: 12),
                  ),
                )),
            if (selectedItems.length > 5)
              Text(T("... and ${selectedItems.length - 5} more items")),
            const SizedBox(height: 12),
            Text(
              T("This action cannot be undone!"),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(T("Cancel")),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
            ),
            child: Text(T("Apply Adjustments")),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      var result = await Provider.of<InventoryOperationStocktakingController>(
              context,
              listen: false)
          .saveInventoryStocktaking(
              mode: InventoryOperationModel(
                fromStoreID: widget.stocktakingData.warehouseId,
                storeName: widget.stocktakingData.warehouseName,
                entryDate: widget.stocktakingData.startDate,
                operationType: InventoryOperationType.Stocktaking.index,
                isPosted: false,
              ),
              items: selectedItems
                  .map((item) => InventoryOperationItems(
                        itemCode: item.itemCode,
                        quantity: item.stocktakingQuantity,
                        itemID: item.itemId,
                        balance: item.serverQuantity,
                        storeID: widget.stocktakingData.warehouseId,
                        unitID: defultUnit,
                        unitPrice: item.itemPrice,
                        selectedOptionIds: item.selectedOptionIds,
                        hasSelectedAttributes: item.hasSelectedAttributes,
                        barcode: item.barcode,
                      ))
                  .toList());

      // Call API to apply adjustments (you'll need to implement this in the controller)
      // bool success = await controller.applyQuantityAdjustments(adjustments);

      // For now, simulate the API call
      await Future.delayed(const Duration(seconds: 2));
      bool success = result.isSuccess; // Replace with actual API result

      if (success) {
        successSnackBar(
            message: T("Quantity adjustments applied successfully"));

        // Update the comparison data to reflect changes
        setState(() {
          for (int i = 0; i < _comparisonItems.length; i++) {
            final item = _comparisonItems[i];
            if (selectedItems
                .any((selected) => selected.itemId == item.itemId)) {
              // Create new item with updated quantities
              _comparisonItems[i] = ComparisonItemDto(
                barcode: item.barcode,
                hasSelectedAttributes: item.hasSelectedAttributes,
                selectedOptionIds: item.selectedOptionIds,
                itemId: item.itemId,
                itemName: item.itemName,
                itemCode: item.itemCode,
                itemPrice: item.itemPrice,
                stocktakingQuantity: item.stocktakingQuantity,
                serverQuantity: item
                    .stocktakingQuantity, // Set server quantity to match stocktaking
                difference: 0, // No difference after adjustment
              );
            }
          }
          _selectedItemIds.clear();
          _isAllSelected = false;
        });
      } else {
        errorSnackBar(message: T("Failed to apply quantity adjustments"));
      }
    } catch (e) {
      errorSnackBar(message: T("Error applying adjustments: $e"));
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  // Print inventory comparison report
  Future<void> _printInventoryComparison() async {
    try {
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);

      // Load Arabic font for product names that might be in Arabic
      pw.Font arabicFont;
      try {
        final fontData =
            await rootBundle.load("assets/fonts/NotoNaskhArabic-Regular.ttf");
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        try {
          final fontData =
              await rootBundle.load("assets/fonts/DroidKufi-Regular.ttf");
          arabicFont = pw.Font.ttf(fontData);
        } catch (e) {
          final fontData = await rootBundle
              .load("assets/fonts/Montserrat-Arabic-Regular.ttf");
          arabicFont = pw.Font.ttf(fontData);
        }
      }

      // Create PDF document without Arabic font for better compatibility
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              // Header
              pw.Header(
                level: 0,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Text(
                      'INVENTORY COMPARISON REPORT',
                      style: pw.TextStyle(
                          fontSize: 22, fontWeight: pw.FontWeight.bold),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'Warehouse: $_warehouseName',
                      style: const pw.TextStyle(fontSize: 16),
                    ),
                    pw.Text(
                      'Date: ${DateTime.now().toString().split(' ')[0]}',
                      style: const pw.TextStyle(fontSize: 14),
                    ),
                    pw.Text(
                      'Total Items: ${_comparisonItems.length}',
                      style: const pw.TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Table
              pw.Table(
                border: pw.TableBorder.all(color: PdfColors.grey400),
                columnWidths: {
                  0: const pw.FlexColumnWidth(3),
                  1: const pw.FlexColumnWidth(2),
                  2: const pw.FlexColumnWidth(1.5),
                  3: const pw.FlexColumnWidth(1.5),
                  4: const pw.FlexColumnWidth(1.5),
                  5: const pw.FlexColumnWidth(1.5),
                },
                children: [
                  // Header row
                  pw.TableRow(
                    decoration:
                        const pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'ITEM NAME',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'CODE',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'STOCKTAKING',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'SERVER',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'DIFFERENCE',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'STATUS',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ],
                  ),

                  // Data rows - Support Arabic product names
                  ..._comparisonItems.map((item) {
                    String status = item.difference == 0
                        ? 'MATCH'
                        : item.difference > 0
                            ? 'SURPLUS'
                            : 'DEFICIT';

                    return pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            item.itemName,
                            style: pw.TextStyle(
                              font:
                                  arabicFont, // Use Arabic font for product names
                              fontSize: 9,
                            ),
                            textDirection: _isArabicText(item.itemName)
                                ? pw.TextDirection.rtl
                                : pw.TextDirection.ltr,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            item.itemCode,
                            style: const pw.TextStyle(fontSize: 9),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            item.stocktakingQuantity.toString(),
                            style: const pw.TextStyle(fontSize: 9),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            item.serverQuantity.toString(),
                            style: const pw.TextStyle(fontSize: 9),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            item.difference >= 0
                                ? '+${item.difference}'
                                : '${item.difference}',
                            style: const pw.TextStyle(fontSize: 9),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            status,
                            style: const pw.TextStyle(fontSize: 9),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ],
              ),

              pw.SizedBox(height: 20),

              // Summary
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey400),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'SUMMARY',
                      style: pw.TextStyle(
                          fontSize: 16, fontWeight: pw.FontWeight.bold),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'Items with exact match: ${_comparisonItems.where((item) => item.difference == 0).length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'Items with surplus: ${_comparisonItems.where((item) => item.difference > 0).length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'Items with deficit: ${_comparisonItems.where((item) => item.difference < 0).length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ];
          },
        ),
      );

      // Print using Printing package directly
      await Printing.layoutPdf(
        onLayout: (_) async => await pdf.save(),
        name: 'Inventory_Comparison_${DateTime.now().millisecondsSinceEpoch}',
        format: PdfPageFormat.a4,
      );

      successSnackBar(message: T("Report sent to printer successfully"));
    } catch (e) {
      errorSnackBar(message: T("Printing error: $e"));
    }
  }

  // Helper method to detect if text contains Arabic characters
  bool _isArabicText(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          ApplicationLayout(
            child: Column(
              children: [
                // Header
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        offset: const Offset(0, 2),
                        blurRadius: 6,
                      )
                    ],
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    children: [
                      CommonHeader(
                        icon: Icons.analytics_outlined,
                        title: T("Inventory Comparison"),
                        actions: [
                          // Print button
                          IconButton(
                            icon: const Icon(Icons.print),
                            onPressed: _comparisonItems.isNotEmpty
                                ? _printInventoryComparison
                                : null,
                            tooltip: T("Print Report"),
                            color: context.secondaryColor,
                          ),
                        ],
                      ),

                      // Warehouse Info
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: context.newBackgroundColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: context.newPrimaryColor.withOpacity(0.2),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                T("Warehouse: $_warehouseName"),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: context.newPrimaryColor,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                T("Total Items: ${_comparisonItems.length}"),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: context.newTextColor,
                                ),
                              ),
                              const SizedBox(height: 12),
                              // Select All Checkbox
                              InkWell(
                                onTap: _comparisonItems.isNotEmpty
                                    ? _toggleSelectAll
                                    : null,
                                borderRadius: BorderRadius.circular(8),
                                child: Row(
                                  children: [
                                    Checkbox(
                                      value: _isAllSelected,
                                      onChanged: _comparisonItems.isNotEmpty
                                          ? (value) => _toggleSelectAll()
                                          : null,
                                      activeColor: context.newPrimaryColor,
                                    ),
                                    Text(
                                      T("Select All Items"),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: context.newTextColor,
                                      ),
                                    ),
                                    const Spacer(),
                                    if (_selectedItemIds.isNotEmpty)
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: context.newPrimaryColor
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          T("${_selectedItemIds.length} selected"),
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: context.newPrimaryColor,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: _isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const CircularProgressIndicator(),
                              const SizedBox(height: 16),
                              Text(
                                _loadingMessage,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: context.newTextColor,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      : _comparisonItems.isEmpty
                          ? _buildEmptyState()
                          : _buildComparisonList(),
                ),
              ],
            ),
          ),

          // Processing overlay
          if (_isProcessing)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        T("Applying quantity adjustments..."),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: context.newTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        T("Please wait, this may take a few moments"),
                        style: TextStyle(
                          fontSize: 14,
                          color: context.newTextColor.withOpacity(0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: _selectedItemIds.isNotEmpty && !_isProcessing
          ? FloatingActionButton.extended(
              onPressed: _applyQuantityAdjustments,
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.sync),
              label: Text(T("Apply Adjustments")),
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 24),
          Text(
            T("No Items Found"),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("No items found in this stocktaking session"),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonList() {
    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _comparisonItems.length,
        itemBuilder: (context, index) {
          final item = _comparisonItems[index];
          return _buildComparisonCard(item);
        },
      ),
    );
  }

  Widget _buildComparisonCard(ComparisonItemDto item) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (item.difference == 0) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = T("Match");
    } else if (item.difference > 0) {
      statusColor = Colors.orange;
      statusIcon = Icons.trending_up;
      statusText = T("Surplus");
    } else {
      statusColor = Colors.red;
      statusIcon = Icons.trending_down;
      statusText = T("Deficit");
    }

    final bool isSelected = _selectedItemIds.contains(item.itemId);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? context.newPrimaryColor.withOpacity(0.5)
              : statusColor.withOpacity(0.3),
          width: isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? context.newPrimaryColor.withOpacity(0.2)
                : statusColor.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with checkbox and status
            Row(
              children: [
                // Checkbox
                Checkbox(
                  value: isSelected,
                  onChanged: (value) => _toggleItemSelection(item.itemId),
                  activeColor: context.newPrimaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.itemName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: context.newTextColor,
                        ),
                      ),
                      if (item.itemCode.isNotEmpty)
                        Text(
                          item.itemCode,
                          style: TextStyle(
                            fontSize: 12,
                            color: context.newTextColor.withOpacity(0.6),
                          ),
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: statusColor.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Quantities comparison
            Row(
              children: [
                Expanded(
                  child: _buildQuantityBox(
                    T("Stocktaking"),
                    item.stocktakingQuantity.toString(),
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 5),
                Expanded(
                  child: _buildQuantityBox(
                    T("Server"),
                    item.serverQuantity.toString(),
                    Colors.purple,
                  ),
                ),
                const SizedBox(width: 5),
                Expanded(
                  child: _buildQuantityBox(
                    T("Difference"),
                    item.difference >= 0
                        ? "+${item.difference.toString()}"
                        : item.difference.toString(),
                    statusColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityBox(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
