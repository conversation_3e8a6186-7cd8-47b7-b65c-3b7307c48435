import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_noInternet_ErrorWidget.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/inverntory/product%20transactions/widgets/product_transaction_header_widget.dart';

class ProductTransactionListScreen extends StatefulWidget {
  const ProductTransactionListScreen({super.key});

  @override
  State<ProductTransactionListScreen> createState() =>
      _ProductTransactionListScreenState();
}

class _ProductTransactionListScreenState
    extends State<ProductTransactionListScreen> {
  Widget build(BuildContext context) {
    var status = AppController.isThereConnection;
    return ApplicationLayout(
      child: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: _StickyHeaderDelegate2(
              child: Center(
                child: Container(
                  padding: const EdgeInsets.only(top: 10),
                  width: context.width - 10,
                  color: context.backgroundColor,
                  child: CommonHeader(
                    icon: Icons.account_tree_outlined,
                    title: T("items transaction"),
                  ),
                ),
              ),
            ),
          ),

          // Sticky Header that stays at the top
          SliverPersistentHeader(
            pinned: true,
            delegate: _StickyHeaderDelegate(
              child: Center(
                child: Container(
                  padding: const EdgeInsets.only(top: 10),
                  width: context.width - 10,
                  color: context.backgroundColor,
                  child: CommonMaterialButton(
                    width: context.width - 20,
                    label: T("Search"),
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) => const SizedBox(),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
          // SliverAppBar that hides on scroll
          status == true
              ? SliverAppBar(
                  expandedHeight:
                      110.0, // Customize this height for the initial section
                  floating: false,
                  leading: const SizedBox.shrink(),
                  pinned: false, // Set pinned to false so it hides on scroll
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                        alignment: Alignment.center,
                        margin: const EdgeInsets.only(top: 10),
                        child: const ProductTransactionHeaderWidget()),
                  ),
                )
              : const SliverToBoxAdapter(
                  child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 130),
                  child: NoInternetErrorWidget(),
                )),

          // Rest of the content
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 4500), // Scrollable content
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Delegate class to create a sticky header
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 50.0;

  @override
  double get minExtent => 50.0;

  @override
  bool shouldRebuild(_StickyHeaderDelegate oldDelegate) {
    return oldDelegate.child != child;
  }
}

// Delegate class to create a sticky header
class _StickyHeaderDelegate2 extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate2({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 85.0;

  @override
  double get minExtent => 85.0;

  @override
  bool shouldRebuild(_StickyHeaderDelegate2 oldDelegate) {
    return oldDelegate.child != child;
  }
}
