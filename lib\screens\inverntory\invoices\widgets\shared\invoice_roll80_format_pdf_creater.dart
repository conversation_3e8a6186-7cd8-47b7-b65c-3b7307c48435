import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:inventory_application/helpers/pdf_helper_formatroll80.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_pleasewait.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:pdf/pdf.dart';
import 'package:share_plus/share_plus.dart';

class InvoiceRoll80FormatPdfCreaterWidget extends StatefulWidget {
  final InvoiceDto? data;
  final PdfPageFormat format;
  const InvoiceRoll80FormatPdfCreaterWidget(
      {super.key, required this.data, required this.format});

  @override
  // ignore: library_private_types_in_public_api
  _InvoiceRoll80FormatPdfCreaterWidgetState createState() =>
      _InvoiceRoll80FormatPdfCreaterWidgetState();
}

class _InvoiceRoll80FormatPdfCreaterWidgetState
    extends State<InvoiceRoll80FormatPdfCreaterWidget> {
  String? pdfPath;

  @override
  void initState() {
    super.initState();
    generatePdf();
  }

  Future<void> generatePdf() async {
    final file = await createInvoicePdfroll80(
        widget.data ?? InvoiceDto(), widget.format);

    setState(() {
      pdfPath = file.path;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: pdfPath == null
                ? const Center(child: PleaseWaitWidget())
                : PDFView(
                    filePath: pdfPath!,
                  ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                FloatingActionButton(
                  heroTag: 'back_button_roll80',
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                FloatingActionButton(
                  heroTag: 'share_button_roll80',
                  onPressed: () async {
                    if (pdfPath != null) {
                      final pdfFile =
                          XFile(pdfPath!); // Create an XFile from the path
                      Share.shareXFiles([pdfFile],
                          text: 'Here is your invoice PDF.');
                    } else {
                      print('PDF file not available');
                    }
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.share,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
