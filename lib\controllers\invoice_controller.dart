import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/base/network/ecommerce_api.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/models/dto/invoice/invoice_list_filter_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_confirm_order_dto.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_order_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';

class InvoiceController with ChangeNotifier {
  List<InvoiceDto> invoices = [];
  InvoiceDto? invoiceDetails;
  List<InvoiceDtoWithLiteId> _allInvoices = [];
  List<InvoiceDtoWithLiteId> _pendingInvoices = [];
  List<InvoiceDtoWithLiteId> _syncedInvoices = [];
  List<SqlLiteInvoiceModel> localInvoices = [];

  List<InvoiceDtoWithLiteId> get allInvoices => _allInvoices;
  List<InvoiceDtoWithLiteId> get pendingInvoices => _pendingInvoices;
  List<InvoiceDtoWithLiteId> get syncedInvoices => _syncedInvoices;

  InvoiceListFilterDTO filterModel = InvoiceListFilterDTO(
      fromDate: DateTime.now().add(
        const Duration(days: -30),
      ),
      toDate: DateTime.now(),
      invoiceTypeName: "فواتير مبيعات",
      dataTableParameters: DataTableParameters(),
      invoiceType: SalesType.Invoice);

  final int _pageSize = 10;

//---------------------------------------------------------------------------
  Future<InvoiceDto?> getInvoiceByTypeAndCode2({
    required String type,
    required String code,
  }) async {
    try {
      var url = '/Sales/Manage?transactions_type=$type&code=$code';

      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          var invoiceModel = InvoiceModel.fromJson(result.data);
          print('Total: ${invoiceDetails?.total}');
          print('Total After Discount: ${invoiceDetails?.totalAfterDiscount}');
          invoiceDetails = mapInvoiceModel(invoiceModel);
          invoiceDetails?.total = invoiceDetails?.calculateTotal();
          invoiceDetails?.totalDiscount =
              invoiceDetails?.calculateTotalDiscount();
          invoiceDetails?.totalAfterDiscount =
              invoiceDetails?.calculateTotalAfterDiscount();
          invoiceDetails?.totalAfterDiscount =
              invoiceDetails?.calculateNetTotal();
          // notifyListeners();
          return invoiceDetails;
        } else {
          return null;
        }
      }

      return null;
    } catch (e) {
      print(e);
      return null;
    }
  }

//---------------------------------------------------------------------------

  Future<void> getInvoices({bool resetAndRefresh = false}) async {
    try {
      if (resetAndRefresh) {
        invoices.clear();
      }

      var url = '/Sales/SalesList';
      filterModel.invoiceTypeNameForRequest =
          getInvoiceTypeName(filterModel.invoiceType);
      filterModel.dataTableParameters = DataTableParameters(
          columnName: "Entry_Date",
          dir: "desc",
          skip: invoices.length,
          take: _pageSize);

      var result = await Api.post(action: url, body: filterModel.toJson());
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            var invoiceModel = InvoiceModel.fromJson(element);
            invoices.add(mapInvoiceModel(invoiceModel));
            print(element);
          }
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

//---------------------------------------------------------------------------
  Future<InvoiceModel?> getInvoiceByTypeAndCode(
      {required String type, required String code}) async {
    try {
      var url = '/Sales/Manage?transactions_type=$type&code=$code';

      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          var productModel = InvoiceModel.fromJson(result.data);

          return productModel;
        } else {
          return null;
        }
      }

      return null;
    } catch (e) {
      print(e);
      return null;
    }
  }

//---------------------------------------------------------------------------
  // Function to insert an invoice
  Future<int> insertOrUpdateInvoice(Map<String, dynamic> invoice) async {
    var IdToReturn = 0;
    final db = await DatabaseHelper().database;
    var checkIfExist = await checkIfInvoiceExists(invoice["localCode"]);
    if (checkIfExist == false) {
      IdToReturn = await db.insert('Invoice', invoice);
    } else {
      IdToReturn = await updateInvoice(
          invoice["localCode"], json.decode(invoice["data"]));
    }

    return IdToReturn;
  }

//------------------------------------------------------------------------------
  // Function to get pending invoices
  Future<List<InvoiceDtoWithLiteId>> getPendingInvoices() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('Invoice',
        where: 'status = ?',
        whereArgs: [getInvoiceSyncStatus(InvoiceSyncStatus.pending)]);
    print(result); // Check raw database result

    List<InvoiceDtoWithLiteId> invoices = [];
    for (var element in result) {
      var invoiceModel = SqlLiteInvoiceModel.fromJson(element);

      if (invoiceModel.data != null) {
        try {
          // Parse data field as JSON
          var parsedData = jsonDecode(invoiceModel.data!);
          // print("Parsed data: $parsedData");

          var invoiceData = InvoiceModel.fromJson(parsedData);

          invoices.add(
              InvoiceDtoWithLiteId(data: invoiceData, id: invoiceModel.id));
        } catch (e) {
          // ignore: avoid_print
          print("Error parsing or deserializing data: $e");
        }
      }
    }

    return invoices;
  }

//------------------------------------------------------------------------------
  Future<bool> syncInvoicesWithSever() async {
    try {
      var invoices = await getPendingInvoices();
      if (invoices.isEmpty) {
        // errorSnackBar(message: T("There are no invoices to sync"));
        return false;
      }

      var url = '/Sales/Manage';
      for (var invoice in invoices) {
        invoice.data?.code = "*";

        var result = await Api.post(
          action: url,
          body: invoice.data?.toJson(),
        );
        if (result != null) {
          if (result.isSuccess) {
            await updateInvoiceSyncStatus(
                getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                invoice.id ?? 0);
            await updateInvoiceServerId(
                invoice.id ?? 0, result.data["ID"], result.data["Code"]);
            invoice.data?.code = result.data["Code"];
            invoice.data?.iD = result.data["ID"];
            if (invoice.data?.transactionsType == "RetrunInvoice") {
              await saveReturnToEcommerce(invoice.data ?? InvoiceModel());
            } else {
              await saveOrderToEcommerce(invoice.data ?? InvoiceModel());
            }
          }

          notifyListeners();
          await CounterGenerator.setInvoicesCounterInServer();
          successSnackBar(message: "تمت مزامنة الفواتير بنجاح");
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

//------------------------------------------------

  // Function to update invoice sync status
  Future<int> updateInvoiceSyncStatus(String status, int id) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      'Invoice',
      {'status': status},
      where: 'ID = ?',
      whereArgs: [id],
    );
  }

  //------------------------------------------------------------------------------
  Future<int> updateInvoiceServerId(
      int localId, int serverId, String code) async {
    final db = await DatabaseHelper().database;

    // Retrieve the existing data for the invoice
    var result = await db.query(
      'Invoice',
      columns: ['data'],
      where: 'ID = ?',
      whereArgs: [localId],
    );

    if (result.isNotEmpty) {
      // Parse the JSON string to a Map
      Map<String, dynamic> data = jsonDecode(result.first['data'] as String);

      // Update the data.ID field with the new server ID
      data['ID'] = serverId;
      data['Code'] = code;

      // Convert the updated map back to a JSON string
      String updatedDataJson = jsonEncode(data);

      // Update only the data field in the database
      return await db.update(
        'Invoice',
        {
          'data': updatedDataJson, // Update the data with the new ID
        },
        where: 'ID = ?',
        whereArgs: [localId],
      );
    } else {
      // If no record is found, return 0 to indicate failure
      return 0;
    }
  }
  //------------------------------------------------------------------------------

  Future<List<SqlLiteInvoiceModel>> getInvoicesFromLocalStorage() async {
    final db = await DatabaseHelper().database; // Get the database instance

    final List<Map<String, dynamic>> result = await db.query('Invoice');

    // Map the result to a list of SqlLiteInvoiceModel instances
    return result.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
  }

//--------------------------------------------------------

  Future<InvoiceDto> getInoviceById(int id) async {
    final db = await DatabaseHelper().database;
    var invoice = await db.query(
      'Invoice',
      where: 'ID = ?',
      whereArgs: [id],
    );

    var mappedItem =
        invoice.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
    var result = mappedItem
        .map(
          (e) => InvoiceDtoWithLiteId(
            id: e.id,
            status: e.status,
            data: InvoiceModel.fromJson(
              json.decode(e.data ?? ""),
            ),
          ),
        )
        .toList();
    if (result.isNotEmpty) {
      var invoiceDto = mapInvoiceModel(result[0].data ?? InvoiceModel());
      invoiceDto.total = invoiceDto.calculateTotal();
      invoiceDto.totalDiscount = invoiceDto.calculateTotalDiscount();
      invoiceDto.totalAfterDiscount = invoiceDto.calculateTotalAfterDiscount();
      // invoiceDto.totalAfterDiscount = invoiceDto.calculateNetTotal();

      return invoiceDto;
    }

    return InvoiceDto();
  }

//--------------------------------------------------------
  Future<List<InvoiceDtoWithLiteId>> fetchLocalInvoices() async {
    try {
      localInvoices = await getInvoicesFromLocalStorage();

      var data = localInvoices
          .map(
            (e) => InvoiceDtoWithLiteId(
              id: e.id,
              status: e.status,
              data: InvoiceModel.fromJson(
                json.decode(e.data ?? ""),
              ),
            ),
          )
          .toList();

      return data;
    } catch (e) {
      // ignore: avoid_print
      print(e);
      return [];
    }
  }

//------------------------------------------------------------------------------
  Future<bool> checkIfInvoiceExists(String invoiceLocalCode) async {
    final db = await DatabaseHelper().database;

    var result = await db.query(
      'Invoice',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    return result.isNotEmpty;
  }

//------------------------------------------------------------------------------
  Future<InvoiceDto?> getInoviceByCode(
      {required String invoiceLocalCode}) async {
    final db = await DatabaseHelper().database;
    var invoice = await db.query(
      'Invoice',
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    var mappedItem =
        invoice.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
    var result = mappedItem
        .map(
          (e) => InvoiceDtoWithLiteId(
            id: e.id,
            status: e.status,
            data: InvoiceModel.fromJson(
              json.decode(e.data ?? ""),
            ),
          ),
        )
        .toList();
    if (result.isNotEmpty) {
      result[0].data?.localId = result[0].id;
      var invoiceDto = mapInvoiceModel(result[0].data ?? InvoiceModel());
      invoiceDto.total = invoiceDto.calculateTotal();
      invoiceDto.totalDiscount = invoiceDto.calculateTotalDiscount();
      invoiceDto.totalAfterDiscount = invoiceDto.calculateTotalAfterDiscount();
      // invoiceDto.totalAfterDiscount = invoiceDto.calculateNetTotal();

      return invoiceDto;
    }

    return null;
  }

//------------------------------------------------------------------------------
  Future<int> updateInvoice(
      String invoiceLocalCode, Map<String, dynamic> invoice) async {
    final db = await DatabaseHelper().database;

    // Retrieve the existing data for the invoice
    var result = await db.query(
      'Invoice',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    if (result.isNotEmpty) {
      // Parse the JSON string to a Map
      Map<String, dynamic> data = jsonDecode(result.first['data'] as String);

      data = invoice;

      // Convert the updated map back to a JSON string
      String updatedDataJson = jsonEncode(data);

      // Update only the data field in the database
      var response = await db.update(
        'Invoice',
        {
          'data': updatedDataJson, // Update the data with the new ID
        },
        where: 'localCode = ?',
        whereArgs: [invoiceLocalCode],
      );
      // var sadas = response;
      return response;
    } else {
      // If no record is found, return 0 to indicate failure
      return 0;
    }
  }

//------------------------------------------------------------------------------

  Future<int> deleteInvoice(String id) async {
    final db = await DatabaseHelper().database;

    // Perform the deletion
    int result = await db.delete(
      'Invoice',
      where: 'localCode = ?',
      whereArgs: [id],
    );

    // Return the number of rows affected (should be 1 if successful)
    return result;
  }

//-----------------------------------------------------------------------------
  Future<bool> deleteSyncInvoice(int invoiceId, String transactionsType) async {
    try {
      var url =
          'Sales/Delete?transactions_type=$transactionsType&id=$invoiceId';

      var result = await Api.post(action: url);
      if (result?.isSuccess == true) {
        successSnackBar(message: "تم الحذف بنجاح");
      }

      final db = await DatabaseHelper().database;
      var deleteResult = await db.delete(
        'Invoice',
        where: 'localCode = ?',
        whereArgs: [invoiceId],
      );

      if (deleteResult == 0) {
        updateInvoiceDeletionStatus(invoiceId);
        notifyListeners();
        print("Invoice successfully deleted from local database.");
        return true;
      } else {
        errorSnackBar(
            message: "Invoice deleted from server but not found locally.");
        return false;
      }
    } catch (e, stackTrace) {
      print("Error during invoice deletion: $e");
      print("Stack Trace: $stackTrace");
      errorSnackBar(message: "Error: ${e.toString()}");
      return false;
    }
  }

//-----------------------------------------------------------------------------
  Future<void> updateInvoiceDeletionStatus(int invoiceId) async {
    invoices.removeWhere((invoice) => invoice.id == invoiceId);
    notifyListeners();
  }

  //---------------------------------------------------------------------------
  Future<void> saveOrderToEcommerce(InvoiceModel model) async {
    try {
      var url = 'api/A_Orders/SaveOrderFromErp';

      var mapedInvoice =
          mapInvoiceModelWithEcommerceDto(model, TransactionTypes.Sale);

      var result = await EcommerceApi.post(
        action: url,
        body: mapedInvoice.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          notifyListeners();
        }
      }
    } catch (e) {
      print(e);
    }
  }

  //---------------------------------------------------------------------------
  Future<bool> confirmOrderToEcommerce(EcommerceConfirmOrderDto model) async {
    try {
      var url = 'api/A_Orders/ConfirmOrderInvoiceFromErp';

      var result = await EcommerceApi.post(
        action: url,
        body: model.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          notifyListeners();
          return true;
        }
      }
      return false;
    } catch (e) {
      print(e);
      return false;
    }
  }

  //---------------------------------------------------------------------------
  Future<void> saveReturnToEcommerce(InvoiceModel model) async {
    try {
      var url = 'api/A_Orders/SaveReturnOrder';

      var mapedInvoice =
          mapInvoiceModelWithEcommerceDto(model, TransactionTypes.Return);

      var result = await EcommerceApi.post(
        action: url,
        body: mapedInvoice.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          notifyListeners();
        }
      }
    } catch (e) {
      print(e);
    }
  }
}
