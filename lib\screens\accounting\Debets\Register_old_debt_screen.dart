import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class RegisterOldDebtScreen extends StatefulWidget {
  const RegisterOldDebtScreen({super.key});

  @override
  State<RegisterOldDebtScreen> createState() => _RegisterOldDebtScreenState();
}

class _RegisterOldDebtScreenState extends State<RegisterOldDebtScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
