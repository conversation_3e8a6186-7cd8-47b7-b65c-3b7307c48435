import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';

class InvoiceDetailsItemsWidget extends StatelessWidget {
  const InvoiceDetailsItemsWidget({super.key, required this.data});
  final InvoiceDto data;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (data.salesItems == null || data.salesItems!.isEmpty)
          _buildEmptyState(context)
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: data.salesItems?.length,
            itemBuilder: (context, index) {
              final invoice = data.salesItems?[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: context.newPrimaryColor.withOpacity(0.3),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Product header
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: context.newPrimaryColor.withOpacity(0.1),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(9),
                          topRight: Radius.circular(9),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.inventory_2_outlined,
                            size: 18,
                            color: context.newSecondaryColor,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              invoice?.title ?? "",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: context.newSecondaryColor,
                                fontSize: 15,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Product details
                    Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        children: [
                          // Product code
                          _buildProductInfoRow(
                            context,
                            label: T("Product Code"),
                            value: invoice?.code ?? "",
                            icon: Icons.qr_code,
                          ),

                          const Divider(height: 16),

                          // Quantity and price
                          Row(
                            children: [
                              // Quantity
                              Expanded(
                                child: _buildProductInfoRow(
                                  context,
                                  label: T("Quantity"),
                                  value: invoice?.quantity.toString() ?? "",
                                  icon: Icons.format_list_numbered,
                                  isCompact: true,
                                ),
                              ),

                              const SizedBox(width: 20),

                              // Price
                              Expanded(
                                child: _buildProductInfoRow(
                                  context,
                                  label: T("Price"),
                                  value: invoice?.price.toString() ?? "",
                                  icon: Icons.attach_money,
                                  isCompact: true,
                                ),
                              ),
                            ],
                          ),

                          const Divider(height: 16),

                          // Unit and total
                          Row(
                            children: [
                              // Unit
                              Expanded(
                                child: _buildProductInfoRow(
                                  context,
                                  label: T("Unit"),
                                  value: invoice?.uniteName ?? "",
                                  icon: Icons.straighten,
                                  isCompact: true,
                                ),
                              ),

                              const SizedBox(width: 20),

                              // Total
                              Expanded(
                                child: _buildProductInfoRow(
                                  context,
                                  label: T("Total"),
                                  value:
                                      '${((invoice?.quantity ?? 0) * (invoice?.price ?? 0))}',
                                  icon: Icons.calculate_outlined,
                                  isCompact: true,
                                  isTotal: true,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildProductInfoRow(
    BuildContext context, {
    required String label,
    required String value,
    required IconData icon,
    bool isCompact = false,
    bool isTotal = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isTotal ? context.newSecondaryColor : context.newPrimaryColor,
        ),
        const SizedBox(width: 6),
        Text(
          "$label: ",
          style: TextStyle(
            fontSize: isCompact ? 13 : 14,
            fontWeight: FontWeight.w500,
            color: context.newTextColor.withOpacity(0.8),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: isCompact ? 13 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? context.newSecondaryColor : context.newTextColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: context.newPrimaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 48,
            color: context.newTextColor.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            T("No products found"),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: context.newTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("This invoice doesn't contain any product items"),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: context.newTextColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
}
