import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_datetime_picker.dart';
import 'package:provider/provider.dart';

class ReportFilterDialog extends StatefulWidget {
  final ReportFilterDTO initialFilter;
  final Function(ReportFilterDTO) onApply;

  const ReportFilterDialog({
    Key? key,
    required this.initialFilter,
    required this.onApply,
  }) : super(key: key);

  @override
  State<ReportFilterDialog> createState() => _ReportFilterDialogState();
}

class _ReportFilterDialogState extends State<ReportFilterDialog> {
  late ReportFilterDTO _filter;
  late ReportType _selectedReportType;

  @override
  void initState() {
    super.initState();
    _filter = widget.initialFilter.copyWith();
    _selectedReportType = _filter.reportType;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dialog header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  T('Report Filters'),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Report type selector
            Text(
              T('Report Type:'),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<ReportType>(
                  value: _selectedReportType,
                  isExpanded: true,
                  icon: const Icon(Icons.arrow_drop_down),
                  onChanged: (ReportType? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedReportType = newValue;
                        _filter = _filter.copyWith(
                          reportType: newValue,
                        );
                      });
                    }
                  },
                  items: ReportType.values.map((ReportType type) {
                    return DropdownMenuItem<ReportType>(
                      value: type,
                      child: Text(_getReportTypeName(type)),
                    );
                  }).toList(),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Date range selector
            Text(
              T('Date Range:'),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // From date
            Row(
              children: [
                Text(
                  T('From:'),
                  style: TextStyle(
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyDatePicker(
                    width: double.infinity,
                    onSave: (date) {
                      setState(() {
                        _filter = _filter.copyWith(
                          fromDate: date,
                        );
                      });
                    },
                    initialVal: _filter.fromDate ?? DateTime.now(),
                    caption: DateFormat('yyyy-MM-dd')
                        .format(_filter.fromDate ?? DateTime.now()),
                    backColor: Colors.grey.shade100,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // To date
            Row(
              children: [
                Text(
                  T('To:'),
                  style: TextStyle(
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 28),
                Expanded(
                  child: MyDatePicker(
                    width: double.infinity,
                    onSave: (date) {
                      setState(() {
                        _filter = _filter.copyWith(
                          toDate: date,
                        );
                      });
                    },
                    initialVal: _filter.toDate ?? DateTime.now(),
                    caption: DateFormat('yyyy-MM-dd')
                        .format(_filter.toDate ?? DateTime.now()),
                    backColor: Colors.grey.shade100,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    T('Cancel'),
                    style: TextStyle(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    widget.onApply(_filter);
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(T('Apply')),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get report type display name
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return T('Daily Sales');
      case ReportType.monthlySales:
        return T('Monthly Sales');
      case ReportType.productSales:
        return T('Product Sales');
      case ReportType.customerSales:
        return T('Customer Sales');
    }
  }
}
