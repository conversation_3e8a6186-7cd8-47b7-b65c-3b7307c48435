import 'package:flutter/material.dart';
import 'package:inventory_application/screens/home/<USER>'; // Assuming ApplicationLayout is in this path

class PaymentVoucherScreen extends StatefulWidget {
  const PaymentVoucherScreen({super.key});

  @override
  State<PaymentVoucherScreen> createState() => _PaymentVoucherScreenState();
}

class _PaymentVoucherScreenState extends State<PaymentVoucherScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header Row: Title and Back Button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'ادارة سندات الصرف', // Payment Voucher Title
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Implement back navigation
                    },
                    icon: Icon(Icons.arrow_back),
                    label: Text('رجوع للقائمة'),
                  ),
                ],
              ),
              SizedBox(height: 20),

              // Main Input Fields Section
              Card(
                elevation: 2.0,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      // Row 1 (Document Number, Date, Payment Method)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration: InputDecoration(
                                  labelText: 'رقم السند'), // Document Number
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  InputDecoration(labelText: 'التاريخ'), // Date
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                  labelText: 'طرق الدفع'), // Payment Method
                              items: [], // TODO: Add actual items
                              onChanged: (value) {},
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),
                      // Row 2 (Voucher Amount, Currency, Exchange Rate)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration: InputDecoration(
                                  labelText: 'مبلغ السند'), // Voucher Amount
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                  labelText: 'العملة'), // Currency
                              items: [], // TODO: Add actual items
                              onChanged: (value) {},
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              decoration: InputDecoration(
                                  labelText: 'سعر الصرف'), // Exchange Rate
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),
                      // Row 3 (Payer, Paid To)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration:
                                  InputDecoration(labelText: 'الدافع'), // Payer
                              items: [], // TODO: Add actual items
                              onChanged: (value) {},
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                  labelText: 'مدين / المدفوع له'), // Paid To
                              items: [], // TODO: Add actual items
                              onChanged: (value) {},
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),
                      // Description Field
                      TextFormField(
                        decoration:
                            InputDecoration(labelText: 'البيان'), // Description
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 20),

              // Balance Section (if needed for payment voucher)
              Card(
                elevation: 2.0,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Column(
                        children: [
                          Text('الرصيد'), // Balance
                          Text('0.000',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      Column(
                        children: [
                          Text('دائن'), // Credit
                          Text('0.000',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      Column(
                        children: [
                          Text('مدين'), // Debit
                          Text('0.000',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 20),

              // Payment Vouchers Table Section
              Text(
                'سندات الصرف', // Payment Vouchers Title
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              // Payment Vouchers Table
              DataTable(
                columns: [
                  DataColumn(label: Text('رقم السند')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('مبلغ السند')),
                  DataColumn(label: Text('الدافع')),
                  DataColumn(label: Text('البيان')),
                ],
                rows: [
                  // TODO: Add actual data rows here
                ],
              ),
              SizedBox(height: 10),
              Center(
                child: Text(
                  'لا يوجد بيانات لعرضها', // Message when table is empty
                  style: TextStyle(
                      fontStyle: FontStyle.italic, color: Colors.grey),
                ),
              ),

              SizedBox(height: 20),

              // Bottom Buttons (Print, Save)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Implement Print functionality
                    },
                    child: Text('طباعة'), // Print
                  ),
                  SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Implement Save functionality
                    },
                    child: Text('حفظ'), // Save
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
