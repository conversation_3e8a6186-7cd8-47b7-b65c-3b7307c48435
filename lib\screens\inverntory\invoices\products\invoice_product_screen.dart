import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/inverntory/invoices/products/widgets/invoice_product_barcodes_dialog.dart';
import 'package:inventory_application/screens/inverntory/invoices/products/widgets/invoice_product_categories_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/products/widgets/invoice_product_list_items_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/products/widgets/invoice_product_search_widget.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class InvoiceProductScreen extends StatefulWidget {
  const InvoiceProductScreen({
    super.key,
    required this.onAddProduct,
    required this.onRemoveProduct,
    required this.selectedProducts,
  });
  final Function onAddProduct;
  final Function onRemoveProduct;
  final List<ProductDTO> selectedProducts;

  @override
  // ignore: library_private_types_in_public_api
  _InvoiceProductScreenState createState() => _InvoiceProductScreenState();
}

class _InvoiceProductScreenState extends State<InvoiceProductScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  String _searchQuery = '';
  int categoryId = 0;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
  }

  //==================================================//
  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  //==================================================//
  void _onRefresh() async {
    await Provider.of<ProductController>(context, listen: false).getItems(
        resetAndRefresh: true, categoryId: categoryId, search: _searchQuery);
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  //==================================================//
  void _onLoading() async {
    await Provider.of<ProductController>(context, listen: false)
        .getItems(categoryId: categoryId, search: _searchQuery);
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<ProductController>(context).realProductList;
    var productsCount = data.length;

    return ApplicationLayout(
      child: Column(
        children: [
          // Header with search bar and title
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 6,
                )
              ],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title Section
                Row(
                  children: [
                    Icon(
                      Icons.inventory_outlined,
                      color: context.newPrimaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      T("Product Selection"),
                      style: TextStyle(
                        color: context.newTextColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 5,
                      ),
                      decoration: BoxDecoration(
                        color: context.newPrimaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.shopping_basket_outlined,
                            color: context.newPrimaryColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            productsCount.toString(),
                            style: TextStyle(
                              color: context.newPrimaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),

                // Search bar
                invoiceProductSearchWidget(
                  context: context,
                  onchange: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    if (_searchQuery.length > 2) _onRefresh();
                  },
                ),
              ],
            ),
          ),

          // Categories Section
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.03),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                )
              ],
            ),
            child: InvoiceProductCategoriesWidget(
              cateogryId: categoryId,
              onSelecte: (id) {
                setState(() {
                  categoryId = id;
                });
                _onRefresh();
              },
            ),
          ),

          // Products Grid
          data.isEmpty
              ? _buildEmptyState()
              : Expanded(
                  child: SmartRefresher(
                    enablePullDown: true,
                    enablePullUp: true,
                    header: WaterDropHeader(
                      waterDropColor: context.newPrimaryColor,
                    ),
                    footer: CustomFooter(
                      builder: (context, mode) {
                        Widget body;
                        if (mode == LoadStatus.idle) {
                          body = Text(
                            T("Pull up to load more"),
                            style: TextStyle(
                              color: context.newTextColor.withOpacity(0.6),
                            ),
                          );
                        } else if (mode == LoadStatus.loading) {
                          body = CupertinoActivityIndicator(
                            color: context.newPrimaryColor,
                          );
                        } else if (mode == LoadStatus.failed) {
                          body = Text(
                            T("Failed to load! Tap to retry"),
                            style: TextStyle(
                              color: Colors.red.shade700,
                            ),
                          );
                        } else if (mode == LoadStatus.canLoading) {
                          body = Text(
                            T("Release to load more"),
                            style: TextStyle(
                              color: context.newTextColor,
                            ),
                          );
                        } else {
                          body = Text(
                            T("No more products"),
                            style: TextStyle(
                              color: context.newTextColor.withOpacity(0.5),
                            ),
                          );
                        }
                        return SizedBox(
                          height: 55.0,
                          child: Center(child: body),
                        );
                      },
                    ),
                    controller: _refreshController,
                    onRefresh: _onRefresh,
                    onLoading: _onLoading,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: MasonryGridView.count(
                        crossAxisCount: 2,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        itemCount: data.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          final animation =
                              Tween<double>(begin: 0.0, end: 1.0).animate(
                            CurvedAnimation(
                              parent: animationController!,
                              curve: Interval(
                                (1 / data.length) * index,
                                1.0,
                                curve: Curves.easeInOut,
                              ),
                            ),
                          );
                          animationController!.forward();

                          return FadeTransition(
                            opacity: animation,
                            child: InvoiceProductListItemsWidget(
                              product: data[index],
                              selectedProducts: widget.selectedProducts,
                              onAddProduct: (ProductDTO product) async {
                                // First check if product has attributes
                                if (product.itemAttributes != null &&
                                    product.itemAttributes!.isNotEmpty &&
                                    product.hasSelectedAttributes != true) {
                                  // Show attribute selection dialog
                                  bool result =
                                      await _showAttributeSelectionDialog(
                                          product);
                                  if (result) {
                                    // Product was added with selected attributes
                                    setState(() {});
                                  }
                                  return;
                                }

                                // Check if the product has barcodes
                                if (product.barcodes != null &&
                                    product.barcodes!.isNotEmpty) {
                                  // Check for final barcodes first
                                  List<Barcodes> finalBarcodes = product
                                      .barcodes!
                                      .where((b) => b.isFinalBarcode == true)
                                      .toList();

                                  if (finalBarcodes.isNotEmpty) {
                                    // Use the first final barcode
                                    Barcodes finalBarcode = finalBarcodes.first;

                                    // Update product with the final barcode
                                    product.barcode = finalBarcode.barCode;
                                    product.barcodeName =
                                        finalBarcode.barCodeName;

                                    // If the barcode has a name, append it to the product title
                                    if (finalBarcode.barCodeName != null &&
                                        finalBarcode.barCodeName!.isNotEmpty) {
                                      product.title =
                                          "${product.title} - ${finalBarcode.barCodeName}";
                                      product.hasSelectedAttributes = true;

                                      // Create a virtual ID for the final barcode product
                                      product.virtualProductId =
                                          "${product.id}_final_${finalBarcode.barCode}";

                                      // Check if this product with this final barcode already exists
                                      int existingIndex = widget
                                          .selectedProducts
                                          .indexWhere((element) =>
                                              element.virtualProductId ==
                                              product.virtualProductId);

                                      if (existingIndex >= 0) {
                                        // Product exists, increase quantity
                                        var existingProduct = widget
                                            .selectedProducts[existingIndex];
                                        double newQuantity =
                                            (existingProduct.quantity ?? 1) + 1;

                                        // Create an updated product with increased quantity
                                        final ProductDTO updatedProduct =
                                            ProductDTO(
                                          id: existingProduct.id,
                                          title: existingProduct.title,
                                          barcode: existingProduct.barcode,
                                          barcodeName:
                                              existingProduct.barcodeName,
                                          code: existingProduct.code,
                                          description:
                                              existingProduct.description,
                                          price: existingProduct.price,
                                          stock: existingProduct.stock,
                                          uniteId: existingProduct.uniteId,
                                          uniteName: existingProduct.uniteName,
                                          category: existingProduct.category,
                                          quantity: newQuantity,
                                          warehouseId:
                                              existingProduct.warehouseId,
                                          warehouseName:
                                              existingProduct.warehouseName,
                                          thumbnail: existingProduct.thumbnail,
                                          hasSelectedAttributes: existingProduct
                                              .hasSelectedAttributes,
                                          virtualProductId:
                                              existingProduct.virtualProductId,
                                        );

                                        // Update the total
                                        updatedProduct.total =
                                            (updatedProduct.price ?? 0) *
                                                newQuantity;

                                        // Remove old product and add updated one
                                        widget.onRemoveProduct(
                                            existingProduct.id ?? 0);
                                        widget.onAddProduct(updatedProduct);
                                        setState(() {});
                                        return;
                                      }
                                    }

                                    // If product doesn't exist or doesn't have attributes, add it
                                    widget.onAddProduct(product);
                                    setState(() {});
                                    return;
                                  }

                                  // If no final barcode, show barcode selection dialog
                                  var selectedBarcode =
                                      await showBarcodeSelectionDialog(
                                    context,
                                    product.barcodes!,
                                  );

                                  if (selectedBarcode != null) {
                                    product.barcode = selectedBarcode.barCode;
                                    product.barcodeName =
                                        selectedBarcode.barCodeName;

                                    // If the selected barcode has a name, append it to the product title
                                    if (selectedBarcode.barCodeName != null &&
                                        selectedBarcode
                                            .barCodeName!.isNotEmpty) {
                                      product.title =
                                          "${product.title} - ${selectedBarcode.barCodeName}";
                                      product.hasSelectedAttributes = true;

                                      // Create a virtual ID for this barcode selection
                                      product.virtualProductId =
                                          "${product.id}_selected_${selectedBarcode.barCode}";

                                      // Check if this product with this barcode already exists
                                      int existingIndex = widget
                                          .selectedProducts
                                          .indexWhere((element) =>
                                              element.virtualProductId ==
                                              product.virtualProductId);

                                      if (existingIndex >= 0) {
                                        // Product exists, increase quantity
                                        var existingProduct = widget
                                            .selectedProducts[existingIndex];
                                        double newQuantity =
                                            (existingProduct.quantity ?? 1) + 1;

                                        // Create an updated product with increased quantity
                                        final ProductDTO updatedProduct =
                                            ProductDTO(
                                          id: existingProduct.id,
                                          title: existingProduct.title,
                                          barcode: existingProduct.barcode,
                                          barcodeName:
                                              existingProduct.barcodeName,
                                          code: existingProduct.code,
                                          description:
                                              existingProduct.description,
                                          price: existingProduct.price,
                                          stock: existingProduct.stock,
                                          uniteId: existingProduct.uniteId,
                                          uniteName: existingProduct.uniteName,
                                          category: existingProduct.category,
                                          quantity: newQuantity,
                                          warehouseId:
                                              existingProduct.warehouseId,
                                          warehouseName:
                                              existingProduct.warehouseName,
                                          thumbnail: existingProduct.thumbnail,
                                          hasSelectedAttributes: existingProduct
                                              .hasSelectedAttributes,
                                          virtualProductId:
                                              existingProduct.virtualProductId,
                                        );

                                        // Update the total
                                        updatedProduct.total =
                                            (updatedProduct.price ?? 0) *
                                                newQuantity;

                                        // Remove old product and add updated one
                                        widget.onRemoveProduct(
                                            existingProduct.id ?? 0);
                                        widget.onAddProduct(updatedProduct);
                                        setState(() {});
                                        return;
                                      }
                                    }

                                    widget.onAddProduct(product);
                                    setState(() {});
                                  }
                                } else {
                                  // Product has no barcodes
                                  // Check if this product already exists without attributes
                                  int existingIndex = widget.selectedProducts
                                      .indexWhere((element) =>
                                          element.id == product.id &&
                                          !element.hasSelectedAttributes);

                                  if (existingIndex >= 0) {
                                    // Product exists, increase quantity
                                    var existingProduct =
                                        widget.selectedProducts[existingIndex];
                                    double newQuantity =
                                        (existingProduct.quantity ?? 1) + 1;

                                    // Create an updated product with increased quantity
                                    final ProductDTO updatedProduct =
                                        ProductDTO(
                                      id: existingProduct.id,
                                      title: existingProduct.title,
                                      barcode: existingProduct.barcode,
                                      barcodeName: existingProduct.barcodeName,
                                      code: existingProduct.code,
                                      description: existingProduct.description,
                                      price: existingProduct.price,
                                      stock: existingProduct.stock,
                                      uniteId: existingProduct.uniteId,
                                      uniteName: existingProduct.uniteName,
                                      category: existingProduct.category,
                                      quantity: newQuantity,
                                      warehouseId: existingProduct.warehouseId,
                                      warehouseName:
                                          existingProduct.warehouseName,
                                      thumbnail: existingProduct.thumbnail,
                                    );

                                    // Update the total
                                    updatedProduct.total =
                                        (updatedProduct.price ?? 0) *
                                            newQuantity;

                                    // Remove old product and add updated one
                                    widget.onRemoveProduct(
                                        existingProduct.id ?? 0);
                                    widget.onAddProduct(updatedProduct);
                                  } else {
                                    // New product, add it
                                    widget.onAddProduct(product);
                                  }
                                  setState(() {});
                                }
                              },
                              onDeleteProduct: (int id) {
                                widget.onRemoveProduct(id);
                                setState(() {});
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),

          // Bottom Save Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.check_circle_outline),
              label: Text(T("Save Selection")),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.newSecondaryColor,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 60,
            color: context.newPrimaryColor.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            T("No products found"),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.newTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("Try changing your search or category filter"),
            style: TextStyle(
              fontSize: 14,
              color: context.newTextColor.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _onRefresh,
            icon: const Icon(Icons.refresh_outlined),
            label: Text(T("Refresh")),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show attribute selection dialog and return true if product was added to cart
  Future<bool> _showAttributeSelectionDialog(ProductDTO product) async {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};
    bool productAdded = false;

    await showDialog(
      context: context,
      barrierDismissible: false, // User must select attributes
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                T('Select Product Options'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: context.newPrimaryColor,
                ),
              ),
              content: Container(
                width: MediaQuery.of(context).size.width * 0.5,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.title ?? '',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Sort attributes by their order property
                      ...(() {
                        final sortedAttributes =
                            List<ItemAttribute>.from(product.itemAttributes!);
                        sortedAttributes.sort(
                            (a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
                        return sortedAttributes;
                      })()
                          .map((attribute) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              attribute.attributeName ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                                color: context.newTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Wrap options in a grid
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: attribute.itemsAttributeOptions!
                                  .map((option) {
                                final isSelected =
                                    selectedOptions[attribute.id] == option;

                                return InkWell(
                                  onTap: () {
                                    setState(() {
                                      selectedOptions[attribute.id!] = option;
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? context.newPrimaryColor
                                          : Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: isSelected
                                            ? context.newPrimaryColor
                                            : Colors.grey.shade300,
                                      ),
                                    ),
                                    child: Text(
                                      option.optionName ?? '',
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : context.newTextColor,
                                        fontWeight: isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                            const SizedBox(height: 16),
                          ],
                        );
                      }).toList(),

                      // Preview of the selected options
                      if (selectedOptions.isNotEmpty) ...[
                        const Divider(),
                        const SizedBox(height: 8),
                        Text(
                          T('Selected Options:'),
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getFormattedSelectedOptions(selectedOptions),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.newPrimaryColor,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    productAdded = false;
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    T('Cancel'),
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: selectedOptions.length ==
                          product.itemAttributes!.length
                      ? () {
                          // All attributes have been selected
                          productAdded = true;
                          Navigator.of(context).pop();
                          _addProductWithSelectedAttributes(
                              product, selectedOptions);
                        }
                      : null, // Disable button if not all attributes are selected
                  child: Text(T('Add to Cart')),
                ),
              ],
            );
          },
        );
      },
    );

    return productAdded;
  }

  // Format the selected options as a string (e.g., "S/Red")
  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    if (selectedOptions.isEmpty) return '';

    // Sort by attribute ID (which should correspond to the attribute order)
    final sortedKeys = selectedOptions.keys.toList()..sort();

    // Join option names with '/'
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }

  // Add product to cart with selected attributes
  void _addProductWithSelectedAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Create a unique key for this product + attributes combination
    // We'll use the selected option IDs to create a "virtual" product ID
    String optionsHash = selectedOptions.entries
        .map((e) => '${e.value.optionId}-${e.key}')
        .join('_');

    String virtualProductId = '${product.id}_$optionsHash';

    // Check if this product with the same attributes already exists in the cart
    int existingIndex = widget.selectedProducts
        .indexWhere((element) => element.virtualProductId == virtualProductId);

    // If the product already exists, just increase its quantity
    if (existingIndex >= 0) {
      List<ProductDTO> updatedList = List.from(widget.selectedProducts);
      double newQuantity =
          (updatedList[existingIndex].quantity ?? 1) + (product.quantity ?? 1);

      // Update the quantity
      updatedList[existingIndex].quantity = newQuantity;

      // Update the total
      double price = updatedList[existingIndex].price ?? 0;
      updatedList[existingIndex].total = price * newQuantity;

      // Update the list
      widget.onAddProduct(updatedList[existingIndex]);
      return;
    }

    // Get the default unit if needed
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
      context,
      listen: false,
    );

    int? unitId = product.uniteId;
    String? unitName = product.uniteName;

    // Apply default unit if product doesn't have a unit and default is set
    if (unitId == null && invoiceSettingsController.defaultUnitId != null) {
      unitId = invoiceSettingsController.defaultUnitId;
      unitName = invoiceSettingsController.defaultUnitName;
    }

    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: unitId,
      uniteName: unitName,
      category: product.category,
      quantity: product.quantity ?? 1.0, // Ensure a default quantity
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      itemAttributes: product.itemAttributes,
      virtualProductId: virtualProductId,
      hasSelectedAttributes: true,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - ${optionsString}';
    }

    // Add to invoice
    widget.onAddProduct(productCopy);
  }
}
