import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';

class InvoiceDetailsBaseInfoWidget extends StatelessWidget {
  const InvoiceDetailsBaseInfoWidget({
    super.key,
    required this.data,
  });

  final InvoiceDto? data;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(15),
      child: Column(
        children: [
          // Invoice code and date section
          _buildInfoSection(
            context,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Icon(Icons.receipt_outlined,
                            size: 18, color: context.newSecondaryColor),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            "${T("Invoice Code")}: ${T(data?.invoiceCode ?? "")}",
                            style: TextStyle(
                              color: context.newTextColor,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: context.newPrimaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      "".myDateFormatter(data?.invoiceDate, isShowTime: false),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Icon(Icons.numbers_outlined,
                      size: 18, color: context.newSecondaryColor),
                  const SizedBox(width: 8),
                  Text(
                    "${T("Local Code")}: ${T(data?.appReferanceCode ?? "")}",
                    style: TextStyle(
                      color: context.newTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Customer and warehouse information
          _buildInfoSection(
            context,
            children: [
              Row(
                children: [
                  Icon(Icons.person_outline,
                      size: 18, color: context.newSecondaryColor),
                  const SizedBox(width: 8),
                  Text(
                    "${T("Customer")}: ",
                    style: TextStyle(
                      color: context.newTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      T(data?.custoemrName ?? ""),
                      style: TextStyle(
                        color: context.newSecondaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.warehouse_outlined,
                      size: 18, color: context.newSecondaryColor),
                  const SizedBox(width: 8),
                  Text(
                    "${T("Warehouses")}: ",
                    style: TextStyle(
                      color: context.newTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      T(data?.salesItems
                              ?.map(
                                (e) => e.warehouseName,
                              )
                              .join(", ") ??
                          ""),
                      style: TextStyle(
                        color: context.newTextColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Financial summary section
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: context.newBackgroundColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: context.newPrimaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                // Section Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    T("Financial Summary"),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: context.newSecondaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Total amount
                _buildFinancialRow(
                  context,
                  label: T("Total Amount"),
                  value: data?.total != null
                      ? data!.total!.toStringAsFixed(2)
                      : '0.00',
                ),

                const Divider(),

                // Payment value
                _buildFinancialRow(
                  context,
                  label: T("Payment"),
                  value: data?.paymentValue != null
                      ? data!.paymentValue!.toStringAsFixed(2)
                      : '0.00',
                ),

                const Divider(),

                // Discount value
                _buildFinancialRow(
                  context,
                  label: T("Discount"),
                  value: data?.discountValue != null
                      ? data!.discountValue!.toStringAsFixed(2)
                      : '0.00',
                ),

                const Divider(),

                // Net total
                _buildFinancialRow(
                  context,
                  label: T("Net Total"),
                  value: data?.total != null && data?.discountValue != null
                      ? (data!.total! - (data?.discountValue ?? 0))
                          .toStringAsFixed(2)
                      : '0.00',
                  isTotal: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context,
      {required List<Widget> children}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: context.newPrimaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _buildFinancialRow(BuildContext context,
      {required String label, required String value, bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: context.newTextColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18 : 14,
            fontWeight: FontWeight.bold,
            color: isTotal ? context.newSecondaryColor : context.newTextColor,
          ),
        ),
      ],
    );
  }
}
