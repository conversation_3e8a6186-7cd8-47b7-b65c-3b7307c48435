import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/model/customer_model.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/components/common_calculator_widget.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/inverntory/invoices/invoice%20details/invoice_details_for_local_screen.dart';
import 'package:inventory_application/services/printer_service.dart';
import 'package:provider/provider.dart';

class POSBottomNavbar extends StatefulWidget {
  const POSBottomNavbar({Key? key}) : super(key: key);

  @override
  State<POSBottomNavbar> createState() => POSBottomNavbarState();
}

class POSBottomNavbarState extends State<POSBottomNavbar> {
  // Public method to save the order - can be called from outside
  void saveOrder() {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    if (saleInvoiceController.selectedSaleInvoiceProduct.isNotEmpty) {
      _processOrder(context, saleInvoiceController);
    } else {
      errorSnackBar(message: T("Cart is empty. Add products before saving."));
    }
  }

  // Public method to return the last order - can be called from outside
  void returnLastOrder() async {
    var lastInvoiceNumber =
        Provider.of<SaleInvoiceController>(context, listen: false)
            .lastAddedInvoiceCode;

    if (lastInvoiceNumber.isEmpty) {
      errorSnackBar(message: T("No last order found"));
      return;
    }

    var confirmation = await showConfirmDialog(
        content: "هل انت متاكد من ارجاع الفاتورة الاخيرة؟",
        title: "تاكيد عميلة الارجاع",
        confirmText: "نعم",
        backText: "لا");

    if (confirmation == true) {
      pleaseWaitDialog(context: context, isShown: true);
      var result =
          await Provider.of<SaleInvoiceController>(context, listen: false)
              .returnLastSaleInvoice();

      // ignore: use_build_context_synchronously
      pleaseWaitDialog(context: context, isShown: false);

      if (result.isSuccess) {
        successSnackBar(
            message: T("تم ارجاع الفاتورة بنجاح برقم ${result.data}"));
      } else {
        errorSnackBar(message: T("Return order failed"));
      }
    }
  }

  // Method to return an order by manually entered code
  void returnOrderByCode() async {
    String? invoiceCode = await _showInvoiceCodeInputDialog();

    if (invoiceCode == null || invoiceCode.isEmpty) {
      return; // User cancelled or didn't enter a code
    }

    // Check if the invoice exists
    var invoiceResult =
        await Provider.of<InvoiceController>(context, listen: false)
            .getInoviceByCode(invoiceLocalCode: invoiceCode);

    if (invoiceResult == null) {
      errorSnackBar(message: T("Invoice not found"));
      return;
    }

    // Confirm return
    var confirmation = await showConfirmDialog(
        content: "هل انت متاكد من ارجاع الفاتورة رقم $invoiceCode؟",
        title: "تاكيد عميلة الارجاع",
        confirmText: "نعم",
        backText: "لا");

    if (confirmation == true) {
      pleaseWaitDialog(context: context, isShown: true);

      // Call the API to return the invoice
      var result =
          await Provider.of<SaleInvoiceController>(context, listen: false)
              .returnSaleInvoiceByCode(invoiceCode);

      // ignore: use_build_context_synchronously
      pleaseWaitDialog(context: context, isShown: false);

      if (result.isSuccess) {
        successSnackBar(
            message: T("تم ارجاع الفاتورة بنجاح برقم ${result.data}"));
      } else {
        errorSnackBar(message: T("Return order failed"));
      }
    }
  }

  // Dialog to input invoice code
  Future<String?> _showInvoiceCodeInputDialog() async {
    final TextEditingController codeController = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(T('Enter Invoice Code')),
          content: TextField(
            controller: codeController,
            decoration: InputDecoration(
              hintText: T('Invoice Code'),
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(T('Cancel')),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(codeController.text),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(T('Return')),
            ),
          ],
        );
      },
    );
  }

  // Public method to view the last order - can be called from outside
  void viewLastOrder() async {
    var lastInvoiceNumber =
        Provider.of<SaleInvoiceController>(context, listen: false)
            .lastAddedInvoiceCode;

    if (lastInvoiceNumber.isEmpty) {
      errorSnackBar(message: T("No last order found"));
      return;
    }

    var result = await Provider.of<InvoiceController>(context, listen: false)
        .getInoviceByCode(invoiceLocalCode: lastInvoiceNumber);

    if (result == null) {
      errorSnackBar(message: T("No last order found"));
      return;
    }

    // ignore: use_build_context_synchronously
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InoviceDetailsForLocalPage(
          id: result.localId ?? 0,
        ),
      ),
    );
  }

  // Public method to show calculator - can be called from outside
  void showCalculator() {
    showBottomSheetWithCalculator(context);
  }

  // Public method to print the last invoice - can be called from outside

  @override
  Widget build(BuildContext context) {
    final saleInvoiceController = Provider.of<SaleInvoiceController>(context);
    final cartProducts = saleInvoiceController.selectedSaleInvoiceProduct;
    final invoice = saleInvoiceController.invoice;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          /// Customer selection
          Expanded(
            flex: 2,
            child: _buildCustomerSelector(context),
          ),
          const SizedBox(width: 16),

          /// Warehouse selection
          Expanded(
            flex: 2,
            child: _buildWarehouseSelector(context),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: _buildSalesmanSelector(context),
          ),
          const SizedBox(width: 16),
          Container(
            height: 50,
            decoration: BoxDecoration(
              color: context.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: context.primaryColor.withOpacity(0.3)),
            ),
            child: PopupMenuButton<String>(
              tooltip: T('Order Actions'),
              offset: const Offset(0, 50),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Icon(Icons.receipt_long, color: context.primaryColor),
                    const SizedBox(width: 8),
                    Text(
                      T('Order Actions'),
                      style: TextStyle(
                        color: context.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(Icons.arrow_drop_down, color: context.primaryColor),
                  ],
                ),
              ),
              itemBuilder: (context) => [
                PopupMenuItem<String>(
                  value: 'view',
                  child: Row(
                    children: [
                      Icon(Icons.receipt, color: context.primaryColor),
                      const SizedBox(width: 8),
                      Text(Platform.isWindows
                          ? T('View last order (Ctrl+V)')
                          : T('View last order')),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'return',
                  child: Row(
                    children: [
                      Icon(Icons.replay, color: context.errorColor),
                      const SizedBox(width: 8),
                      Text(Platform.isWindows
                          ? T('Return Order(F1)')
                          : T('Return Order')),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'return_by_code',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_return, color: context.errorColor),
                      const SizedBox(width: 8),
                      Text(Platform.isWindows
                          ? T('Return Order by Code (F2)')
                          : T('Return Order by Code')),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'view') {
                  viewLastOrder();
                } else if (value == 'return') {
                  returnLastOrder();
                } else if (value == 'return_by_code') {
                  returnOrderByCode();
                }
              },
            ),
          ),

          /// Total amount
          // Expanded(
          //   flex: 2,
          //   child: Column(
          //     crossAxisAlignment: CrossAxisAlignment.end,
          //     children: [
          //       Text(
          //         T('Total'),
          //         style: TextStyle(
          //           color: Colors.grey.shade600,
          //           fontSize: 14,
          //         ),
          //       ),
          //       Text(
          //         '${invoice.totalAfterDiscount?.toStringAsFixed(2) ?? '0.00'}',
          //         style: const TextStyle(
          //           fontSize: 24,
          //           fontWeight: FontWeight.bold,
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // const SizedBox(width: 16),

          /// Save Button
          Expanded(
            flex: 3,
            child: ElevatedButton(
              onPressed: cartProducts.isEmpty
                  ? null
                  : () async {
                      await _processOrder(context, saleInvoiceController);
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.onPrimary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                Platform.isWindows ? T('Save (F10)') : T('Save'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showCloseOrderDialog(
      BuildContext context, SaleInvoiceController controller) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('Close Order')),
        content: Text(T('Are you sure you want to close this order?')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(T('Cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(T('Close Order')),
          ),
        ],
      ),
    );

    if (result == true) {
      controller.removeOrder(controller.activeOrderIndex);
    }
  }

  // Customer selector dropdown
  Widget _buildCustomerSelector(BuildContext context) {
    final customerController = Provider.of<CustomerController>(context);
    final saleInvoiceController = Provider.of<SaleInvoiceController>(context);
    final customers = customerController.customers;
    final selectedCustomerId = saleInvoiceController.invoice.customerId;

    if (customers.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Text(T('No customers available')),
      );
    }

    CustomerModel? selectedCustomer;
    try {
      selectedCustomer = customers.firstWhere(
        (customer) => customer.iD == selectedCustomerId,
      );
    } catch (e) {
      // No customer selected or not found
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedCustomerId,
          isExpanded: true,
          hint: Text(T('Select Customer')),
          icon: const Icon(Icons.arrow_drop_down),
          dropdownColor: Colors.white,
          items: customers.map((customer) {
            return DropdownMenuItem(
              value: customer.iD,
              child: Text(
                customer.name ?? T('Unknown'),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (int? id) {
            if (id != null) {
              final selectedCustomer = customers.firstWhere(
                (customer) => customer.iD == id,
              );
              saleInvoiceController.invoice.customerId = id;
              saleInvoiceController.invoice.custoemrName =
                  selectedCustomer.name;
              saleInvoiceController.notifyListeners();
            }
          },
        ),
      ),
    );
  }

  Widget _buildSalesmanSelector(BuildContext context) {
    final salesmenController = Provider.of<SalesmenController>(context);
    final saleInvoiceController = Provider.of<SaleInvoiceController>(context);
    final salesmen = salesmenController.salesmen;
    final selectedSalesmanId = saleInvoiceController.invoice.salesmanId;

    if (salesmen.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Text(T('No salesmen available')),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedSalesmanId,
          isExpanded: true,
          hint: Text(T('Select Salesman')),
          icon: const Icon(Icons.arrow_drop_down),
          dropdownColor: Colors.white,
          items: salesmen.map((salesman) {
            return DropdownMenuItem(
              value: salesman.id,
              child: Text(
                salesman.name ?? T('Unknown'),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (int? id) {
            if (id != null) {
              final selectedSalesman = salesmen.firstWhere(
                (salesman) => salesman.id == id,
              );
              saleInvoiceController.invoice.salesmanId = id;
              saleInvoiceController.invoice.salesmanName =
                  selectedSalesman.name;
              saleInvoiceController.notifyListeners();
            }
          },
        ),
      ),
    );
  }

  // Warehouse selector dropdown
  Widget _buildWarehouseSelector(BuildContext context) {
    final warehouseController = Provider.of<WarehouseController>(context);
    final saleInvoiceController = Provider.of<SaleInvoiceController>(context);
    final warehouses = warehouseController.warehouses;
    final selectedWarehouseId = saleInvoiceController.invoice.warehouseId;

    if (warehouses.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Text(T('No warehouses available')),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedWarehouseId,
          isExpanded: true,
          hint: Text(T('Select Warehouse')),
          icon: const Icon(Icons.arrow_drop_down),
          dropdownColor: Colors.white,
          items: warehouses.map((warehouse) {
            return DropdownMenuItem(
              value: warehouse.id,
              child: Text(
                warehouse.name ?? T('Unknown'),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (int? id) {
            if (id != null) {
              final selectedWarehouse = warehouses.firstWhere(
                (warehouse) => warehouse.id == id,
              );
              saleInvoiceController.invoice.warehouseId = id;
              saleInvoiceController.invoice.warehouseName =
                  selectedWarehouse.name;
              saleInvoiceController.notifyListeners();
            }
          },
        ),
      ),
    );
  }

  // Show confirmation dialog for canceling order
  Future<void> _confirmCancelOrder(BuildContext context) async {
    final provider = Provider.of<SaleInvoiceController>(context, listen: false);

    if (provider.selectedSaleInvoiceProduct.isEmpty) {
      // If cart is empty, just reset the invoice
      provider.invoice = InvoiceDto();
      provider.notifyListeners();
      return;
    }

    final result = await showConfirmDialog(
      title: T('Cancel Order'),
      content: T(
          'Are you sure you want to cancel this order? All items will be removed.'),
      backText: T('No'),
      confirmText: T('Yes, Cancel'),
    );

    if (result == true) {
      provider.invoice = InvoiceDto();
      provider.selectedSaleInvoiceProduct = [];
      provider.notifyListeners();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(T('Order canceled')),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // Process checkout
  Future<void> _processOrder(
      BuildContext context, SaleInvoiceController controller) async {
    // Show loading dialog
    pleaseWaitDialog(context: context, isShown: true);

    // Save current customer and warehouse selections
    final currentCustomerId = controller.invoice.customerId;
    final currentCustomerName = controller.invoice.custoemrName;
    final currentWarehouseId = controller.invoice.warehouseId;
    final currentWarehouseName = controller.invoice.warehouseName;

    // Save the invoice
    final result = await controller.saveSaleInvoice();

    // Hide loading dialog
    pleaseWaitDialog(context: context, isShown: false);

    if (result.isSuccess) {
      // Show success dialog
      successSnackBar(message: T('Order processed successfully'));

      // Reset invoice but keep customer and warehouse
      controller.invoice = InvoiceDto();
      controller.selectedSaleInvoiceProduct = [];

      // Set invoice type
      controller.invoice.invoiceType = SalesType.Invoice;
      controller.invoice.invoiceTypeName = "Invoice";

      // Restore previous customer and warehouse selections
      controller.invoice.customerId = currentCustomerId;
      controller.invoice.custoemrName = currentCustomerName;
      controller.invoice.warehouseId = currentWarehouseId;
      controller.invoice.warehouseName = currentWarehouseName;

      // Get new invoice number
      await controller.getSaleInvoiceNumber();

      // Set today's date
      controller.invoice.invoiceDate = DateTime.now();

      // Set payment type to cash
      controller.invoice.invoicePaymentType = InvoicePaymentType.chash;

      // Notify listeners
      controller.notifyListeners();
    } else {
      // Show error message
      errorSnackBar(message: T('Failed to process order. Please try again.'));
    }
  }
}
