import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:sqflite/sqflite.dart';

class WarehouseController with ChangeNotifier {
  List<ComboBoxDataModel> warehouses = [];
  int fetchedWarehouseCount = 0;
  bool runningSyncization = false;
  Future<void> fetchWarehouses() async {
    try {
      var fromlocalDatabase = await getWarehouses();
      if (fromlocalDatabase.isNotEmpty) {
        warehouses.clear();
        for (var element in fromlocalDatabase) {
          warehouses.add(ComboBoxDataModel.fromJson(element));
        }
        notifyListeners();
        return;
      }

      warehouses.clear();
      var url = '/Warehouse/GetWarehouse';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            setWarehouse(element["ID"], element["Name"]);
            warehouses.add(ComboBoxDataModel(element["ID"], element["Name"]));
          }
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------
  Future<void> fetchWarehousesFromServer() async {
    try {
      bool isStillThereWarehouses = true;
      fetchedWarehouseCount = 0;
      warehouses.clear();
      while (isStillThereWarehouses) {
        runningSyncization = true;
        var url =
            '/Warehouse/GetWarehouse?skip=$fetchedWarehouseCount&take=200';
        var result = await Api.getOne(action: url);
        if (result != null && result.isSuccess) {
          for (var element in result.data) {
            await setWarehouse(element["ID"], element["Name"]);
            warehouses.add(ComboBoxDataModel(element["ID"], element["Name"]));
            fetchedWarehouseCount++;
            notifyListeners();
          }
          if (result.data.length < 200) {
            isStillThereWarehouses = false;
            runningSyncization = false;
          }
        } else {
          isStillThereWarehouses = false;
          runningSyncization = false;
        }
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

//--------------------------------------------
  Future<int> setWarehouse(int id, String name) async {
    final db = await DatabaseHelper().database;

    // Use insert with conflict resolution to replace if id exists
    final result = await db.insert(
      'Warehouse',
      {'id': id, 'name': name},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return result;
  }

//--------------------------------------------
  Future<List<Map<String, dynamic>>> getWarehouses() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('Warehouse');
    return result;
  }

//--------------------------------------------
  Future<int> getWarehouseCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery('SELECT COUNT(*) as count FROM Warehouse');
    int count = Sqflite.firstIntValue(result) ?? 0;
    return count;
  }

  //--------------------------------------------------------------------------------
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await DatabaseHelper().database;

      await db.transaction((txn) async {
        await txn.delete('Warehouse');
      });

      await fetchWarehousesFromServer();
      return true;
    } catch (e) {
      return false;
    }
  }
//--------------------------------------------
}
