import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/inverntory/home/<USER>';
import '../components/common_snackbar.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

enum BottomNavbarItems { inventory, accounting, report, settings, none }

class HomeLayout extends StatefulWidget {
  const HomeLayout({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  State<HomeLayout> createState() => _HomeLayoutState();
}

class _HomeLayoutState extends State<HomeLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  Widget body = const InventoryHomeScreen();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) {
          return;
        }

        final bool shouldPop = await showConfirmDialog(
              title: T('Exit'),
              content: T("Are you sure?"),
              backText: T("back"),
              confirmText: T("exit"),
            ) ??
            false;

        if (shouldPop) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        // resizeToAvoidBottomInset: false,
        appBar: AppBar(
          toolbarHeight: 40,
          shadowColor: Colors.transparent,
          backgroundColor: context.backgroundColor,
          automaticallyImplyLeading: false,
          title: MyAppBar(
            onClick: ToggleDrawer,
          ),
        ),
        body: Scaffold(
          key: _scaffoldKey,
          // drawer: const MainDrawer(),
          body: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }

  // ignore: non_constant_identifier_names
  void ToggleDrawer() {
    if (_scaffoldKey.currentState!.isDrawerOpen) {
      _scaffoldKey.currentState!.closeDrawer();
      //close drawer, if drawer is open
    } else {
      _scaffoldKey.currentState!.openDrawer();
      //open drawer, if drawer is closed
    }
  }
}

class ApplicationLayout extends StatefulWidget {
  const ApplicationLayout(
      {super.key,
      required this.child,
      this.floatingActionButtonLocation,
      this.floatingActionButton});

  final Widget child;

  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? floatingActionButton;

  @override
  State<ApplicationLayout> createState() => _ApplicationLayoutState();
}

class _ApplicationLayoutState extends State<ApplicationLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    // Usar un enfoque diferente para escritorio para evitar problemas de mouse_tracker
    return _buildScaffold();
  }

  Widget _buildScaffold() {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        toolbarHeight: 40,
        shadowColor: Colors.transparent,
        backgroundColor: context.backgroundColor,
        automaticallyImplyLeading: false,
        title: MyAppBar(
          onClick: () {},
        ),
      ),
      backgroundColor: Colors.white,
      floatingActionButtonLocation: widget.floatingActionButtonLocation,
      floatingActionButton: widget.floatingActionButton,
      resizeToAvoidBottomInset: true,
      body: Builder(
        builder: (context) {
          if (kIsWeb ||
              (Platform.isWindows || Platform.isMacOS || Platform.isLinux)) {
            // Versión optimizada para escritorio
            return Material(
              color: Colors.white,
              child: widget.child,
            );
          } else {
            // Versión normal para móvil
            return widget.child;
          }
        },
      ),
    );
  }
}
