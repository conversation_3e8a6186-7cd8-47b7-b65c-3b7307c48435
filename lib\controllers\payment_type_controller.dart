import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:sqflite/sqflite.dart';

class PaymentTypeController with ChangeNotifier {
  List<ComboBoxDataModel> paymentTypes = [];
  int _fetchedTypeCount = 0;
  bool runningSyncization = false;

  // Getter for the fetchedUnitCount
  int get fetchedTypeCount => _fetchedTypeCount;

  /// Fetches units from local database first, if not available then from server
  Future<void> fetchPaymentTypes() async {
    try {
      var fromLocalDatabase = await getPaymentTypes();
      if (fromLocalDatabase.isNotEmpty) {
        paymentTypes.clear();
        for (var element in fromLocalDatabase) {
          paymentTypes.add(ComboBoxDataModel.fromJson(element));
        }
        notifyListeners();
        return;
      }

      paymentTypes.clear();
      var url = '/PaymentType/GetAllPaymentType';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            setPaymentType(element["ID"], element["Name_AR"]);
            paymentTypes
                .add(ComboBoxDataModel(element["ID"], element["Name_AR"]));
          }
        }
      }

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print("Error fetching paymentTypes: $e");
      }
    }
  }

  /// Fetches paymentTypes from server with pagination
  Future<void> fetchPayemntTypesFromServer() async {
    try {
      paymentTypes.clear();
      runningSyncization = true;
      _fetchedTypeCount = 0;

      var url = '/PaymentType/GetAllPaymentType';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          await setPaymentType(element["ID"], element["Name_AR"]);
          paymentTypes
              .add(ComboBoxDataModel(element["ID"], element["Name_AR"]));
          _fetchedTypeCount++;
        }
      }

      runningSyncization = false;
      notifyListeners();
    } catch (e) {
      runningSyncization = false;
      if (kDebugMode) {
        print("Error fetching paymentTypes from server: $e");
      }
    }
  }

  /// Saves a unit to the local database
  Future<int> setPaymentType(int id, String name) async {
    final db = await DatabaseHelper().database;

    // Use insert with conflict resolution to replace if id exists
    final result = await db.insert(
      'PaymentType',
      {'ID': id, 'Name_AR': name},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return result;
  }

  /// Gets all units from the local database
  Future<List<Map<String, dynamic>>> getPaymentTypes() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('PaymentType');
    return result;
  }

  /// Gets the count of units in the local database
  Future<int> getPayemntTypeCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery('SELECT COUNT(*) as count FROM PaymentType');
    int count = Sqflite.firstIntValue(result) ?? 0;
    return count;
  }

  /// Clears the units table and refetches data from server
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await DatabaseHelper().database;

      await db.transaction((txn) async {
        await txn.delete('PaymentType');
      });

      await fetchPayemntTypesFromServer();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print("Error clearing and refetching units: $e");
      }
      return false;
    }
  }
}
