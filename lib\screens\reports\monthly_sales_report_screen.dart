import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/base_report_screen.dart';
import 'package:inventory_application/screens/reports/widgets/monthly_sales_report_widget.dart';
import 'package:provider/provider.dart';

class MonthlySalesReportScreen extends BaseReportScreen {
  const MonthlySalesReportScreen({Key? key}) : super(key: key);

  @override
  State<MonthlySalesReportScreen> createState() => _MonthlySalesReportScreenState();
}

class _MonthlySalesReportScreenState extends BaseReportScreenState<MonthlySalesReportScreen> {
  @override
  ReportType get reportType => ReportType.monthlySales;

  @override
  String getReportTitle() => T('Monthly Sales');

  @override
  IconData getReportIcon() => Icons.date_range;

  @override
  Color getHeaderColor() => const Color(0xFF4CC9F0);

  @override
  Color getAccentColor() => const Color(0xFF4361EE);

  @override
  Future<void> generateReport() async {
    setLoading(true);

    try {
      final reportController = Provider.of<ReportController>(context, listen: false);
      await reportController.generateMonthlySalesReport();
    } catch (e) {
      errorSnackBar(message: 'Error generating monthly sales report: $e');
    } finally {
      setLoading(false);
    }
  }

  @override
  Widget buildReportContent(ReportController reportController) {
    final report = reportController.monthlySalesReport;
    if (report == null) {
      return Center(
        child: Text(T('No monthly report data available')),
      );
    }
    return MonthlySalesReportWidget(report: report);
  }
}
