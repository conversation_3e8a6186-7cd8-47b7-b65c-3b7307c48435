import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';

Widget invoiceProductSearchWidget(
    {required BuildContext context,
    required Function onchange,
    String? txt,
    BorderRadius? borderRadius,
    EdgeInsets? padding}) {
  return Row(
    children: [
      const Icon(Icons.search),
      Expanded(
        child: Container(
          padding:
              padding ?? const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
          color: Colors.white,
          child: TextField(
            decoration: InputDecoration(
              hintText: txt ?? T('Search products...'),
              border: OutlineInputBorder(
                borderRadius: borderRadius ?? BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              fillColor: Colors.grey[200],
              filled: true,
            ),
            onChanged: (value) {
              onchange(value);
            },
          ),
        ),
      ),
    ],
  );
}
