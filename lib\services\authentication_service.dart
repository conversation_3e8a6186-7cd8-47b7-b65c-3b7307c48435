import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/model/roles_with_permission_model.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

class AuthenticationService with ChangeNotifier {
  List<RolesWithPermission> rolesAndPermissions = [];
  List<int> userRoles = [];
  List<int> userModules = [];
  List<int> userBranches = [];
  bool isMainAdmin = false;

  Future<void> fetchRolesWithPermissions(
      {bool? resetAndRefresh = false}) async {
    try {
      if (resetAndRefresh == true) {
        await clearRolesAndPermissions();
      }
      var fromlocalDatabase = await getRolesWithPermissions();
      if (fromlocalDatabase.isNotEmpty) {
        rolesAndPermissions.clear();
        for (var element in fromlocalDatabase) {
          rolesAndPermissions.add(RolesWithPermission.fromJson(element));
        }
        notifyListeners();
        return;
      }

      rolesAndPermissions.clear();
      var url = '/Security/GetRolesWithPermission';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            setRolesWithPermissions(RolesWithPermission.fromJson(element));
            rolesAndPermissions.add(RolesWithPermission.fromJson(element));
          }
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> clearRolesAndPermissions() async {
    final db = await DatabaseHelper().database;

    await db.transaction((txn) async {
      await txn.delete('Roles');
      await txn.delete('Permissions');
    });
  }

  Future<List<Map<String, dynamic>>> getRolesWithPermissions() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('Roles');
    if (result.isNotEmpty) {
      var permissionsResult = await db.query('Permissions');
      for (var element in result) {
        int roleId = element['RoleID'] as int;
        element['Permissions'] = permissionsResult
            .where((permission) => permission['RoleID'] == roleId)
            .toList();
      }
    }
    return result;
  }

  Future<int> setRolesWithPermissions(RolesWithPermission model) async {
    final db = await DatabaseHelper().database;

    // Use insert with conflict resolution to replace if id exists
    final result = await db.insert(
      'Roles',
      {'RoleID': model.roleID, 'RoleName': model.roleName},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    for (var element in model.permissions!) {
      await db.insert(
        'Permissions',
        {
          'PermissionID': element.permissionID,
          'PermissionName': element.permissionName,
          'Parent_ID': element.parentID,
          'RoleID': model.roleID,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    return result;
  }

  void setUserRoles(String roles) {
    userRoles = roles.split(',').map((e) => int.parse(e)).toList();

    notifyListeners();
  }

  void setUserModules(String modules) {
    userModules = modules.split(',').map((e) => int.parse(e)).toList();
    notifyListeners();
  }

  void setUserBranches(String branches) {
    userBranches = branches.split(',').map((e) => int.parse(e)).toList();
    notifyListeners();
  }

  void setIsMainAdmin(bool value) {
    isMainAdmin = value;
    notifyListeners();
  }

  // bool hasRole(int PermissionsId) {
  //   return userRoles.contains(role);
  // }

  bool hasPermission(int permissionId) {
    if (isMainAdmin) return true;
    for (var role in userRoles) {
      for (var element in rolesAndPermissions) {
        if (element.roleID == role) {
          for (var permission in element.permissions!) {
            if (permission.permissionID == permissionId) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  bool hasModule(int module) {
    if (isMainAdmin) return true;
    return userModules.contains(module);
  }

  bool hasBranch(int branch) {
    if (isMainAdmin) return true;
    return userBranches.contains(branch);
  }
}
