import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class RegisterANewDebtScreen extends StatefulWidget {
  const RegisterANewDebtScreen({super.key});

  @override
  State<RegisterANewDebtScreen> createState() => _RegisterANewDebtScreenState();
}

class _RegisterANewDebtScreenState extends State<RegisterANewDebtScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
