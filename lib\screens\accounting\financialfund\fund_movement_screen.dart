import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class FundMovementScreen extends StatefulWidget {
  const FundMovementScreen({super.key});

  @override
  State<FundMovementScreen> createState() => _FundMovementScreenState();
}

class _FundMovementScreenState extends State<FundMovementScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
