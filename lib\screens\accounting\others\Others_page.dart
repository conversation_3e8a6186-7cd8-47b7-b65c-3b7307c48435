import 'package:flutter/material.dart';
import 'package:inventory_application/screens/accounting/accounts/accounting_home_screen.dart';
import 'package:inventory_application/screens/accounting/others/exchange_rates_screen.dart';
import 'package:inventory_application/screens/accounting/others/expenses_recording_screen.dart';
import 'package:inventory_application/screens/accounting/others/revenue_recording_screen.dart';
import 'package:inventory_application/screens/accounting/others/search_by_registration_number_Screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class OthersPage extends StatefulWidget {
  const OthersPage({super.key});

  @override
  State<OthersPage> createState() => _OthersPageState();
}

class _OthersPageState extends State<OthersPage> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16.0,
          mainAxisSpacing: 16.0,
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const ExpensesRecordingScreen(),
                ));
              },
              child: PlaceCard(
                image: Image.asset(
                  "assets/images/home/<USER>",
                  height: 75,
                ),
                iconColor: Colors.amber,
                title: 'تسجيل المصاريف',
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const RevenueRecodingScreen(),
                ));
              },
              child: PlaceCard(
                image: Image.asset(
                  "assets/images/home/<USER>",
                  height: 75,
                ),
                iconColor: Colors.amber,
                title: 'تسجيل الايرادات الخارجية',
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) =>
                      const SearchByRegistrationNumberScreen(),
                ));
              },
              child: PlaceCard(
                image: Image.asset(
                  "assets/images/home/<USER>",
                  height: 75,
                ),
                iconColor: Colors.yellow,
                title: 'البحث في رقم القيد',
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const ExchangeRatesScreen(),
                ));
              },
              child: PlaceCard(
                image: Image.asset(
                  "assets/images/home/<USER>",
                  height: 75,
                ),
                iconColor: Colors.yellow,
                title: 'اسعار الصرف',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
