import 'dart:io';

import 'package:file_selector/file_selector.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path/path.dart' as path;

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._();
  static Database? _database;

  DatabaseHelper._();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();

    final path = join(dbPath, 'invoices.db');

    return await openDatabase(
      path,
      version: 7,
      onUpgrade: (db, oldVersion, newVersion) {
        if (oldVersion < 7) {
          // // New table added in version 2
          db.execute('''
          CREATE TABLE Roles (
            RoleID INTEGER PRIMARY KEY,
            RoleName TEXT
          )
        ''');
          db.execute('''
          CREATE TABLE Permissions (
            PermissionID INTEGER PRIMARY KEY,
            PermissionName TEXT,
            Parent_ID INTEGER,
            RoleID INTEGER,
            FOREIGN KEY (RoleID) REFERENCES Roles(RoleID)
          )
        ''');
        }
      },
      onCreate: (db, version) async {
        // Create Invoice table first
        await db.execute('''
        CREATE TABLE Invoice (
         ID INTEGER PRIMARY KEY AUTOINCREMENT,
          data TEXT,
          status TEXT,
          localCode TEXT,
          type TEXT
        )
      ''');

        await db.execute('''
        CREATE TABLE Warehouse (
         id INTEGER PRIMARY KEY,
          name TEXT
        )
      ''');
        await db.execute('''
        CREATE TABLE Salesmen (
         id INTEGER PRIMARY KEY,
          name TEXT
        )
      ''');
        await db.execute('''
        CREATE TABLE Customer (
          Code TEXT,
          Name TEXT,
          Name_En TEXT,
          Name_Tr TEXT,
          Name_Localized TEXT,
          Customer_Type_Name TEXT,
          Accounting_Name TEXT,
          Accounting_Number TEXT,
          ID INTEGER,
          local_Id INTEGER PRIMARY KEY AUTOINCREMENT,
          status TEXT,
          Phone TEXT,
          Notes TEXT,
          Address TEXT

        )
      ''');
        await db.execute('''
        CREATE TABLE ProductModel (
          ID INT PRIMARY KEY,
          Name VARCHAR(255),
          Code VARCHAR(255),
          Parent_ID INT,
          Parent_Name VARCHAR(255),
          Level_Type INT,
          isParent BIT
        )
      ''');
        await db.execute('''
        CREATE TABLE Inventory (
          ID2 INTEGER PRIMARY KEY AUTOINCREMENT,
          ID INT,
          Store_Name VARCHAR(255),
          Quantity_Balance INT,
          Product_ID INT,  -- Foreign key to link to ProductModel
          FOREIGN KEY (Product_ID) REFERENCES ProductModel(ID)
        )
      ''');
        await db.execute('''
        CREATE TABLE ItemPrice (
           ID INT PRIMARY KEY,
            Item_ID INT,
            Unit_ID INT,
            Unit_Name VARCHAR(255),
            Is_Defult BIT,
            Sales_Price INT,
            Product_ID INT,    -- Foreign key to link to ProductModel
            FOREIGN KEY (Product_ID) REFERENCES ProductModel(ID)
        )
      ''');
        await db.execute('''
        CREATE TABLE Barcodes (
          ID2 INTEGER PRIMARY KEY AUTOINCREMENT,
          BarCode VARCHAR(255),
          BarCodeName VARCHAR(255),
          IsFinalBarcode BIT,
          ItemID INT,  -- Foreign key to link to ProductModel
          FOREIGN KEY (ItemID) REFERENCES ProductModel(ID)
        )
      ''');
        await db.execute('''
        CREATE TABLE ItemAttributes (
            ID INTEGER PRIMARY KEY,
            Attribute_Type_Id INTEGER,
            Attribute_Name TEXT,
            Item_ID INTEGER,
            Attribute_Order INTEGER,
            FOREIGN KEY (Item_ID) REFERENCES ProductModel(ID)
        )
    ''');

        await db.execute('''
        CREATE TABLE ItemsAttributeOptions (
            ID INTEGER PRIMARY KEY,
            Attribute_ID INTEGER,
            Option_ID INTEGER,
            Option_Name TEXT,
            FOREIGN KEY (Attribute_ID) REFERENCES ItemAttributes(ID)
        )
    ''');
        await db.execute('''
        CREATE TABLE Category (
           ID INTEGER PRIMARY KEY,
            Name TEXT,
            Code TEXT,
            Parent_ID INTEGER,
            Parent_Name TEXT,
            Level_Type INTEGER,
            IsParent BOOLEAN
    )
''');
        await db.execute('''
            CREATE TABLE User (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
                usernameOrEmail TEXT,
                token TEXT,
                password TEXT
        )
      ''');
        await db.execute('''
            CREATE TABLE Units (
              id INTEGER PRIMARY KEY,
              name TEXT
            )
      ''');
        await db.execute('''
            CREATE TABLE PaymentType (
              ID INTEGER PRIMARY KEY,
              Name_AR TEXT
            )
      ''');
        await db.execute('''
        CREATE TABLE InventoryOperation (
         ID INTEGER PRIMARY KEY AUTOINCREMENT,
          data TEXT,
          status TEXT,
          localCode TEXT,
          type TEXT
        )
      ''');
        await db.execute('''
      CREATE TABLE Roles (
        RoleID INTEGER PRIMARY KEY,
        RoleName TEXT
      )
    ''');

        await db.execute('''
      CREATE TABLE Permissions (
        PermissionID INTEGER PRIMARY KEY,
        PermissionName TEXT,
        Parent_ID INTEGER,
        RoleID INTEGER,
        FOREIGN KEY (RoleID) REFERENCES Roles(RoleID)
      )
    ''');
        await db.execute('''
      CREATE TABLE StocktakingDrafts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        stocktakingId TEXT UNIQUE,
        warehouseId INTEGER,
        warehouseName TEXT,
        startDate TEXT,
        items TEXT,
        status TEXT,
        lastModified TEXT
      )
    ''');
      },
    );
  }

//-------------------------------------------------------------------------------
  Future<void> selectAndRestoreDatabase() async {
    try {
      // Step 1: Open file picker to select a file
      final XFile? file = await openFile(
        acceptedTypeGroups: [
          const XTypeGroup(label: 'Database', extensions: ['db'])
        ],
      );

      if (file != null) {
        // Step 2: Get file path and read file
        final String selectedFilePath = file.path;
        print('Selected file path: $selectedFilePath');

        // Step 3: Define the destination for the database

        final Directory appDir = await getApplicationDocumentsDirectory();
        final String dbPath = path.join(appDir.path, 'backup_database.db');

        // Step 4: Copy the selected file to the app's database directory
        final File selectedFile = File(selectedFilePath);
        await selectedFile.copy(dbPath);

        // Step 5: Notify the user
      } else {
        // User canceled the file picker
      }
    } catch (e) {
      // Handle errors
    }
  }

//-----------------------------------------------------------------------
  Future<void> createBackup() async {
    try {
      // Request storage permissions
      final status = await Permission.manageExternalStorage.request();
      if (status.isGranted) {
        final backupFolder = await getBackupFolder();
        // Access Downloads directory
        final downloadsDir = Directory(backupFolder);
        if (!downloadsDir.existsSync()) {
          await Directory(downloadsDir.path).create(recursive: true);
        }

        // Define backup file name with timestamp
        final String timestamp = DateTime.now().toString().replaceAll(':', '-');
        final String backupFileName = 'backup_database-$timestamp.db';
        final String backupFilePath =
            path.join(downloadsDir.path, backupFileName);

        // Replace this with your database path
        final currentDbPath = await getDatabasesPath();
        final currentPath = join(currentDbPath, 'invoices.db');
        final File currentDatabaseFile = File(currentPath);

        if (await currentDatabaseFile.exists()) {
          // Copy database file to Downloads
          await currentDatabaseFile.copy(backupFilePath);
          print('Backup created at: $backupFilePath');
        } else {
          print('Database file does not exist.');
        }
      } else {
        print('Storage permission not granted.');
      }
    } catch (e) {
      print('Failed to create backup: $e');
    }
  }

//-----------------------------------------------------------------------
  Future<String> getBackupFolder() async {
    final prefs = LocaleManager.instance;
    String? folderPath = prefs.getStringValue(PreferencesKeys.BackupPath);

    // If no backup folder is set, return a default folder
    if (folderPath.isEmpty) {
      folderPath = '/storage/emulated/0/Pal-ERP-Backup'; // Default folder
      await setBackupFolder();
    }

    return folderPath;
  }

//-----------------------------------------------------------------------
  Future<void> setBackupFolder() async {
    try {
      final prefs = LocaleManager.instance;
      final String? folderPath = await getDirectoryPath();

      if (folderPath != null) {
        // Save the folder path in shared preferences

        prefs.setString(PreferencesKeys.BackupPath, folderPath);
        print('Backup folder set to: $folderPath');
      } else {
        prefs.setString(
            PreferencesKeys.BackupPath, '/storage/emulated/0/Pal-ERP-Backup');
        print('No folder selected');
      }
    } catch (e) {
      print('Failed to set backup folder: $e');
    }
  }
}
