import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:provider/provider.dart';

class InventoryOperationIncomingController with ChangeNotifier {
  List<ProductDTO> selectedIncomingProduct = [];
  String? returnTransfarCode;
  InventoryOperationModel inventoryIncoming = InventoryOperationModel();

  void addProductToSelectedList(ProductDTO model) {
    // IMPORTANT: Apply default unit first, before any other processing
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
      navigatorKey.currentContext!,
      listen: false,
    );

    // Force apply default unit if default is set (regardless of whether product already has a unit)
    if (invoiceSettingsController.defaultUnitId != null) {
      model.uniteId = invoiceSettingsController.defaultUnitId;
      model.uniteName = invoiceSettingsController.defaultUnitName;
    }

    // Apply default warehouse if available
    if (inventoryIncoming.toStoreID != null) {
      model.warehouseId = inventoryIncoming.toStoreID;
      model.warehouseName = inventoryIncoming.toStoreName ?? "";
    }

    // Create a copy of the model to ensure it's a completely separate object
    ProductDTO newProduct = ProductDTO(
        id: model.id,
        title: model.title,
        barcode: model.barcode,
        barcodeName: model.barcodeName,
        code: model.code,
        description: model.description,
        price: model.price,
        total: model.total,
        discountValue: model.discountValue,
        stock: model.stock,
        uniteId: model.uniteId,
        uniteName: model.uniteName,
        category: model.category,
        quantity: model.quantity,
        warehouseId: model.warehouseId,
        warehouseName: model.warehouseName,
        thumbnail: model.thumbnail,
        warehouse: model.warehouse,
        units: model.units,
        barcodes: model.barcodes,
        hasSelectedAttributes: model.hasSelectedAttributes,
        virtualProductId: model.virtualProductId,
        itemAttributes: model.itemAttributes);

    // Check if this exact product + attributes combination already exists
    int existingIndex = -1;
    if (model.virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      existingIndex = selectedIncomingProduct.indexWhere(
        (element) => element.virtualProductId == model.virtualProductId,
      );
    } else {
      // Otherwise, fall back to checking by regular ID
      existingIndex = selectedIncomingProduct.indexWhere(
        (element) =>
            element.id == model.id &&
            element.barcode == model.barcode &&
            !element.hasSelectedAttributes,
      );
    }

    // If we found the exact same product, increase its quantity instead of adding a new one
    if (existingIndex >= 0) {
      // Create a new list to trigger UI update
      List<ProductDTO> updatedList = List.from(selectedIncomingProduct);

      double newQuantity = (updatedList[existingIndex].quantity ?? 1) +
          (newProduct.quantity ?? 1);

      // Update the quantity
      updatedList[existingIndex].quantity = newQuantity;

      // Update the total
      double price = updatedList[existingIndex].price ?? 0;
      updatedList[existingIndex].total = price * newQuantity;

      // Replace the list with the updated one
      selectedIncomingProduct = updatedList;
    } else {
      // Add as a new item
      selectedIncomingProduct.add(newProduct);
    }

    calculateInvoiceTotal();
    notifyListeners();
  }

  ///--------------------------------------------------------------------------
  void deleteProductFromSelectedList(int id, {String? virtualProductId}) {
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      selectedIncomingProduct.removeAt(productIndex);
      calculateInvoiceTotal();
      notifyListeners();
    }
  }

  //--------------------------------------------------------------------------
  //--------------------------------------------------------------------------
  void updateProductPrice(int id, double price, [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        selectedIncomingProduct[productIndex].price = price;
        selectedIncomingProduct[productIndex].total =
            price * (selectedIncomingProduct[productIndex].quantity ?? 1);
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        selectedIncomingProduct[productIndex].price = price;
        selectedIncomingProduct[productIndex].total =
            price * (selectedIncomingProduct[productIndex].quantity ?? 1);
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

  //--------------------------------------------------------------------------
  //--------------------------------------------------------------------------
  void updateProductQuantity(int id, double quantity,
      [String? virtualProductId]) async {
    // Find the product index
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      // Update the quantity
      selectedIncomingProduct[productIndex].quantity = quantity;

      // Update the total amount based on the new quantity and existing price
      double price = selectedIncomingProduct[productIndex].price ?? 0;
      selectedIncomingProduct[productIndex].total = price * quantity;

      // Calculate the invoice total
      calculateInvoiceTotal();

      // Notify listeners for UI update
      notifyListeners();

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 100));

      // Notify again to ensure UI is updated
      notifyListeners();
    }
  }

  //--------------------------------------------------------------------------
  void updateProductUnit(int id, ItemPriceDTO unit,
      [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedIncomingProduct[productIndex].uniteId = unit.unitID;
        selectedIncomingProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedIncomingProduct[productIndex].price ?? 0;
        double quantity = selectedIncomingProduct[productIndex].quantity ?? 1;
        selectedIncomingProduct[productIndex].total = price * quantity;
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedIncomingProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedIncomingProduct[productIndex].uniteId = unit.unitID;
        selectedIncomingProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedIncomingProduct[productIndex].price ?? 0;
        double quantity = selectedIncomingProduct[productIndex].quantity ?? 1;
        selectedIncomingProduct[productIndex].total = price * quantity;
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

  //--------------------------------------------------------------------------
  Future<dynamic> getItemByBarcode({String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) {
        return false;
      }

      // Notify that we're starting to process
      notifyListeners();

      // Use getItemByBarcodeWithWriting instead to handle final barcodes
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (result != null) {
        // If the product has attributes but they haven't been selected yet,
        // and it's not a final barcode, return the product so the UI can show the attribute dialog
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          return result; // Return the product so the UI can show the attribute selection dialog
        }

        // First check if we already have this product with the same barcode
        int existingIndex = -1;

        // Handle products with virtual ID (products with attributes)
        if (result.virtualProductId != null) {
          existingIndex = selectedIncomingProduct.indexWhere(
            (element) => element.virtualProductId == result.virtualProductId,
          );
        } else {
          // For regular products, check by barcode or product ID
          for (int i = 0; i < selectedIncomingProduct.length; i++) {
            var product = selectedIncomingProduct[i];
            if ((product.barcode == barcode || product.id == result.id) &&
                !product.hasSelectedAttributes) {
              existingIndex = i;
              break;
            }
          }
        }

        // If we found an existing product, increase its quantity
        if (existingIndex >= 0) {
          var existingProduct = selectedIncomingProduct[existingIndex];
          double newQuantity = (existingProduct.quantity ?? 1) + 1;

          // Update the quantity
          selectedIncomingProduct[existingIndex].quantity = newQuantity;

          // Update the total
          double price = selectedIncomingProduct[existingIndex].price ?? 0;
          selectedIncomingProduct[existingIndex].total = price * newQuantity;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        } else {
          // This is a new product, add it to the list
          // Apply default unit if default is set
          final invoiceSettingsController =
              Provider.of<InvoiceSettingsController>(
            navigatorKey.currentContext!,
            listen: false,
          );

          // Force apply default unit if default is set
          if (invoiceSettingsController.defaultUnitId != null) {
            result.uniteId = invoiceSettingsController.defaultUnitId;
            result.uniteName = invoiceSettingsController.defaultUnitName;
          }

          // Set warehouse if available
          if (inventoryIncoming.toStoreID != null) {
            result.warehouseId = inventoryIncoming.toStoreID;
            result.warehouseName = inventoryIncoming.toStoreName ?? "";
          }

          // Ensure quantity is at least 1
          result.quantity = result.quantity ?? 1;

          // Calculate total
          result.total = (result.price ?? 0) * result.quantity!;

          // Add to list
          selectedIncomingProduct.add(result);

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        }
      }

      return false;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return false;
    }
  }

  //---------------------------------------------------------------------------
  void setProductWarehouse(int id, int warehouseId, String warehouseName,
      [String? virtualProductId]) {
    ProductDTO product;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      product = selectedIncomingProduct.firstWhere(
        (element) => element.virtualProductId == virtualProductId,
        orElse: () => selectedIncomingProduct.firstWhere(
          (element) => element.id == id,
          orElse: () => ProductDTO(),
        ),
      );
    } else {
      // Otherwise use the regular product ID
      product = selectedIncomingProduct.firstWhere(
        (element) => element.id == id,
        orElse: () => ProductDTO(),
      );
    }

    if (product.id != null) {
      product.warehouseId = warehouseId;
      product.warehouseName = warehouseName;
      notifyListeners();
    }
  }

  //---------------------------------------------------------------------------
  Future<String> getIncomingNumber() async {
    try {
      var number =
          await InventoryOperationCounterGenerator.getNextCounterByType(
              InventoryOperationType.Incoming);
      inventoryIncoming.aPPReferanceCode ??= number;

      notifyListeners();
      return inventoryIncoming.aPPReferanceCode ?? "";
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------

  void calculateInvoiceTotal() {
    double total = 0;
    double totalQuantity = 0;

    for (var product in selectedIncomingProduct) {
      total += product.total ?? 0;
      totalQuantity += product.quantity ?? 0;
    }

    inventoryIncoming.total = total;

    // Ensure UI updates with immediate notification
    notifyListeners();

    // Add a small delay and notify again to ensure UI updates
    Future.delayed(const Duration(milliseconds: 100), () {
      notifyListeners();
    });
  }

  //---------------------------------------------------------------------------
  //---------------------------------------------------------- -----------------
  Future<ResponseResultModel> saveInventoryIncoming() async {
    try {
      var url = '/InventoryOperation/Manage?transactions_type=Incoming';
      final db = InventoryOperationController();

      var mapeditems =
          mapListProductDtoToInventoryOperationItem(selectedIncomingProduct);
      inventoryIncoming.inventoryOperationItems = mapeditems;

      // mapedInvoice.paidAmount ??= 0.0;

      inventoryIncoming.operationType = InventoryOperationType.Incoming.index;
      inventoryIncoming.entryDateFormated =
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());
      if (inventoryIncoming.iD == null || (inventoryIncoming.iD ?? 0) == 0) {
        inventoryIncoming.entryDate = DateTime.now();
        inventoryIncoming.code = "*";
      }

      if (inventoryIncoming.aPPReferanceCode == null ||
          inventoryIncoming.aPPReferanceCode == "") {
        inventoryIncoming.aPPReferanceCode = await getIncomingNumber();
      } else {
        if (await InventoryOperationCounterGenerator.checkIfCounterUsed(
            InventoryOperationType.Incoming,
            inventoryIncoming.aPPReferanceCode ?? "")) {
          inventoryIncoming.aPPReferanceCode = await getIncomingNumber();
        }
      }
      inventoryIncoming.iD ??= 0;
      // mapedInvoice.iD = 0;
      // mapedInvoice.vATPercent = 0;

      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateInventoryOperation(
            SqlLiteInvoiceModel(
                    data: jsonEncode(inventoryIncoming.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                    id: null,
                    localCode: inventoryIncoming.aPPReferanceCode,
                    type: InventoryOperationType.Incoming.name)
                .toJson());
        inventoryIncoming = InventoryOperationModel();
        selectedIncomingProduct.clear();
        if (inventoryIncoming.iD == 0) {
          await InventoryOperationCounterGenerator.setCounterByTypeAuto(
              type: InventoryOperationType.Incoming);
        }
        notifyListeners();

        return ResponseResultModel(
            isSuccess: true, data: InvoiceResponseDto(localId: localId));
      }

      var result = await Api.post(
        action: url,
        body: inventoryIncoming.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          if (inventoryIncoming.iD == 0) {
            await InventoryOperationCounterGenerator
                .setInventoryOperationCounterInServer();
            await InventoryOperationCounterGenerator.setCounterByTypeAuto(
                type: InventoryOperationType.Incoming);
          }
          var response = InvoiceResponseDto.fromJson(result.data);
          inventoryIncoming.iD = response.id;
          inventoryIncoming.code = response.code;
          var localId = await db.insertOrUpdateInventoryOperation(
              SqlLiteInvoiceModel(
                      data: jsonEncode(inventoryIncoming.toJson()),
                      status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                      id: null,
                      localCode: inventoryIncoming.aPPReferanceCode,
                      type: InventoryOperationType.Incoming.name)
                  .toJson());
          response.localId = localId;
          inventoryIncoming = InventoryOperationModel();
          selectedIncomingProduct.clear();
          // await db.saveReturnToEcommerce(mapedInvoice);
          notifyListeners();
          return ResponseResultModel(data: response, isSuccess: true);
        }
      }

      return ResponseResultModel(
          isSuccess: false, message: [result?.message ?? "Unknown error"]);
    } catch (e) {
      print("Error in saveInventoryIncoming: $e");
      return ResponseResultModel(isSuccess: false, message: [e.toString()]);
    }
  }
}
