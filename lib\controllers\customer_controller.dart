import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/base/network/ecommerce_api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/customer_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:sqflite/sqflite.dart';

class CustomerController with ChangeNotifier {
  List<CustomerModel> customers = [];
  bool runningSyncization = false;
  int fetchedCustomersCount = 0;

  Future<void> fetchCustomers() async {
    if (runningSyncization) return;
    try {
      var fromlocalDatabase = await getCustomers();
      if (await isThereNetworkConnection() == false) {
        customers.clear();
        customers.addAll(
          fromlocalDatabase.map((e) => CustomerModel.fromJson(e)).toList(),
        );
        notifyListeners();
        return;
      }

      customers.clear();
      bool isStillThereCustomers = true;
      fetchedCustomersCount = 0;
      while (isStillThereCustomers) {
        runningSyncization = true;

        var url = 'Customer/GetCustomers?take=200&skip=$fetchedCustomersCount';
        var result = await Api.getOne(action: url);
        if (result != null && result.isSuccess) {
          // customers.add(CustomerModel(iD: 0, name: "زبون عام"));
          for (var element in result.data) {
            var customer = CustomerModel.fromJson(element);
            // customer.status = CustomerSyncStatus.synced;
            insertCustomerLocally(customer);
            fetchedCustomersCount++;
            notifyListeners();
            customers.add(customer);
          }
          if (result.data.length < 200) {
            isStillThereCustomers = false;
            runningSyncization = false;
          }
        } else {
          isStillThereCustomers = false;
          runningSyncization = false;
        }
        notifyListeners();
      }
    } catch (e) {
      print(e);
    }
  }

  //---------------------------------------------------------------------

  Future<bool> insertCustomer(CustomerModel customer) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        await syncCustomerWithServer(customer);
        await saveCustomerToEcommerce(customer);
        // customer.status = CustomerSyncStatus.synced;
        return await insertCustomerLocally(customer);
      } else {
        // customer.status = CustomerSyncStatus.pending;
        return await insertCustomerLocally(customer);
      }
    } catch (e) {
      print("Error in inserting customer: $e");
      return false;
    }
  }

  //---------------------------------------------------------------------
  Future<int?> syncCustomerWithServer(CustomerModel customer) async {
    try {
      var url = 'Customer/manage';
      var result = await Api.post(
        body: customer.toJson(),
        action: url,
      );

      if (result != null && result.isSuccess) {
        var responseData = result.data;
        print("Response from server: $responseData");

        if (responseData != null) {
          customer.iD = responseData['ID'];
          customer.code = responseData['Code'];
          await saveCustomerToEcommerce(customer);
          return customer.iD;
        }
      }
      return 0;
    } catch (e) {
      print("Error syncing customer: $e");
      return 0;
    }
  }

  //---------------------------------------------------------------------
  Future<void> syncUnsyncedCustomers() async {
    try {
      final db = await DatabaseHelper().database;

      // Fetch all unsynced customers (ID == 0)
      //   var customers = await db.query('Customer');
      List<Map<String, dynamic>> unsyncedCustomers = await db.query(
        'Customer',
        where: 'ID = ?',
        whereArgs: [0],
      );

      if (unsyncedCustomers.isEmpty) {
        print("No unsynced customers to process.");
        return;
      }

      for (var customerMap in unsyncedCustomers) {
        // Convert the map to a CustomerModel object
        var customer = CustomerModel.fromJson(customerMap);

        // Sync customer with server
        int customerId = await syncCustomerWithServer(customer) ?? 0;

        if (customerId > 0) {
          // Update the local record with the new ID from the server
          await db.update(
            'Customer',
            {'ID': customerId}, // Update with the server-generated ID
            where:
                'local_Id = ?', // Use the SQLite ROWID for unique identification
            whereArgs: [customerMap['local_Id']],
          );
          print("Customer synced successfully: ${customer.iD}");
        } else {
          print("Failed to sync customer: ${customer.name}");
        }
      }
    } catch (e) {
      print("Error syncing unsynced customers: $e");
    }
  }

  //---------------------------------------------------------------------
  Future<bool> insertCustomerLocally(CustomerModel customer) async {
    try {
      final db = await DatabaseHelper().database;

      // Check if ID is null
      if (customer.iD == null) {
        // Insert new customer without ID
        await db.insert('Customer', customer.toJson());
        print("Customer saved locally without ID: ${customer.toJson()}");
        return true;
      } else {
        // Check if the ID exists in the database
        final existingCustomer = await db.query(
          'Customer',
          where: 'local_Id = ?',
          whereArgs: [customer.localId],
        );

        if (existingCustomer.isEmpty) {
          // ID does not exist, insert new customer
          await db.insert('Customer', customer.toJson());
          print("Customer saved locally with new ID: ${customer.toJson()}");
          return true;
        } else {
          // ID already exists, do not insert
          print("Customer with ID ${customer.iD} already exists.");
          return false;
        }
      }
    } catch (e) {
      print("Error saving customer locally: $e");
      return false;
    }
  }

//--------------------------------------------------------------------------------
  Future<void> syncCustomersWithServer() async {
    final db = await DatabaseHelper().database;

    var unsyncedCustomers = await db.query('Customer', where: 'ID = 0');

    for (var customerMap in unsyncedCustomers) {
      var customer = CustomerModel.fromJson(customerMap);

      await syncCustomerWithServer(customer);
    }
  }

//--------------------------------------------------------------------------------
  Future<bool> updateCustomer(
      int customerId, CustomerModel customerData) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        await updateCustomerOnline(customerId, customerData);
        await updateCustomerOffline(customerId, customerData);
        return true;
      } else {
        return await updateCustomerOffline(customerId, customerData);
      }
    } catch (e) {
      print("Error in updating customer: $e");
      return false;
    }
  }

//--------------------------------------------------------------------------------
  Future<bool> updateCustomerOnline(
      int customerId, CustomerModel customerData) async {
    try {
      //  final db = await DatabaseHelper().database;
      var url = 'Customer/manage?Id=$customerId';

      // Logging the data being sent to the API
      print("Sending data to API: ${customerData.toJson()}");

      var result = await Api.post(
        body: customerData.toJson(),
        action: url,
      );

      // Verifying API response
      if (result != null && result.isSuccess) {
        print("API update successful. Updating local database.");

        notifyListeners();
        return true;
      } else {
        print("API update failed. Response: $result");
        return false;
      }
    } catch (e) {
      // Log detailed error for debugging
      print("Error in updating customer (online): ${e.toString()}");
      return false;
    }
  }

//--------------------------------------------------------------------------------
  Future<bool> updateCustomerOffline(
      int customerId, CustomerModel customerData) async {
    try {
      final db = await DatabaseHelper().database;

      var customer =
          await db.query('Customer', where: 'ID = ?', whereArgs: [customerId]);

      if (customer.isEmpty) {
        errorSnackBar(
            message:
                "Customer is already synced and cannot be updated offline.");
        print(
            'Error: Customer is already synced and cannot be updated offline.');
        return false;
      } else {
        await db.update(
          'Customer',
          customerData.toJson(),
          where: 'ID = ?',
          whereArgs: [customerId],
        );
        notifyListeners();
        return true;
      }
    } catch (e) {
      print("Error in updating customer (offline): $e");
      return false;
    }
  }

//---------------------------------------------------------------------
  // Get all customers
  Future<List<Map<String, dynamic>>> getCustomers() async {
    final db = await DatabaseHelper().database;
    var customers = await db.query('Customer');
    return customers;
  }

  // Optional: Get a customer by ID
  Future<Map<String, dynamic>?> getCustomerById(int id) async {
    final db = await DatabaseHelper().database;
    final result = await db.query(
      'Customer',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

//--------------------------------------------------------------------------------

  Future<int> getCustomerCount() async {
    final db = await DatabaseHelper().database;

    var result = await db.rawQuery('SELECT COUNT(*) as count FROM Customer');
    int count = Sqflite.firstIntValue(result) ?? 0;
    return count;
  }

//--------------------------------------------------------------------------------
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await DatabaseHelper().database;

      await db.transaction((txn) async {
        await txn.delete('Customer');
      });

      await fetchCustomers();
      return true;
    } catch (e) {
      return false;
    }
  }

//--------------------------------------------------------------------------------
  Future<void> saveCustomerToEcommerce(CustomerModel model) async {
    try {
      var url = 'api/A_Account/AddCustomerFromERP';

      var result = await EcommerceApi.post(
        action: url,
        body: model.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          notifyListeners();
        }
      }
    } catch (e) {
      print(e);
    }
  }
}
