import 'package:animated_icon/animated_icon.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:provider/provider.dart';

class SyncizationStatusWidget extends StatelessWidget {
  const SyncizationStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    var product = Provider.of<ProductController>(context).runningSyncization;
    var customers = Provider.of<CustomerController>(context).runningSyncization;
    if (product == true || customers == true) {
      return Container(
        width: context.width,
        height: 30,
        decoration: BoxDecoration(
          color: context.colors.primary,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimateIcon(
              key: UniqueKey(),
              onTap: () {},
              iconType: IconType.continueAnimation,
              height: 70,
              width: 70,
              color: context.backgroundColor,
              animateIcon: AnimateIcons.refresh,
            ),
            Text(
              "جاري عملية المزامنة",
              style: TextStyle(color: context.backgroundColor),
            ),
          ],
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
