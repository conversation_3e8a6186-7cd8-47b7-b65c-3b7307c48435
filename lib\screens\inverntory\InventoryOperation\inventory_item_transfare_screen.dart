import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_items_transfar_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/inverntory/InventoryOperation/widgets/inventory_item_transare_base_info_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/inverntory/invoices/widgets/invoice_select_product_widget.dart';
import 'package:provider/provider.dart';

class InventoryItemTransfareScreen extends StatefulWidget {
  const InventoryItemTransfareScreen({Key? key}) : super(key: key);

  @override
  State<InventoryItemTransfareScreen> createState() =>
      _InventoryItemTransfareScreenState();
}

class _InventoryItemTransfareScreenState
    extends State<InventoryItemTransfareScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<InventoryItemsTransfarController>(context, listen: false)
          .getItemsTransferNumber();
    });
  }

  @override
  Widget build(BuildContext context) {
    var products = Provider.of<InventoryItemsTransfarController>(context)
        .selectedTransfarProduct;

    var provider =
        Provider.of<InventoryItemsTransfarController>(context, listen: false);
    return ApplicationLayout(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              CommonHeader(
                icon: Icons.move_down,
                title: T("Item transfare"),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  child: Column(
                    children: [
                      // const SizedBox(height: 50),
                      const InventoryItemTransareBaseInfoWidget(),
                      InvoiceSelectProductWidget(
                        selectedProducts: products,
                        onChange: () {
                          // Force UI refresh when products change
                          setState(() {});
                        },
                        onAddProduct: (ProductDTO product) {
                          provider.addProductToSelectedList(product);
                          // Force UI refresh
                          setState(() {});
                        },
                        onRemoveProduct: (int id) {
                          provider.deleteProductFromSelectedList(id);
                          // Force UI refresh
                          setState(() {});
                        },
                        onSearchByBarcode: (String barcode) async {
                          // Get the controller
                          final inventoryItemsTransController =
                              Provider.of<InventoryItemsTransfarController>(
                            context,
                            listen: false,
                          );

                          // Process the barcode
                          var result = await inventoryItemsTransController
                              .getItemByBarcode(
                            barcode: barcode,
                          );

                          // If result is a ProductDTO object rather than a boolean,
                          // it means we need to show the attribute selection dialog
                          if (result is ProductDTO) {
                            // Get reference to the product screen
                            Provider.of<ProductController>(
                                // ignore: use_build_context_synchronously
                                context,
                                listen: false);

                            // Show attribute dialog for the product
                            await showDialog(
                              // ignore: use_build_context_synchronously
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return _buildAttributeSelectionDialog(result);
                              },
                            );

                            // Return true to indicate success (dialog was shown)
                            return true;
                          }

                          // Force UI refresh immediately after barcode scan
                          setState(() {
                            // This will trigger a rebuild with the updated product list
                          });

                          // Add a small delay and refresh again to ensure UI updates
                          await Future.delayed(
                              const Duration(milliseconds: 100));
                          setState(() {});

                          // Show error if product not found
                          if (result == false) {
                            errorSnackBar(
                              message: T(
                                  "There is no product associated with the barcode"),
                              // ignore: use_build_context_synchronously
                              context: context,
                            );
                          }

                          return result;
                        },
                      ),

                      if (products.isNotEmpty) ...[
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.zero,
                          child: InvoiceProductListHeaderWidget(
                            backgroundColor: context.primaryColor,
                            textColor: Colors.white,
                          ),
                        ),
                        Consumer<InventoryItemsTransfarController>(
                          builder: (context, inventoryItemsTransfarController,
                              child) {
                            return ListView.separated(
                              itemCount: products.length,
                              shrinkWrap: true,
                              padding: EdgeInsets.zero,
                              separatorBuilder: (context, index) => Divider(
                                color: Colors.grey.withOpacity(0.2),
                                height: 1,
                                indent: 0,
                                endIndent: 0,
                              ),
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                return InvoiceListItemsWidget(
                                  id: products[index].id ?? 0,
                                  barcode: products[index].barcode,
                                  virtualProductId:
                                      products[index].virtualProductId,
                                  selectedInvoiceProduct:
                                      inventoryItemsTransfarController
                                          .selectedTransfarProduct,
                                  onChangeWarehouse: (int productId,
                                      int warehouseId, String warehouseName,
                                      [String? virtualProductId]) {
                                    errorSnackBar(
                                        message: "لا يمكن تغير المستودع هنا");
                                  },
                                  onDeleteProduct: (int productId,
                                      [String? virtualProductId]) {
                                    if (virtualProductId != null) {
                                      // If we have a virtual ID, use it when deleting
                                      provider.deleteProductFromSelectedList(
                                          productId,
                                          virtualProductId: virtualProductId);
                                    } else {
                                      // Otherwise use the regular product ID
                                      provider.deleteProductFromSelectedList(
                                          productId);
                                    }
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                  onUpdatePrice: (int productId, double price,
                                      [String? virtualProductId]) {
                                    if (virtualProductId != null) {
                                      provider.updateProductPrice(
                                          productId, price, virtualProductId);
                                    } else {
                                      provider.updateProductPrice(
                                          productId, price);
                                    }
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                  onUpdateQuantity:
                                      (int productId, double quantity,
                                          [String? virtualProductId]) {
                                    if (virtualProductId != null) {
                                      provider.updateProductQuantity(productId,
                                          quantity, virtualProductId);
                                    } else {
                                      provider.updateProductQuantity(
                                          productId, quantity);
                                    }
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                  onUpdateUnit:
                                      (int productId, ItemPriceDTO unit,
                                          [String? virtualProductId]) {
                                    provider.updateProductUnit(
                                        productId, unit, virtualProductId);
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                );
                              },
                            );
                          },
                        ),
                      ],

                      // Summary Section
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: SizedBox(
          height: 75,
          width: context.width,
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'اجمالي : ${(provider.inventoryItemTransfar.total ?? 0)}', // TODO: Add actual count
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Implement save functionality
                    },
                    child: const Text('حفظ العملية'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: Text(
            T('Select Product Options'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: context.primaryColor,
            ),
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.5,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.title ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Sort attributes by their order property
                  ...(() {
                    final sortedAttributes =
                        List<ItemAttribute>.from(product.itemAttributes ?? []);
                    sortedAttributes
                        .sort((a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
                    return sortedAttributes;
                  })()
                      .map((attribute) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          attribute.attributeName ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: context.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Wrap options in a grid
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: (attribute.itemsAttributeOptions ?? [])
                              .map((option) {
                            final isSelected =
                                selectedOptions[attribute.id] == option;

                            return InkWell(
                              onTap: () {
                                setState(() {
                                  if (attribute.id != null) {
                                    selectedOptions[attribute.id!] = option;
                                  }
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? context.primaryColor
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: isSelected
                                        ? context.primaryColor
                                        : Colors.grey.shade300,
                                  ),
                                ),
                                child: Text(
                                  option.optionName ?? '',
                                  style: TextStyle(
                                    color: isSelected
                                        ? Colors.white
                                        : context.onSurface,
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  }).toList(),

                  // Preview of the selected options
                  if (selectedOptions.isNotEmpty) ...[
                    const Divider(),
                    const SizedBox(height: 8),
                    Text(
                      T('Selected Options:'),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getFormattedSelectedOptions(selectedOptions),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: context.primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                T('Cancel'),
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: context.primaryColor,
                foregroundColor: Colors.white,
              ),
              onPressed: (product.itemAttributes != null &&
                      selectedOptions.length == product.itemAttributes!.length)
                  ? () {
                      // All attributes have been selected
                      Navigator.of(context).pop();
                      _addProductWithSelectedAttributes(
                          product, selectedOptions);
                    }
                  : null, // Disable button if not all attributes are selected
              child: Text(T('Add to Cart')),
            ),
          ],
        );
      },
    );
  }

  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    if (selectedOptions.isEmpty) return '';

    // Sort by attribute ID (which should correspond to the attribute order)
    final sortedKeys = selectedOptions.keys.toList()..sort();

    // Join option names with '/'
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }

//---------------------------------------------------------------------------
  void _addProductWithSelectedAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Create a unique key for this product + attributes combination
    // We'll use the selected option IDs to create a "virtual" product ID
    String optionsHash = selectedOptions.entries
        .map((e) => '${e.value.optionId}-${e.key}')
        .join('_');

    String virtualProductId = '${product.id}_$optionsHash';

    // Get the SaleInvoiceController
    final saleInvoiceController =
        Provider.of<InventoryItemsTransfarController>(context, listen: false);

    // Check if this product with the same attributes already exists in the cart
    int existingIndex = saleInvoiceController.selectedTransfarProduct
        .indexWhere((element) => element.virtualProductId == virtualProductId);

    // If the product already exists, just increase its quantity
    if (existingIndex >= 0) {
      var existingProduct =
          saleInvoiceController.selectedTransfarProduct[existingIndex];
      double newQuantity =
          (existingProduct.quantity ?? 1) + (product.quantity ?? 1);

      // Update the quantity
      saleInvoiceController.selectedTransfarProduct[existingIndex].quantity =
          newQuantity;

      // Update the total
      double price =
          saleInvoiceController.selectedTransfarProduct[existingIndex].price ??
              0;
      saleInvoiceController.selectedTransfarProduct[existingIndex].total =
          price * newQuantity;

      // Recalculate invoice total
      saleInvoiceController.calculateInvoiceTotal();

      // Notify listeners for UI update
      // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
      saleInvoiceController.notifyListeners();
      return;
    }

    // If the product doesn't exist, create a new one
    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: product.uniteId,
      uniteName: product.uniteName,
      category: product.category,
      quantity: product.quantity ?? 1.0, // Ensure a default quantity
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      itemAttributes: product.itemAttributes,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - ${optionsString}';
      productCopy.hasSelectedAttributes = true;

      // Store selected options in the description field for reference
      // This way we can retrieve the information later if needed
      String attributeDetails = selectedOptions.entries
          .map((entry) =>
              '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
          .join(', ');

      if (productCopy.description == null || productCopy.description!.isEmpty) {
        productCopy.description = attributeDetails;
      } else {
        productCopy.description =
            '${productCopy.description}\n[$attributeDetails]';
      }

      // Set the virtual product ID
      productCopy.virtualProductId = virtualProductId;
    }

    // Add to invoice
    saleInvoiceController.addProductToSelectedList(productCopy);
  }
}
