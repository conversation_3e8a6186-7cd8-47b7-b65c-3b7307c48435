import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/daily_sales_report_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class SalesTrendChartWidget extends StatefulWidget {
  final DailySalesReportDTO report;
  final bool showTitle;

  const SalesTrendChartWidget({
    Key? key,
    required this.report,
    this.showTitle = true,
  }) : super(key: key);

  @override
  State<SalesTrendChartWidget> createState() => _SalesTrendChartWidgetState();
}

class _SalesTrendChartWidgetState extends State<SalesTrendChartWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.showTitle)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              T('Sales Trend'),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        _buildTrendChart(),
      ],
    );
  }

  Widget _buildTrendChart() {
    // Group invoices by date
    final Map<DateTime, double> salesByDate = {};

    for (var invoice in widget.report.invoices) {
      if (invoice.entryDate != null) {
        final date = DateTime(
          invoice.entryDate!.year,
          invoice.entryDate!.month,
          invoice.entryDate!.day,
        );

        if (salesByDate.containsKey(date)) {
          salesByDate[date] = salesByDate[date]! + (invoice.total ?? 0);
        } else {
          salesByDate[date] = invoice.total ?? 0;
        }
      }
    }

    // If we have no data, show a message
    if (salesByDate.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No sales data available for the selected period'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // If we have only one day, create a special chart for single day
    if (salesByDate.length == 1) {
      return _buildSingleDayChart(salesByDate);
    }

    // Sort dates
    final sortedDates = salesByDate.keys.toList()..sort();

    return SizedBox(
      height: 300,
      child: Padding(
        padding: const EdgeInsets.only(right: 16, left: 8, top: 24, bottom: 12),
        child: BarChart(
          BarChartData(
            alignment: BarChartAlignment.spaceAround,
            maxY: salesByDate.values.reduce((a, b) => a > b ? a : b) * 1.2,
            barTouchData: BarTouchData(
              touchTooltipData: BarTouchTooltipData(
                tooltipBgColor: Colors.grey.shade800,
                getTooltipItem: (group, groupIndex, rod, rodIndex) {
                  final date = sortedDates[groupIndex];
                  final value = salesByDate[date]!;
                  return BarTooltipItem(
                    '${DateFormat('MMM d').format(date)}\n${NumberFormat('#,##0.00').format(value)}',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                },
              ),
            ),
            titlesData: FlTitlesData(
              show: true,
              rightTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              topTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  getTitlesWidget: (value, meta) {
                    if (value < 0 || value >= sortedDates.length) {
                      return const SizedBox.shrink();
                    }

                    // Format date as day or day/month depending on range
                    final date = sortedDates[value.toInt()];
                    String text;

                    if (sortedDates.length <= 7) {
                      // For a week or less, show day/month
                      text = DateFormat('d/M').format(date);
                    } else {
                      // For more than a week, just show day
                      text = DateFormat('d').format(date);
                    }

                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        text,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                  },
                  reservedSize: 30,
                ),
              ),
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  getTitlesWidget: (value, meta) {
                    if (value == 0) {
                      return const SizedBox.shrink();
                    }

                    // Format currency value
                    String text = NumberFormat.compact().format(value);

                    return Text(
                      text,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                  reservedSize: 40,
                ),
              ),
            ),
            borderData: FlBorderData(
              show: false,
            ),
            barGroups: List.generate(
              sortedDates.length,
              (index) {
                final date = sortedDates[index];
                final value = salesByDate[date]!;

                return BarChartGroupData(
                  x: index,
                  barRods: [
                    BarChartRodData(
                      toY: value,
                      color: context.newPrimaryColor,
                      width: 20,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                    ),
                  ],
                );
              },
            ),
            gridData: const FlGridData(
              show: true,
              drawVerticalLine: false,
            ),
          ),
        ),
      ),
    );
  }

  // Build a chart for a single day showing hourly breakdown
  Widget _buildSingleDayChart(Map<DateTime, double> salesByDate) {
    final date = salesByDate.keys.first;

    // Create a breakdown by invoice to show more detail
    final invoices = widget.report.invoices
        .where((inv) =>
            inv.entryDate != null &&
            DateTime(inv.entryDate!.year, inv.entryDate!.month,
                    inv.entryDate!.day)
                .isAtSameMomentAs(date))
        .toList();

    // Sort invoices by time
    invoices.sort((a, b) => (a.entryDate ?? DateTime.now())
        .compareTo(b.entryDate ?? DateTime.now()));

    // Group by hour if possible
    final Map<int, double> salesByHour = {};

    for (var invoice in invoices) {
      if (invoice.entryDate != null) {
        final hour = invoice.entryDate!.hour;

        if (salesByHour.containsKey(hour)) {
          salesByHour[hour] = salesByHour[hour]! + (invoice.total ?? 0);
        } else {
          salesByHour[hour] = invoice.total ?? 0;
        }
      }
    }

    // If we don't have hourly breakdown (all same hour), show by invoice
    if (salesByHour.length <= 1) {
      return _buildInvoiceBreakdownChart(invoices, date);
    }

    // Sort hours
    final sortedHours = salesByHour.keys.toList()..sort();

    return SizedBox(
      height: 300,
      child: Padding(
        padding: const EdgeInsets.only(right: 16, left: 8, top: 24, bottom: 12),
        child: Column(
          children: [
            Text(
              '${T('Sales for')} ${DateFormat('yyyy-MM-dd').format(date)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY:
                      salesByHour.values.reduce((a, b) => a > b ? a : b) * 1.2,
                  barTouchData: BarTouchData(
                    touchTooltipData: BarTouchTooltipData(
                      tooltipBgColor: Colors.grey.shade800,
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        final hour = sortedHours[groupIndex];
                        final value = salesByHour[hour]!;
                        return BarTooltipItem(
                          '${hour.toString().padLeft(2, '0')}:00\n${NumberFormat('#,##0.00').format(value)}',
                          const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                    ),
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value < 0 || value >= sortedHours.length) {
                            return const SizedBox.shrink();
                          }

                          final hour = sortedHours[value.toInt()];
                          return Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              '${hour.toString().padLeft(2, '0')}:00',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value == 0) {
                            return const SizedBox.shrink();
                          }

                          String text = NumberFormat.compact().format(value);

                          return Text(
                            text,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                        reservedSize: 40,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: false,
                  ),
                  barGroups: List.generate(
                    sortedHours.length,
                    (index) {
                      final hour = sortedHours[index];
                      final value = salesByHour[hour]!;

                      return BarChartGroupData(
                        x: index,
                        barRods: [
                          BarChartRodData(
                            toY: value,
                            color: context.newPrimaryColor,
                            width: 20,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  gridData: const FlGridData(
                    show: true,
                    drawVerticalLine: false,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build a chart showing individual invoices for a single day
  Widget _buildInvoiceBreakdownChart(
      List<InvoiceModel> invoices, DateTime date) {
    return SizedBox(
      height: 300,
      child: Padding(
        padding: const EdgeInsets.only(right: 16, left: 8, top: 24, bottom: 12),
        child: Column(
          children: [
            Text(
              '${T('Sales for')} ${DateFormat('yyyy-MM-dd').format(date)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: invoices
                          .map((inv) => inv.total ?? 0)
                          .reduce((a, b) => a > b ? a : b) *
                      1.2,
                  barTouchData: BarTouchData(
                    touchTooltipData: BarTouchTooltipData(
                      tooltipBgColor: Colors.grey.shade800,
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        final invoice = invoices[groupIndex];
                        final value = invoice.total ?? 0;
                        final time = invoice.entryDate != null
                            ? DateFormat('HH:mm').format(invoice.entryDate!)
                            : '';
                        return BarTooltipItem(
                          '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? ''}\n$time\n${NumberFormat('#,##0.00').format(value)}',
                          const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                    ),
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value < 0 || value >= invoices.length) {
                            return const SizedBox.shrink();
                          }

                          final invoiceNumber = (value.toInt() + 1).toString();

                          return Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              invoiceNumber,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value == 0) {
                            return const SizedBox.shrink();
                          }

                          String text = NumberFormat.compact().format(value);

                          return Text(
                            text,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                        reservedSize: 40,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: false,
                  ),
                  barGroups: List.generate(
                    invoices.length,
                    (index) {
                      final invoice = invoices[index];
                      final value = invoice.total ?? 0;

                      return BarChartGroupData(
                        x: index,
                        barRods: [
                          BarChartRodData(
                            toY: value,
                            color: context.newPrimaryColor,
                            width: 20,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  gridData: const FlGridData(
                    show: true,
                    drawVerticalLine: false,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
