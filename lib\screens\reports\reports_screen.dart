import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/widgets/customer_sales_report_widget.dart';
import 'package:inventory_application/screens/reports/widgets/daily_sales_report_widget.dart';
import 'package:inventory_application/screens/reports/widgets/monthly_sales_report_widget.dart';
import 'package:inventory_application/screens/reports/widgets/product_sales_report_widget.dart';
import 'package:inventory_application/screens/reports/widgets/report_filter_dialog.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  ReportType _selectedReportType = ReportType.dailySales;
  bool _isLoading = false;
  bool _showReportContent =
      false; // Flag to control whether to show report content or main menu

  @override
  void initState() {
    super.initState();
    // Generate report on screen load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateReport();
    });
  }

  // Generate the selected report
  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportController =
          Provider.of<ReportController>(context, listen: false);

      switch (_selectedReportType) {
        case ReportType.dailySales:
          await reportController.generateDailySalesReport();
          break;
        case ReportType.monthlySales:
          await reportController.generateMonthlySalesReport();
          break;
        case ReportType.productSales:
          await reportController.generateProductSalesReport();
          break;
        case ReportType.customerSales:
          await reportController.generateCustomerSalesReport();
          break;
      }
    } catch (e) {
      errorSnackBar(message: 'Error generating report: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Show filter dialog
  void _showFilterDialog() {
    final reportController =
        Provider.of<ReportController>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => ReportFilterDialog(
        initialFilter: reportController.filter,
        onApply: (filter) {
          setState(() {
            _selectedReportType = filter.reportType;
            reportController.filter = filter;
          });
          _generateReport();
        },
      ),
    );
  }

  // Get formatted date range text
  String _getDateRangeText(ReportFilterDTO filter) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final fromDate = dateFormat.format(filter.fromDate ?? DateTime.now());
    final toDate = dateFormat.format(filter.toDate ?? DateTime.now());

    if (fromDate == toDate) {
      return fromDate;
    } else {
      return '$fromDate ${T('to')} $toDate';
    }
  }

  @override
  Widget build(BuildContext context) {
    final reportController = Provider.of<ReportController>(context);

    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _showReportContent
            ? _buildReportView(reportController)
            : _buildReportsMenu();
  }

  // Build the report view when a report type is selected
  Widget _buildReportView(ReportController reportController) {
    // Get colors based on report type
    Color headerColor;
    Color accentColor;
    IconData reportIcon;

    switch (_selectedReportType) {
      case ReportType.dailySales:
        headerColor = const Color(0xFF4361EE);
        accentColor = const Color(0xFF3A0CA3);
        reportIcon = Icons.calendar_today;
        break;
      case ReportType.monthlySales:
        headerColor = const Color(0xFF4CC9F0);
        accentColor = const Color(0xFF4361EE);
        reportIcon = Icons.date_range;
        break;
      case ReportType.productSales:
        headerColor = const Color(0xFFF72585);
        accentColor = const Color(0xFF7209B7);
        reportIcon = Icons.inventory_2;
        break;
      case ReportType.customerSales:
        headerColor = const Color(0xFF06D6A0);
        accentColor = const Color(0xFF1B9AAA);
        reportIcon = Icons.people;
        break;
    }

    return Column(
      children: [
        // Enhanced report header with date range info
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [headerColor, accentColor],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: headerColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      reportIcon,
                      size: 24,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getReportTypeName(_selectedReportType),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _getDateRangeText(reportController.filter),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: _showFilterDialog,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.date_range,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      T('Filter'),
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Report content - takes remaining space
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade300,
                  blurRadius: 5,
                  // offset: const Offset(0, -3),
                  spreadRadius: 1,
                ),
              ],
            ),
            // margin: const EdgeInsets.only(top: 20),
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              child: _buildReportContent(reportController),
            ),
          ),
        ),
      ],
    );
  }

  // Build the main reports menu with cards
  Widget _buildReportsMenu() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white,
            Colors.grey.shade100,
          ],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Internal Reports Section
            Row(
              children: [
                Container(
                  width: 6,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  T('التقارير الداخلية'),
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            Column(children: [
              Row(
                children: [
                  _buildEnhancedReportCard(
                    title: T('Daily Sales'),
                    subtitle: T('View daily revenue and transactions'),
                    icon: Icons.calendar_today,
                    color1: const Color(0xFF4361EE),
                    color2: const Color(0xFF3A0CA3),
                    iconBgColor: const Color(0xFF7209B7).withOpacity(0.2),
                    onTap: () => _selectReportType(ReportType.dailySales),
                  ),
                  _buildEnhancedReportCard(
                    title: T('Monthly Sales'),
                    subtitle: T('Analyze monthly performance'),
                    icon: Icons.date_range,
                    color1: const Color(0xFF4CC9F0),
                    color2: const Color(0xFF4361EE),
                    iconBgColor: const Color(0xFF4CC9F0).withOpacity(0.2),
                    onTap: () => _selectReportType(ReportType.monthlySales),
                  ),
                ],
              ),
              Row(
                children: [
                  _buildEnhancedReportCard(
                    title: T('Product Sales'),
                    subtitle: T('Track product performance'),
                    icon: Icons.inventory_2,
                    color1: const Color(0xFFF72585),
                    color2: const Color(0xFF7209B7),
                    iconBgColor: const Color(0xFFF72585).withOpacity(0.2),
                    onTap: () => _selectReportType(ReportType.productSales),
                  ),
                  _buildEnhancedReportCard(
                    title: T('Customer Sales'),
                    subtitle: T('Analyze customer behavior'),
                    icon: Icons.people,
                    color1: const Color(0xFF06D6A0),
                    color2: const Color(0xFF1B9AAA),
                    iconBgColor: const Color(0xFF06D6A0).withOpacity(0.2),
                    onTap: () => _selectReportType(ReportType.customerSales),
                  ),
                ],
              )
            ]),
            // Internal Reports Grid with enhanced design

            const SizedBox(height: 40),

            // Server Reports Section
            Padding(
              padding: const EdgeInsets.only(bottom: 20, left: 8, right: 8),
              child: Row(
                children: [
                  Container(
                    width: 6,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade600,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    T('Server Reports'),
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),

            // Server Reports Coming Soon Card with enhanced design
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.shade300,
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                    spreadRadius: 2,
                  ),
                ],
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.cloud_sync,
                      size: 60,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    T('Coming Soon'),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                      letterSpacing: 1.2,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: 80,
                    height: 3,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    T('Server reports will be available in a future update.'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            // Bottom padding
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  // Build an enhanced report card for the main menu
  Widget _buildEnhancedReportCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color1,
    required Color color2,
    required Color iconBgColor,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        height: 220,
        width: context.width / 2 - 15,
        margin: const EdgeInsets.all(5),
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color1, color2],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: color1.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon with background
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: iconBgColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                icon,
                size: 28,
                color: Colors.white,
              ),
            ),

            const Spacer(),

            // Title
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
              ),
            ),

            const SizedBox(height: 6),

            // Subtitle
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),

            const SizedBox(height: 12),

            // Arrow indicator
            Align(
              alignment: Alignment.bottomRight,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.arrow_forward,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Handle report type selection
  void _selectReportType(ReportType type) {
    setState(() {
      _selectedReportType = type;
      _showReportContent = true;
    });
    _generateReport();
  }

  // Build the appropriate report content based on selected report type
  Widget _buildReportContent(ReportController reportController) {
    switch (_selectedReportType) {
      case ReportType.dailySales:
        final report = reportController.dailySalesReport;
        if (report == null) {
          return Center(
            child: Text(T('No report data available')),
          );
        }
        return DailySalesReportWidget(report: report);
      case ReportType.monthlySales:
        final report = reportController.monthlySalesReport;
        if (report == null) {
          return Center(
            child: Text(T('No monthly report data available')),
          );
        }
        return MonthlySalesReportWidget(report: report);
      case ReportType.productSales:
        final report = reportController.productSalesReport;
        if (report == null) {
          return Center(
            child: Text(T('No product report data available')),
          );
        }
        return ProductSalesReportWidget(report: report);
      case ReportType.customerSales:
        final report = reportController.customerSalesReport;
        if (report == null) {
          return Center(
            child: Text(T('No customer report data available')),
          );
        }
        return CustomerSalesReportWidget(report: report);
    }
  }

  // Helper method to get report type display name
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return T('Daily Sales');
      case ReportType.monthlySales:
        return T('Monthly Sales');
      case ReportType.productSales:
        return T('Product Sales');
      case ReportType.customerSales:
        return T('Customer Sales');
    }
  }
}
