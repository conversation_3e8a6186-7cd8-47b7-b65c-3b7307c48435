import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class DoubleEntryTransferScreen extends StatefulWidget {
  const DoubleEntryTransferScreen({super.key});

  @override
  State<DoubleEntryTransferScreen> createState() =>
      _DoubleEntryTransferScreenState();
}

class _DoubleEntryTransferScreenState extends State<DoubleEntryTransferScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
