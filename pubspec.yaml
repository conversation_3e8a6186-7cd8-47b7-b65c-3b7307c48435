name: inventory_application
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  provider: ^6.1.2
  easy_localization: ^3.0.7
  shared_preferences: ^2.3.2
  dio: ^5.7.0
  dotted_decoration: ^2.0.0
  photo_view: ^0.14.0
  cached_network_image: ^3.2.2
  flutter_pdfview: ^1.3.2
  another_flushbar: ^1.12.30
  webview_flutter: ^4.4.2
  flutter_image_slideshow: ^0.1.4
  bottom_bar_matu: ^1.5.0
  flutter_staggered_grid_view: ^0.6.2
  font_awesome_flutter: ^10.7.0
  flutter_barcode_scanner: ^2.0.0
  mobile_scanner: ^5.2.3
  screenshot: ^3.0.0
  share_plus: ^10.0.2
  pull_to_refresh: ^2.0.0
  flutter_simple_calculator: ^2.3.0
  sqflite: ^2.3.3+1
  sqflite_common_ffi: ^2.3.0
  connectivity_plus: ^6.1.0
  flutter_localization: ^0.2.2
  animated_icon: ^0.0.9
  audioplayers: ^5.2.0
  percent_indicator: ^4.2.3
  pdf: ^3.11.1
  device_info_plus: ^11.1.1
  jwt_decode: ^0.3.1
  jwt_decoder: ^2.0.1
  flutter_custom_clippers: ^2.1.0
  flutter_pw_validator: ^1.6.0
  lottie: ^3.1.3
  path_provider: ^2.1.5
  permission_handler: ^11.3.1
  file_selector: ^1.0.3
  crypto: ^3.0.6
  intl_phone_field: ^3.1.0
  virtual_keyboard_multi_language: ^1.1.2
  printing: ^5.11.1
  file_picker: ^8.3.2
  flutter_blue_plus: ^1.31.15
  fl_chart: ^0.66.2
  intl: ^0.19.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  sqflite_common_ffi_web: ^0.4.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

   # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true


  # To add assets to your application, add an assets section, like this:
  assets:

    - assets/images/
    - assets/images/base_images/
    - assets/images/home/
    - assets/langs/
    - assets/sounds/
    - assets/fonts/Montserrat-Arabic-Regular.ttf
    - assets/fonts/NeoSans-Regular.ttf
    - assets/fonts/NeoSans-Bold.ttf
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Medium.ttf
        - asset: assets/fonts/Montserrat-SemiBold.ttf
          weight: 600

    - family: MontserratAR
      fonts:
        - asset: assets/fonts/Montserrat-Arabic-Regular.ttf
        - asset: assets/fonts/Montserrat-Arabic-SemiBold.ttf
          weight: 600

    - family: NeoSans
      fonts:
        - asset: assets/fonts/NeoSans-Regular.ttf
        - asset: assets/fonts/NeoSans-Bold.ttf
          weight: 600

    - family: DroidKufi
      fonts:
        - asset: assets/fonts/DroidKufi-Regular.ttf

    - family: Inter
      fonts:
        - asset: assets/fonts/Inter.ttf

    - family: STV
      fonts:
        - asset: assets/fonts/STV.ttf
        - asset: assets/fonts/stv-bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
