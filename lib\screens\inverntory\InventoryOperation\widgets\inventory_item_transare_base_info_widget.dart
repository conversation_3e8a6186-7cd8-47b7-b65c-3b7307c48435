import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_items_transfar_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:provider/provider.dart';

class InventoryItemTransareBaseInfoWidget extends StatefulWidget {
  const InventoryItemTransareBaseInfoWidget({super.key});

  @override
  State<InventoryItemTransareBaseInfoWidget> createState() =>
      _InventoryItemTransareBaseInfoWidgetState();
}

class _InventoryItemTransareBaseInfoWidgetState
    extends State<InventoryItemTransareBaseInfoWidget> {
  @override
  Widget build(BuildContext context) {
    var provider =
        Provider.of<InventoryItemsTransfarController>(context, listen: false);
    return Column(
      children: [
        Card(
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'رقم العملية: ${provider.inventoryItemTransfar.aPPReferanceCode ?? ""}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'التاريخ: ${DateTime.now().toString().split(' ')[0]}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 5),

        // Warehouse Selection Section
        Card(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 10,
            ),
            child: Row(
              children: [
                MyComboBox(
                  width: context.width / 2 - 25,
                  caption:
                      provider.inventoryItemTransfar.storeName ?? T("From"),
                  height: 40,
                  onSelect: (int id, String name) {
                    provider.inventoryItemTransfar.fromStoreID = id;
                    provider.inventoryItemTransfar.storeName = name;
                    setState(() {});
                  },
                  fontSize: 12,
                  modalTitle: T("From"),
                  data: Provider.of<WarehouseController>(context, listen: false)
                      .warehouses,
                  isShowLabel: false,
                  labelText: "",
                ),
                const SizedBox(width: 16),
                MyComboBox(
                  width: context.width / 2 - 25,
                  caption:
                      provider.inventoryItemTransfar.toStoreName ?? T("To"),
                  height: 40,
                  fontSize: 12,
                  onSelect: (int id, String name) {
                    provider.inventoryItemTransfar.toStoreID = id;
                    provider.inventoryItemTransfar.toStoreName = name;
                    setState(() {});
                  },
                  modalTitle: T("To"),
                  data: Provider.of<WarehouseController>(context, listen: false)
                      .warehouses,
                  isShowLabel: false,
                  labelText: "",
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
