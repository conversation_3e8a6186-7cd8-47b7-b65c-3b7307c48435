import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_datetime_picker.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:provider/provider.dart';

class CreateReturnInvoiceDialogWidget extends StatefulWidget {
  const CreateReturnInvoiceDialogWidget({super.key});

  @override
  State<CreateReturnInvoiceDialogWidget> createState() =>
      _CreateReturnInvoiceDialogWidgetState();
}
//important important important important
//this screen open by using this statment
//  Navigator.of(context).push(DialogRoute(
//         context: context,
//         builder: (context) => const CreateSaleInvoiceDialogWidget(),
//       ));

class _CreateReturnInvoiceDialogWidgetState
    extends State<CreateReturnInvoiceDialogWidget> {
  var invoiceCodeController = TextEditingController(text: "");

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Get invoice number
      await Provider.of<ReturnInvoiceController>(context, listen: false)
          .getRetrunInvoiceNumber();

      // Set default values
      await _setDefaultValues();
    });
  }

  Future<void> _setDefaultValues() async {
    final returnInvoiceController =
        Provider.of<ReturnInvoiceController>(context, listen: false);
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);
    final customerController =
        Provider.of<CustomerController>(context, listen: false);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    // Try to load warehouses with retry logic
    int retryCount = 0;
    while (warehouseController.warehouses.isEmpty && retryCount < 3) {
      try {
        await warehouseController.fetchWarehouses();
        retryCount++;
        if (warehouseController.warehouses.isEmpty && retryCount < 3) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        print("Error loading warehouses: $e");
        retryCount++;
      }
    }

    // Try to load customers with retry logic
    retryCount = 0;
    while (customerController.customers.isEmpty && retryCount < 3) {
      try {
        await customerController.fetchCustomers();
        retryCount++;
        if (customerController.customers.isEmpty && retryCount < 3) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        print("Error loading customers: $e");
        retryCount++;
      }
    }

    // First check if we have a default warehouse in settings
    if (invoiceSettingsController.defaultWarehouseId != null) {
      returnInvoiceController.invoice.warehouseId =
          invoiceSettingsController.defaultWarehouseId;
      returnInvoiceController.invoice.warehouseName =
          invoiceSettingsController.defaultWarehouseName;
    }
    // If not, set default warehouse from the first available warehouse
    else if (warehouseController.warehouses.isNotEmpty) {
      try {
        final firstWarehouse = warehouseController.warehouses.first;
        if (firstWarehouse != null && firstWarehouse.id != null) {
          returnInvoiceController.invoice.warehouseId = firstWarehouse.id;
          returnInvoiceController.invoice.warehouseName = firstWarehouse.name;
        } else {
          print("Warning: First warehouse has null id or name");
          errorSnackBar(
            message:
                T("Could not set default warehouse. Please select manually."),
            context: context,
          );
        }
      } catch (e) {
        print("Error setting default warehouse: $e");
      }
    } else {
      print("Warning: No warehouses available");
      errorSnackBar(
        message:
            T("No warehouses available. Please add warehouses to the system."),
        context: context,
      );
    }

    // First check if we have a default customer in settings
    if (invoiceSettingsController.defaultCustomerId != null) {
      returnInvoiceController.invoice.customerId =
          invoiceSettingsController.defaultCustomerId;
      returnInvoiceController.invoice.custoemrName =
          invoiceSettingsController.defaultCustomerName;
    }
    // If not, set default customer from the first available customer
    else if (customerController.customers.isNotEmpty) {
      try {
        final firstCustomer = customerController.customers.first;
        if (firstCustomer != null && firstCustomer.iD != null) {
          returnInvoiceController.invoice.customerId = firstCustomer.iD;
          returnInvoiceController.invoice.custoemrName = firstCustomer.name;
        } else {
          print("Warning: First customer has null id or name");
          errorSnackBar(
            message:
                T("Could not set default customer. Please select manually."),
            context: context,
          );
        }
      } catch (e) {
        print("Error setting default customer: $e");
      }
    } else {
      print("Warning: No customers available");
      errorSnackBar(
        message:
            T("No customers available. Please add customers to the system."),
        context: context,
      );
    }

    // Set today's date
    returnInvoiceController.invoice.invoiceDate = DateTime.now();

    // Notify listeners to update UI
    returnInvoiceController.notifyListeners();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final textProvider = Provider.of<ReturnInvoiceController>(context).invoice;

    invoiceCodeController.text =
        (textProvider.appReferanceCode ?? "").toString();
  }

  String? saleInvoiceCode;

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<ReturnInvoiceController>(context).invoice;
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: context.width > 800 ? 800 : context.width - 32,
          maxHeight: context.height * 0.85,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.newSecondaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.assignment_return,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      T("Create Return Invoice"),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content area
            Flexible(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Sales invoice reference section
                      _buildSectionContainer(
                        context,
                        title: T("Sales Invoice Reference"),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: CommonTextField(
                                    initialValue:
                                        data.connectedToInvoiceLocalCode,
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.never,
                                    label: T("Sales Invoice Number"),
                                    onChanged: (value) {
                                      saleInvoiceCode = value;
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                ElevatedButton.icon(
                                  onPressed: () async {
                                    if (saleInvoiceCode != null) {
                                      pleaseWaitDialog(
                                          context: context, isShown: true);

                                      var result =
                                          await Provider.of<InvoiceController>(
                                                  context,
                                                  listen: false)
                                              .getInoviceByCode(
                                                  invoiceLocalCode:
                                                      saleInvoiceCode ?? "");
                                      if (result != null) {
                                        // ignore: use_build_context_synchronously
                                        Provider.of<ReturnInvoiceController>(
                                                context,
                                                listen: false)
                                            .setSaleInvoiceToReturnModel(
                                                result);
                                        // ignore: use_build_context_synchronously
                                        pleaseWaitDialog(
                                            context: context, isShown: false);
                                        // ignore: use_build_context_synchronously
                                        Navigator.of(context).pop();
                                      } else {
                                        // ignore: use_build_context_synchronously
                                        pleaseWaitDialog(
                                            context: context, isShown: false);
                                        errorSnackBar(
                                            message: T("Invoice not found"));
                                      }
                                    }
                                  },
                                  icon: const Icon(Icons.search, size: 18),
                                  label: Text(T("Find")),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: context.newPrimaryColor,
                                    foregroundColor: Colors.white,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Date and invoice number section
                      _buildSectionContainer(
                        context,
                        title: T("Invoice Information"),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                // Date picker
                                Expanded(
                                  child: MyDatePicker(
                                    width: double.infinity,
                                    onSave: (date) {
                                      data.invoiceDate = date;
                                    },
                                    initialVal:
                                        data.invoiceDate ?? DateTime.now(),
                                    caption: ''.myDateFormatter(
                                        data.invoiceDate ?? DateTime.now(),
                                        isShowTime: false),
                                    backColor: Colors.white,
                                  ),
                                ),
                                const SizedBox(width: 12),

                                // Invoice number
                                Expanded(
                                  child: CommonTextField(
                                    controller: invoiceCodeController,
                                    enabled: false,
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.never,
                                    label: T("Invoice Number"),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Customer section
                      _buildSectionContainer(
                        context,
                        title: T("Customer Information"),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MyComboBox(
                              selectedValue: data.customerId,
                              caption: data.custoemrName ?? T("Customer Name"),
                              height: 50,
                              onSelect: (int id, String name) {
                                data.customerId = id;
                                data.custoemrName = name;
                                setState(() {});
                              },
                              modalTitle: T("Customer Name"),
                              data: Provider.of<CustomerController>(context,
                                      listen: false)
                                  .customers
                                  .map(
                                    (e) => ComboBoxDataModel(
                                        e.iD ?? 0, e.name ?? ""),
                                  )
                                  .toList(),
                              isShowLabel: false,
                              labelText: "",
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Warehouse section
                      _buildSectionContainer(
                        context,
                        title: T("Warehouse Information"),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MyComboBox(
                              caption: data.warehouseName ?? T("Warehouse"),
                              height: 50,
                              onSelect: (int id, String name) {
                                data.warehouseId = id;
                                data.warehouseName = name;
                                setState(() {});
                              },
                              modalTitle: T("Warehouse"),
                              data: Provider.of<WarehouseController>(context,
                                      listen: false)
                                  .warehouses,
                              isShowLabel: false,
                              labelText: "",
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Notes section
                      _buildSectionContainer(
                        context,
                        title: T("Additional Information"),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextField(
                              initialValue: data.note ?? "",
                              label: T("Notes"),
                              onChanged: (value) {
                                data.note = value;
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.newBackgroundColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                border: Border(
                  top: BorderSide(
                    color: context.newPrimaryColor.withOpacity(0.1),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.check_circle_outline, size: 18),
                      label: Text(T("Create Return Invoice")),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.newSecondaryColor,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionContainer(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.newBackgroundColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: context.newPrimaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: context.newPrimaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(9),
                topRight: Radius.circular(9),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.arrow_right,
                  color: context.newSecondaryColor,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: TextStyle(
                    color: context.newSecondaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(12),
            child: child,
          ),
        ],
      ),
    );
  }
}
