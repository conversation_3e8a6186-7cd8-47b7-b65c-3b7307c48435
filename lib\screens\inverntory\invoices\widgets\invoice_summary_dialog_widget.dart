import 'package:flutter/material.dart';

import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';

import 'package:share_plus/share_plus.dart';
import 'dart:ui' as ui;

// Define a new GlobalKey for the RepaintBoundary
final GlobalKey _repaintBoundaryKey = GlobalKey();

void invoiceSummaryDialog(BuildContext context, InvoiceDto model,
    List<ProductDTO> products, SalesType invoiceType) {
  Future<void> captureScreenShot() async {
    RenderRepaintBoundary boundary = _repaintBoundaryKey.currentContext
        ?.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 2);
    ByteData byteData =
        await image.toByteData(format: ui.ImageByteFormat.png) as ByteData;
    Uint8List pngBytes = byteData.buffer.asUint8List();
    await Share.shareXFiles([XFile.fromData(pngBytes, mimeType: 'image/png')]);
  }

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return RepaintBoundary(
        key: _repaintBoundaryKey, // Use the new key here
        child: Dialog(
          backgroundColor: Colors.white,
          insetPadding: EdgeInsets.zero, // Removes default padding.
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          elevation: 5,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: context.width > 800 ? 800 : context.width - 10,
              maxHeight: context.height * 0.85,
            ),
            padding: const EdgeInsets.all(15), // Padding inside the dialog
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize:
                  MainAxisSize.min, // Makes the dialog wrap its content
              children: [
                Container(
                  width: context.width,
                  padding:
                      const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                  decoration: BoxDecoration(
                    color: context.newBackgroundColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "معلومات الاساسية",
                            style: TextStyle(
                                color: context.newSecondaryColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          RichText(
                            text: TextSpan(
                              text: '',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                  text: invoiceType == SalesType.Invoice
                                      ? "فاتورة مبيع "
                                      : invoiceType == SalesType.RetrunInvoice
                                          ? "فاتورة مرتجعات"
                                          : "فاتورة شراء",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: context.newPrimaryColor,
                                    fontSize: 16,
                                  ),
                                ),
                                TextSpan(
                                  text: "- ${model.custoemrName ?? ""}",
                                  style: TextStyle(
                                    color: context.newTextColor,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                "رقم الفاتورة - ${model.invoiceCode}",
                                style: TextStyle(
                                  color: context.newTextColor,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(width: 8),
                              InkWell(
                                onTap: () async {
                                  await Clipboard.setData(ClipboardData(
                                      text: model.invoiceCode.toString()));

                                  successSnackBar(
                                      message: "تم نسخ: ${model.invoiceCode}");
                                },
                                child: Icon(
                                  Icons.copy,
                                  color: context.newSecondaryColor,
                                  size: 22,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      Material(
                        elevation: 3,
                        color: context.newPrimaryColor,
                        borderRadius: BorderRadius.circular(10),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(10),
                          onTap: () async {
                            await captureScreenShot();
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.share,
                              size: 24,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 15),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    color: context.newSecondaryColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  width: double.infinity,
                  child: Text(
                    "تـــفاصيل الفاتورة",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 15),
                Container(
                  width: context.width,
                  padding:
                      const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                  decoration: BoxDecoration(
                    color: context.newBackgroundColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Product details
                      _buildInvoiceRow(context, "مجموع كميات المنتجات",
                          (model.productsTotalCount ?? 0).toString()),
                      _buildInvoiceRow(
                          context, "عدد المنتجات", products.length.toString()),
                      _buildInvoiceRow(
                          context,
                          "اجمالي الفاتورة",
                          0.0.covertDoubleToMoneyReturnString(
                              model.total ?? 0)),
                      _buildInvoiceRow(
                          context,
                          "مجموع الخصم",
                          0.0.covertDoubleToMoneyReturnString(
                              model.totalDiscount ?? 0)),
                      _buildInvoiceRow(
                          context,
                          "الدفعة",
                          0.0.covertDoubleToMoneyReturnString(
                              model.paymentValue ?? 0)),
                      _buildInvoiceRow(
                          context,
                          "صافي الفاتورة",
                          0.0.covertDoubleToMoneyReturnString(
                              model.totalAfterDiscount ?? 0)),
                    ],
                  ),
                ),
                // Add buttons at the bottom
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.newPrimaryColor,
                        foregroundColor: Colors.white,
                        elevation: 3,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 25, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: Text(
                        T("Close"),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

Widget _buildInvoiceRow(BuildContext context, String label, String value) {
  return Column(
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                  color: context.newSecondaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            Text(
              value,
              style: TextStyle(
                color: context.newTextColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
      Divider(
        color: Colors.grey.withOpacity(0.3),
        thickness: 1,
      ),
    ],
  );
}
