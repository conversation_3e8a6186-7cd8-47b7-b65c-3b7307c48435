import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/helpers/function.dart';

class ProductTransactionHeaderWidget extends StatelessWidget {
  const ProductTransactionHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
      color: context.onBackground,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          RichText(
            text: TextSpan(
              text: '',
              style: DefaultTextStyle.of(context).style,
              children: <TextSpan>[
                TextSpan(text: '${T("Product Name")} : '),
                TextSpan(
                    text: 'مهلبية',
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: context.colors.secondary)),
              ],
            ),
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RichText(
                text: TextSpan(
                  text: '',
                  style: DefaultTextStyle.of(context).style,
                  children: <TextSpan>[
                    TextSpan(text: '${T("from Date")} : '),
                    TextSpan(
                        text: ''
                            .myDateFormatter(DateTime.now(), isShowTime: false),
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.colors.secondary)),
                  ],
                ),
              ),
              RichText(
                text: TextSpan(
                  text: '',
                  style: DefaultTextStyle.of(context).style,
                  children: <TextSpan>[
                    TextSpan(text: '${T("to Date")} :  '),
                    TextSpan(
                        text: ''
                            .myDateFormatter(DateTime.now(), isShowTime: false),
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.colors.secondary)),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
