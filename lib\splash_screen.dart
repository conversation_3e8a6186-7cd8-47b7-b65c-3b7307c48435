import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/device_setup_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/controllers/barcode_controller.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/auth/SignIn_screen.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/setting/server_settings_screen.dart';
import 'package:provider/provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    // add post frame callback to wait for the context to be initialized
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      init();
      await Future.delayed(
        const Duration(seconds: 8),
        () async {
          if (await AppController.checkFirstTime()) {
            // ignore: use_build_context_synchronously
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const ServerSettingsScreen(
                  isFirstTime: true,
                ),
              ),
            );
            return;
          } else {
            if (AppController.isAuth) {
              // ignore: use_build_context_synchronously
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const HomeScreen(),
                ),
              );

              return;
            } else {
              // ignore: use_build_context_synchronously
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const SignInScreen(),
                ),
              );
              return;
            }
          }
        },
      );
    });
    super.initState();
  }

  void init() async {
    // await AuthController.tryAutoLogin();
    AppController.getBaseUrlFromShared();
    AppController.getEcommerceBaseUrlFromShared();
    AppController.getIsUsingEcommerceFromShared();
    await context.read<AppController>().getDeviceDetails();
    await context.read<DeviceSetupController>().deviceIdSetup();
    await CounterGenerator.getInvoicesCounterFromServer();
    await InventoryOperationCounterGenerator
        .getInventoryOperationCounterFromServer();

    // Initialize barcode settings
    await context.read<BarcodeController>().initialize();

    // ignore: use_build_context_synchronously
    await context.read<CategoryController>().fetchCategories();
    // ignore: use_build_context_synchronously
    await context.read<WarehouseController>().fetchWarehouses();

    // ignore: use_build_context_synchronously
    await context.read<SalesmenController>().fetchSalesmen();
    // ignore: use_build_context_synchronously
    await context.read<CustomerController>().fetchCustomers();
    // await context.read<ProductController>().fetchProduct();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().getProductCount();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().fetchProductAfterCertineId();
    await CounterGenerator.initializeSaleInvoiceCounter();
    await CounterGenerator.initializeReturnInvoiceCounter();
    await CounterGenerator.initializeOrderInvoiceCounter();
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.ItemsTransfer);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.Outgoing);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.Incoming);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.DamagedExpired);
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  // Future<bool> checkFirstTime() async {
  //   var isFirstTime = await AppController.checkFirstTime();
  //   return isFirstTime;
  //   if (isFirstTime) {
  //     Navigator.pushReplacement(
  //       navigatorKey.currentContext!,
  //       MaterialPageRoute(
  //         builder: (context) => const EnterAddressScreen(),
  //       ),
  //     );
  //   } else {
  //     Navigator.pushReplacement(
  //       navigatorKey.currentContext!,
  //       MaterialPageRoute(
  //         builder: (context) => const MainScreen(),
  //       ),
  //     );
  //   }
  // }
}
