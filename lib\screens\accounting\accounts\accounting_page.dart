import 'package:flutter/material.dart';
import 'package:inventory_application/screens/accounting/accounts/account_statement_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/accounts_list_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/double_entry_transfer_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/pay_to_account_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/receive_from_account_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/accounting_home_screen.dart';
import 'package:inventory_application/screens/components/back_button_header.dart';
import 'package:inventory_application/screens/home/<USER>';

class AccountingScreen extends StatefulWidget {
  const AccountingScreen({super.key});

  @override
  State<AccountingScreen> createState() => _AccountingScreenState();
}

class _AccountingScreenState extends State<AccountingScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          const BackButtonHeader(
            title: 'Accounts',
            icon: Icons.account_balance,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16.0,
                mainAxisSpacing: 16.0,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AccountStatementScreen(),
                        ),
                      );
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'كشف حساب',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AccountsListScreen(),
                        ),
                      );
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'قائمة الحسابات',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const PayTOAccountScreen(),
                        ),
                      );
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.yellow,
                      title: 'دفع لحساب',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              const ReceiveFromAccountScreen(),
                        ),
                      );
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.yellow,
                      title: 'قبض من حساب',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              const DoubleEntryTransferScreen(),
                        ),
                      );
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'قيد من حساب الى حساب (قيد مزدوج)',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
