import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/widgets/invoice_lsit_filter_dialog_widget.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class InvoicesListScreenScreen extends StatefulWidget {
  const InvoicesListScreenScreen({super.key});

  @override
  State<InvoicesListScreenScreen> createState() =>
      _InvoicesListScreenScreenState();
}

class _InvoicesListScreenScreenState extends State<InvoicesListScreenScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
  }

  //==================================================//
  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  //==================================================//
  void _onRefresh() async {
    await Provider.of<InvoiceController>(context, listen: false)
        .getInvoices(resetAndRefresh: true);
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  //==================================================//
  void _onLoading() async {
    await Provider.of<InvoiceController>(context, listen: false).getInvoices();
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<InvoiceController>(context).invoices;
    var status = AppController.isThereConnection;

    return ApplicationLayout(
      child: Column(
        children: [
          // HeaderText(

          CommonHeader(
            icon: Icons.receipt_long,
            title: T("All invoices"),
          ),
          // Search button header
          _buildSearchHeader(context),

          // Section header for invoices

          // Invoice list
          Expanded(
            child: status == true
                ? Padding(
                    padding: const EdgeInsets.all(10),
                    child: SmartRefresher(
                      enablePullDown: true,
                      enablePullUp: true,
                      header: WaterDropHeader(
                        waterDropColor: context.newPrimaryColor,
                      ),
                      footer: CustomFooter(
                        builder: (context, mode) {
                          Widget body;
                          if (mode == LoadStatus.loading) {
                            body = const CupertinoActivityIndicator();
                          } else if (mode == LoadStatus.idle) {
                            body = Text(
                              T("Pull up to load more"),
                              style: TextStyle(
                                color: context.newTextColor,
                              ),
                            );
                          } else if (mode == LoadStatus.failed) {
                            body = Text(
                              T("Load Failed! Click to retry!"),
                              style: TextStyle(
                                color: context.newTextColor,
                              ),
                            );
                          } else if (mode == LoadStatus.canLoading) {
                            body = Text(
                              T("Release to load more"),
                              style: TextStyle(
                                color: context.newTextColor,
                              ),
                            );
                          } else {
                            body = Text(
                              T("No more invoices"),
                              style: TextStyle(
                                color: context.newTextColor,
                              ),
                            );
                          }
                          return SizedBox(
                            height: 55.0,
                            child: Center(child: body),
                          );
                        },
                      ),
                      controller: _refreshController,
                      onRefresh: _onRefresh,
                      onLoading: _onLoading,
                      child: data.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.receipt_long,
                                    size: 80,
                                    color:
                                        context.newTextColor.withOpacity(0.3),
                                  ),
                                  const SizedBox(height: 15),
                                  Text(
                                    T("No invoices found"),
                                    style: TextStyle(
                                      color: context.newTextColor,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    T("Try adjusting your search parameters"),
                                    style: TextStyle(
                                      color:
                                          context.newTextColor.withOpacity(0.7),
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: data.length,
                              itemBuilder: (context, index) {
                                return InvoiceListItemWidget(
                                  onDelete: () {
                                    _onRefresh();
                                  },
                                  model: data[index],
                                );
                              },
                            ),
                    ),
                  )
                : Container(
                    margin: const EdgeInsets.only(top: 50),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.wifi_off,
                          size: 80,
                          color: context.newTextColor.withOpacity(0.3),
                        ),
                        const SizedBox(height: 20),
                        Text(
                          T("No Internet Connection"),
                          style: TextStyle(
                            color: context.newTextColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          T("Please check your connection and try again"),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: context.newTextColor.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 30),
                        ElevatedButton.icon(
                          onPressed: () {
                            _onRefresh();
                          },
                          icon: const Icon(Icons.refresh),
                          label: Text(T("Retry")),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.newPrimaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 25, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      width: context.width - 20,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      child: ElevatedButton.icon(
        onPressed: () {
          showDialog(
            context: context,
            builder: (BuildContext context) => InvoiceListFilterDialogWidget(
              onSearch: () {
                _onRefresh();
                setState(() {});
              },
            ),
          );
        },
        icon: const Icon(Icons.search),
        label: Text(T("Search Invoices")),
        style: ElevatedButton.styleFrom(
          backgroundColor: context.newSecondaryColor,
          foregroundColor: Colors.white,
          elevation: 3,
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }
}
