import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_draft_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/comparison/inventory_comparison_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/stocktaking_screen.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class StocktakingListScreen extends StatefulWidget {
  const StocktakingListScreen({super.key});

  @override
  _StocktakingListScreenState createState() => _StocktakingListScreenState();
}

class _StocktakingListScreenState extends State<StocktakingListScreen> {
  final InventoryOperationController _controller =
      InventoryOperationController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<Map<String, dynamic>> _stocktakingDrafts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStocktakingDrafts();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadStocktakingDrafts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final drafts = await _controller.getStocktakingDrafts();
      setState(() {
        _stocktakingDrafts = drafts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(message: T("Error loading stocktaking sessions: $e"));
    }
  }

  void _onRefresh() async {
    await _loadStocktakingDrafts();
    _refreshController.refreshCompleted();
  }

  void _createNewStocktaking() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StocktakingScreen(),
      ),
    ).then((_) {
      // Refresh list when returning from stocktaking screen
      _loadStocktakingDrafts();
    });
  }

  void _openStocktaking(Map<String, dynamic> draft) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StocktakingScreen(
          existingStocktakingId: draft['stocktakingId'],
          existingWarehouseId: draft['warehouseId']?.toString(),
          existingWarehouseName: draft['warehouseName'],
        ),
      ),
    ).then((_) {
      // Refresh list when returning from stocktaking screen
      _loadStocktakingDrafts();
    });
  }

  void _openInventoryComparison(Map<String, dynamic> draft) async {
    try {
      // Convert Map to DTO
      List<ProductDTO> items = [];

      // Parse items from JSON string
      final itemsJson = draft['items'] ?? '[]';
      try {
        final itemsList =
            List<Map<String, dynamic>>.from(jsonDecode(itemsJson));
        items = itemsList
            .map((item) => ProductDTO(
                  id: item['id'],
                  title: item['title'],
                  barcode: item['barcode'],
                  code: item['code'],
                  quantity: (item['quantity'] as num?)?.toDouble(),
                  virtualProductId: item['virtualProductId'],
                  hasSelectedAttributes: item['hasSelectedAttributes'] ?? false,
                  description: item['description'],
                ))
            .toList();
      } catch (e) {
        errorSnackBar(message: T("Error parsing stocktaking items: $e"));
        return;
      }

      final stocktakingDto = StocktakingDraftDTO(
        stocktakingId: draft['stocktakingId'],
        warehouseId: draft['warehouseId'],
        warehouseName: draft['warehouseName'],
        startDate: draft['startDate'] != null
            ? DateTime.tryParse(draft['startDate'])
            : null,
        items: items,
        status: draft['status'],
        lastModified: draft['lastModified'] != null
            ? DateTime.tryParse(draft['lastModified'])
            : null,
      );

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => InventoryComparisonScreen(
            stocktakingData: stocktakingDto,
          ),
        ),
      );
    } catch (e) {
      errorSnackBar(message: T("Error opening inventory comparison: $e"));
    }
  }

  Future<void> _deleteStocktaking(String stocktakingId) async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T("Delete Stocktaking")),
        content: Text(T(
            "Are you sure you want to delete this stocktaking session? All data will be lost.")),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(T("Cancel")),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(T("Delete")),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _controller.deleteStocktakingDraft(stocktakingId);
        successSnackBar(message: T("Stocktaking session deleted"));
        _loadStocktakingDrafts();
      } catch (e) {
        errorSnackBar(message: T("Error deleting stocktaking: $e"));
      }
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return "${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      return dateString;
    }
  }

  int _getItemsCount(String itemsJson) {
    try {
      final items = List.from(jsonDecode(itemsJson));
      return items.length;
    } catch (e) {
      return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 6,
                )
              ],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                CommonHeader(
                  icon: Icons.inventory_2_outlined,
                  title: T("Stocktaking Sessions"),
                ),

                // New Stocktaking Button
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _createNewStocktaking,
                      icon: const Icon(Icons.add),
                      label: Text(T("New Stocktaking")),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.newPrimaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _stocktakingDrafts.isEmpty
                    ? _buildEmptyState()
                    : _buildStocktakingList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 24),
          Text(
            T("No Stocktaking Sessions"),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("Create your first stocktaking session to get started"),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _createNewStocktaking,
            icon: const Icon(Icons.add),
            label: Text(T("Create New Stocktaking")),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStocktakingList() {
    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _stocktakingDrafts.length,
        itemBuilder: (context, index) {
          final draft = _stocktakingDrafts[index];
          final itemsCount = _getItemsCount(draft['items'] ?? '[]');

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.all(16),
              leading: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.newPrimaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory,
                  color: context.newPrimaryColor,
                  size: 24,
                ),
              ),
              title: Text(
                draft['warehouseName'] ?? T("Unknown Warehouse"),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: context.newTextColor,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    T("Started: ${_formatDate(draft['startDate'] ?? '')}"),
                    style: TextStyle(
                      color: context.newTextColor.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.inventory_2,
                        size: 16,
                        color: context.newSecondaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        T("$itemsCount items"),
                        style: TextStyle(
                          color: context.newSecondaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    T("Last Modified: ${_formatDate(draft['lastModified'] ?? '')}"),
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              trailing: Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Inventory Comparison Button

                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.delete_outline),
                        color: Colors.red,
                        onPressed: () =>
                            _deleteStocktaking(draft['stocktakingId']),
                      ),
                      const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.grey.shade400,
                        size: 16,
                      ),
                    ],
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _openInventoryComparison(draft),
                    icon: const Icon(Icons.analytics_outlined, size: 16),
                    label: Text(T("Inventory")),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.newSecondaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                  ),
                ],
              ),
              onTap: () => _openStocktaking(draft),
            ),
          );
        },
      ),
    );
  }
}
