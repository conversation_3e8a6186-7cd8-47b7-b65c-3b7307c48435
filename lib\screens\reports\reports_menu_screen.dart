import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/reports/customer_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/daily_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/monthly_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/product_sales_report_screen.dart';

class ReportsMenuScreen extends StatelessWidget {
  const ReportsMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white,
            Colors.grey.shade100,
          ],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Internal Reports Section
            Row(
              children: [
                Container(
                  width: 6,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  T('التقارير الداخلية'),
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Report cards grid
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildReportCard(
                      context,
                      title: T('Daily Sales'),
                      subtitle: T('View daily revenue and transactions'),
                      icon: Icons.calendar_today,
                      color1: const Color(0xFFFF8C42),
                      color2: const Color(0xFFEB4A5F),
                      iconBgColor: const Color(0xFF7209B7).withOpacity(0.2),
                      onTap: () => _navigateToReport(context, ReportType.daily),
                    ),
                    _buildReportCard(
                      context,
                      title: T('Monthly Sales'),
                      subtitle: T('Analyze monthly performance'),
                      icon: Icons.date_range,
                      color1: const Color(0xFF6B8DD6),
                      color2: const Color(0xFF8E37D7),
                      iconBgColor: const Color(0xFF4CC9F0).withOpacity(0.2),
                      onTap: () =>
                          _navigateToReport(context, ReportType.monthly),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildReportCard(
                      context,
                      title: T('Product Sales'),
                      subtitle: T('Track product performance'),
                      icon: Icons.inventory_2,
                      color1: const Color(0xFF667EEA),
                      color2: const Color(0xFF764BA2),
                      iconBgColor: const Color(0xFF4CC9F0).withOpacity(0.2),
                      onTap: () =>
                          _navigateToReport(context, ReportType.product),
                    ),
                    _buildReportCard(
                      context,
                      title: T('Customer Sales'),
                      subtitle: T('Analyze customer behavior'),
                      icon: Icons.people,
                      color1: const Color(0xFF4CA1AF),
                      color2: const Color(0xFF2C3E50),
                      iconBgColor: const Color(0xFF06D6A0).withOpacity(0.2),
                      onTap: () =>
                          _navigateToReport(context, ReportType.customer),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 40),

            // Server Reports Section
            Row(
              children: [
                Container(
                  width: 6,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade600,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  T('Server Reports'),
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Server Reports Coming Soon Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.shade300,
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                    spreadRadius: 2,
                  ),
                ],
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.cloud_sync,
                      size: 60,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    T('Coming Soon'),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                      letterSpacing: 1.2,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: 80,
                    height: 3,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    T('Server reports will be available in a future update.'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            // Bottom padding
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  // Build a report card
  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color1,
    required Color color2,
    required Color iconBgColor,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        height: 220,
        width: context.width / 2 - 15,
        margin: const EdgeInsets.all(5),
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color1, color2],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: color1.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: iconBgColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                icon,
                size: 28,
                color: Colors.white,
              ),
            ),
            const Spacer(),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 13,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.bottomRight,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.arrow_forward,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Navigate to the selected report screen
  void _navigateToReport(BuildContext context, ReportType type) {
    Widget reportScreen;

    switch (type) {
      case ReportType.daily:
        reportScreen = const DailySalesReportScreen();
        break;
      case ReportType.monthly:
        reportScreen = const MonthlySalesReportScreen();
        break;
      case ReportType.product:
        reportScreen = const ProductSalesReportScreen();
        break;
      case ReportType.customer:
        reportScreen = const CustomerSalesReportScreen();
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => reportScreen),
    );
  }
}

// Local enum for report types
enum ReportType {
  daily,
  monthly,
  product,
  customer,
}
