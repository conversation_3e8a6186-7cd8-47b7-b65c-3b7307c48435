import 'package:flutter/material.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';

class ConfigrationController with ChangeNotifier {
  List<ComboBoxDataModel> branchs = [];

  Future<List<ComboBoxDataModel>> fetchBranchs() async {
    try {
      branchs.clear();
      var url = '/Security/GetAllBranch';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            branchs.add(ComboBoxDataModel(element["Id"], element["Name"]));
          }
        }
      }
      notifyListeners();
      return branchs;
    } catch (e) {
      print(e);
      return [];
    }
  }
}
