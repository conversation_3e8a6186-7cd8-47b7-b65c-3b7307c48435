import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/dto/counter/device_counter_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';

class InventoryOperationCounterGenerator {
  //---------------------------------------------------------
  static Future<int> getCounterByType(
      {required InventoryOperationType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());

    return currentCounter;
  }

  //------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> setCounterByTypeAuto(
      {required InventoryOperationType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());
    currentCounter += 1;

    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), currentCounter.toString());
  }

  //---------------------------------------------------------
  static Future<bool> setCounterByType(
      {required InventoryOperationType type, required int counter}) async {
    if (counter == 0) {
      initializeCounterByType(type);
    }
    var oldCounter = await getCounterByType(type: type);
    if (oldCounter > counter) {
      await setInventoryOperationCounterInServer();
      return false;
    }
    counter = counter++;
    LocaleManager.instance.removeKeyByStringKey(type.toString());
    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), counter.toString());

    return true;
  }

  //------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> getInventoryOperationCounterFromServer() async {
    try {
      var types = [
        InventoryOperationType.ItemsTransfer,
      ];

      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var model = ApiDeviceCounterDTO(
          counterType: type.index,
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.inventoryOperation,
        );
        var url = '/Counter/GetCodeByTypes';
        var result = await Api.getWithBody(action: url, body: model.toJson());
        if (result?.isSuccess != null && result?.isSuccess == true) {
          setCounterByType(type: type, counter: result?.data ?? 0);
        } else {
          await initializeCounterByType(type);
        }
      }
    } catch (e) {
      print(e);
    }
  }

//------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> setInventoryOperationCounterInServer() async {
    try {
      var types = [
        InventoryOperationType.ItemsTransfer,
      ];
      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var counter = await getCounterByType(type: type);
        var model = ApiDeviceCounterDTO(
          counter: counter,
          counterType: type.index,
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.inventoryOperation,
        );
        var url = '/Counter/SetCodeByTypes';
        await Api.post(action: url, body: model.toJson());
      }
    } catch (e) {
      print(e);
    }
  }

  //---------------------------------------------------------
  static Future<void> initializeCounterByType(
      InventoryOperationType type) async {
    // LocaleManager.instance.removeKey(PreferencesKeys.SaleInvoice);
    if (!await LocaleManager.instance.checkKeyByStringKey(type.toString())) {
      await LocaleManager.instance
          .setStringValueByStringKey(type.toString(), 1.toString());
      // Initial counter value
    }
  }

//--------------------------------------------------------------------------
  static Future<String> getCurrentCounteByType(
      InventoryOperationType type) async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    // Format the counter to ensure it's always 7 digits

    return formattedCounter;
  }

//--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<String> getNextCounterByType(
      InventoryOperationType type) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();
    String formattedCounter = currentCounter.toString().padLeft(5, '0');
    switch (type) {
      case InventoryOperationType.DamagedExpired:
        break;
      case InventoryOperationType.Incoming:
        return 'IN${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case InventoryOperationType.Outgoing:
        return 'OUT${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
      case InventoryOperationType.OpeningBalance:
        break;
      case InventoryOperationType.ItemsTransfer:
        return 'TR${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case InventoryOperationType.Stocktaking:
        break;
      case InventoryOperationType.Shortage:
        break;
      case InventoryOperationType.Surplus:
        break;
    }
    return "";
    // Format the counter to ensure it's always 7 digits
  }

  //--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<bool> checkIfCounterUsed(
      InventoryOperationType type, String code) async {
    // Step 1: Extract the last segment (counter) from code
    try {
      final parts = code.split('-');
      if (parts.length != 3) return false;

      final counterString = parts.last; // e.g., "00023"
      final codeCounter = int.tryParse(counterString);
      if (codeCounter == null) return false;

      // Step 2: Get current counter from preferences
      final currentCounterString = LocaleManager.instance
          .getStringValueByStringKey(type.toString())
          .toString();

      final currentCounter = int.tryParse(currentCounterString);
      if (currentCounter == null) return false;

      // Step 3: Compare
      return codeCounter < currentCounter;
    } catch (e) {
      print("Error checking invoice counter: $e");
      return false;
    }
  }
}
