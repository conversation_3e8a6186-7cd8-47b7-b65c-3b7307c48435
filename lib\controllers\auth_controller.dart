import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/device_setup_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/auth/login_dto.dart';
import 'package:inventory_application/screens/auth/SignIn_screen.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:jwt_decode/jwt_decode.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:provider/provider.dart';

class AuthController with ChangeNotifier {
  static String _token = "";
  static String getToken() => _token;

  static Map<String, String> headers = Map.fromEntries({
    'Accept-Language': appCtrl.languageVal,
    "Authorization": 'Basic ${getToken()}',
    'Accept': 'application/json',
  }.entries);

  Future<void> login(
      {required BuildContext context, required LogInDto model}) async {
    if (AppController.isThereConnection == false) {
      await loginLocaly(context: context, model: model);
      return;
    }
    pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: true);

    try {
      var url = 'Security/Login';
      var counter = await CounterGenerator.getSaleInvoiceCurrentCounter();
      var deviceId = AppController.deviceId;
      var result = await Api.post(action: url, body: {
        "User_Name": model.usernameOrEmail,
        "User_Password": model.password,
        "Counter": counter,
        "Device_ID": deviceId,
      });

      if (result != null) {
        if (result.isSuccess) {
          addUserToLocalDataBase(
            model: LogInDto(
              usernameOrEmail: model.usernameOrEmail,
              password: hashPassword(model.password ?? ""),
            ),
          );
          _token = result.data["Token"];
          var tokenBody = decodeJWTToken(_token);

          LocaleManager.instance
              .setString(PreferencesKeys.TOKEN, result.data["Token"]);

          AppController.isAuth = true;

          Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
            (Route<dynamic> route) => false,
          );
          // ignore: use_build_context_synchronously
          try {
            // ignore: use_build_context_synchronously
            await initAfterLogin(context: context);
          } catch (e) {
            rethrow;
          }
        } else {
          pleaseWaitDialog(
              context: navigatorKey.currentContext!, isShown: false);
          errorSnackBar(message: "خطأ في اسم المستخدم او كلمة المرور");
          return;
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------
  Future<void> initAfterLogin({required BuildContext context}) async {
    // await AuthController.tryAutoLogin();
    await context.read<AppController>().getDeviceDetails();
    await context.read<DeviceSetupController>().deviceIdSetup();

    await CounterGenerator.getInvoicesCounterFromServer();
    await InventoryOperationCounterGenerator
        .getInventoryOperationCounterFromServer();

    // ignore: use_build_context_synchronously
    await context.read<CategoryController>().fetchCategories();
    // ignore: use_build_context_synchronously
    await context.read<WarehouseController>().fetchWarehouses();
    // ignore: use_build_context_synchronously
    await context.read<SalesmenController>().fetchSalesmen();
    // ignore: use_build_context_synchronously
    await context.read<CustomerController>().fetchCustomers();
    // await context.read<ProductController>().fetchProduct();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().getProductCount();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().fetchProductAfterCertineId();
  }

//--------------------------------------------------------------
  Future<void> checkAuth() async {
    try {
      var token = LocaleManager.instance.getStringValue(PreferencesKeys.TOKEN);
      if (token.isNotEmpty) {
        bool expirationDate = JwtDecoder.isExpired(token);

        if (expirationDate) {
          AppController.isAuth = false;
        } else {
          AppController.isAuth = true;
          await initAfterLogin(context: navigatorKey.currentContext!);
        }
      } else {
        AppController.isAuth = false;
      }
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------
  Future<void> logOut() async {
    try {
      LocaleManager.instance.removeKey(PreferencesKeys.TOKEN);
      AppController.isAuth = false;
      Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const SignInScreen()),
        (Route<dynamic> route) => false,
      );
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------
  Map<String, dynamic> decodeJWTToken(String token) {
    // Decode the payload (body) of the JWT
    Map<String, dynamic> payload = Jwt.parseJwt(token);
    return payload;
  }
//--------------------------------------------------------------

// #region Local Auth
  String hashPassword(String password) {
    var bytes = utf8.encode(password); // Convert password to bytes
    var hashedPassword = sha256.convert(bytes); // Hash the password
    return hashedPassword.toString(); // Return the hash as a string
  }

  //----------------------------------------------------------
  Future<bool> addUserToLocalDataBase({required LogInDto model}) async {
    try {
      final db = await DatabaseHelper().database;

      // Check if the user already exists by username
      var result = await db.query(
        'User',
        where: 'usernameOrEmail = ?',
        whereArgs: [model.usernameOrEmail],
      );

      if (result.isNotEmpty) {
        // User already exists
        return false;
      }

      // If user doesn't exist, add them to the database
      model.id = 0;
      var id = await db.insert('User', model.toJson());
      return id > 0;
    } catch (e) {
      // Handle any errors
      return false;
    }
  }

  //----------------------------------------------------------
  Future<LogInDto?> checkIfUserExist(LogInDto model) async {
    try {
      final db = await DatabaseHelper().database;
      var result = await db.query(
        'User',
        where: 'usernameOrEmail = ?',
        whereArgs: [model.usernameOrEmail],
      );
      if (result.isEmpty) {
        // User already exists
        return null;
      }
      var user = LogInDto.fromJson(result.first);
      return user;
    } catch (e) {
      return null;
    }
  }

  //----------------------------------------------------------
  Future<void> loginLocaly(
      {required BuildContext context, required LogInDto model}) async {
    try {
      pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: true);
      var user = await checkIfUserExist(model);
      if (user == null) {
        pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: false);
        errorSnackBar(message: "خطأ في اسم المستخدم او كلمة المرور");
      }
      var isPasswordCurrect =
          hashPassword(model.password ?? "") == user?.password;

      if (!isPasswordCurrect) {
        pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: false);
        errorSnackBar(message: "خطأ في اسم المستخدم او كلمة المرور");
      }
      AppController.isAuth = true;

      Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
        (Route<dynamic> route) => false,
      );
      // ignore: use_build_context_synchronously
      try {
        // ignore: use_build_context_synchronously
        await initAfterLogin(context: context);
      } catch (e) {
        rethrow;
      }
    } catch (e) {}
  }

// #endregion
}
