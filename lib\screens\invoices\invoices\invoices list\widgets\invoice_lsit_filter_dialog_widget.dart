import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_datetime_picker.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:provider/provider.dart';

class InvoiceListFilterDialogWidget extends StatefulWidget {
  const InvoiceListFilterDialogWidget({super.key, required this.onSearch});

  final Function onSearch;
  @override
  State<InvoiceListFilterDialogWidget> createState() =>
      _InvoiceListFilterDialogWidgetState();
}

class _InvoiceListFilterDialogWidgetState
    extends State<InvoiceListFilterDialogWidget> {
  @override
  Widget build(BuildContext context) {
    var filterData = Provider.of<InvoiceController>(context).filterModel;
    return AlertDialog(
      titlePadding: EdgeInsets.zero,
      insetPadding: EdgeInsets.zero,
      content: Container(
        constraints: BoxConstraints(
          maxWidth: context.width - 32,
          maxHeight: context.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Column(
                  children: [
                    const Text("من تاريخ"),
                    const SizedBox(height: 5),
                    MyDatePicker(
                      width: context.width / 2 - 30,
                      onSave: (date) {
                        filterData.fromDate = date;
                        setState(() {});
                      },
                      initialVal: filterData.fromDate ?? DateTime.now(),
                      caption: "".myDateFormatter(filterData.fromDate),
                      backColor: context.onBackground,
                    ),
                  ],
                ),
                const SizedBox(width: 10),
                Column(
                  children: [
                    const Text("الى تاريخ"),
                    const SizedBox(height: 5),
                    MyDatePicker(
                      width: context.width / 2 - 30,
                      onSave: (date) {
                        filterData.toDate = date;
                        setState(() {});
                      },
                      initialVal: filterData.toDate ?? DateTime.now(),
                      caption: "".myDateFormatter(filterData.toDate),
                      backColor: context.onBackground,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 10),
            MyComboBox(
              selectedValue: filterData.invoiceType.index,
              caption: filterData.invoiceTypeName ?? 'نوع الفاتورة',
              height: 55,
              width: context.width - 50,
              onSelect: (int id, String name) {
                filterData.invoiceType = SalesType.values[id];
                filterData.invoiceTypeName = name;
                setState(() {});
              },
              modalTitle: 'نوع الفاتورة',
              data: [
                ComboBoxDataModel(3, "فواتير مبيعات"),
                ComboBoxDataModel(4, "فواتير مرتجعات"),
                ComboBoxDataModel(0, "طلبيات المبيعات"),
              ],
              isShowLabel: false,
              labelText: "",
            ),
            const SizedBox(height: 10),
            SizedBox(
              width: context.width - 50,
              child: CommonTextField(
                // controller: invoiceCodeController,
                initialValue: filterData.invoiceCode,
                floatingLabelBehavior: FloatingLabelBehavior.never,
                label: 'رقم الفاتورة',
                onChanged: (p0) {
                  filterData.invoiceCode = p0;
                },
              ),
            ),
            const SizedBox(height: 10),
            MyComboBox(
              selectedValue: filterData.customerId,
              caption:
                  filterData.customerName ?? 'اسم العميل, اسم الزبون, الحساب',
              height: 55,
              width: context.width - 50,
              onSelect: (int id, String name) {
                filterData.customerId = id;
                filterData.customerName = name;
                setState(() {});
              },
              modalTitle: 'اسم العميل, اسم الزبون, الحساب',
              data: Provider.of<CustomerController>(context, listen: false)
                  .customers
                  .map(
                    (e) => ComboBoxDataModel(e.iD ?? 0, e.name ?? ""),
                  )
                  .toList(),
              isShowLabel: false,
              labelText: "",
            ),
            // const SizedBox(height: 15),
            // SizedBox(
            //   width: context.width - 55,
            //   child: RadioList(
            //     data: [
            //       ComboBoxDataModel(0, "الكل"),
            //       ComboBoxDataModel(1, "نقدي"),
            //       ComboBoxDataModel(1, "بالدين"),
            //     ],
            //     selectedValue: 0,
            //     onSelect: (int id, String name) {
            //       setState(() {});
            //     },
            //   ),
            // ),
            const SizedBox(height: 10),
            CommonMaterialButton(
              height: 45,
              label: "عرض النتائج",
              onPressed: () => widget.onSearch(),
            )
          ],
        ),
      ),
    );
  }
}
