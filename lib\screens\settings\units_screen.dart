import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:provider/provider.dart';

class UnitsScreen extends StatefulWidget {
  const UnitsScreen({Key? key}) : super(key: key);

  @override
  State<UnitsScreen> createState() => _UnitsScreenState();
}

class _UnitsScreenState extends State<UnitsScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUnits();
  }

  Future<void> _loadUnits() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<UnitController>(context, listen: false).fetchUnits();
    } catch (e) {
      errorSnackBar(message: T("Failed to load units"));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _syncUnits() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<UnitController>(context, listen: false).fetchUnitsFromServer();
      successSnackBar(message: T("Units synchronized successfully"));
    } catch (e) {
      errorSnackBar(message: T("Failed to synchronize units"));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(T("Units")),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _syncUnits,
            tooltip: T("Sync Units"),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Consumer<UnitController>(
              builder: (context, unitController, child) {
                final units = unitController.units;
                
                if (units.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          T("No units found"),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: _syncUnits,
                          icon: const Icon(Icons.sync),
                          label: Text(T("Sync Units")),
                        ),
                      ],
                    ),
                  );
                }
                
                return ListView.builder(
                  itemCount: units.length,
                  itemBuilder: (context, index) {
                    final unit = units[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: context.newPrimaryColor.withOpacity(0.2),
                        child: Text(
                          unit.name.substring(0, 1).toUpperCase(),
                          style: TextStyle(
                            color: context.newPrimaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(unit.name),
                      subtitle: Text(T("ID: ") + unit.id.toString()),
                    );
                  },
                );
              },
            ),
    );
  }
}
