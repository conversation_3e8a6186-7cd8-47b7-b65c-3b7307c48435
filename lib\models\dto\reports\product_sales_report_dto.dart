import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class ProductSalesReportDTO {
  final DateTime fromDate;
  final DateTime toDate;
  final double totalSales;
  final int totalQuantity;
  final List<InvoiceModel> invoices;
  final List<ProductSalesSummary> productSummaries;

  ProductSalesReportDTO({
    required this.fromDate,
    required this.toDate,
    required this.totalSales,
    required this.totalQuantity,
    required this.invoices,
    required this.productSummaries,
  });

  // Calculate sales by product
  Map<String, double> calculateSalesByProduct() {
    final Map<String, double> salesByProduct = {};

    for (var product in productSummaries) {
      salesByProduct[product.productName] = product.totalSales;
    }

    return salesByProduct;
  }

  // Calculate quantity by product
  Map<String, int> calculateQuantityByProduct() {
    final Map<String, int> quantityByProduct = {};

    for (var product in productSummaries) {
      quantityByProduct[product.productName] = product.quantity;
    }

    return quantityByProduct;
  }

  // Calculate sales by date
  Map<String, double> calculateSalesByDate() {
    final Map<String, double> salesByDate = {};

    for (var invoice in invoices) {
      if (invoice.entryDate != null) {
        final dateKey = DateFormat('yyyy-MM-dd').format(invoice.entryDate!);

        if (salesByDate.containsKey(dateKey)) {
          salesByDate[dateKey] = salesByDate[dateKey]! + (invoice.total ?? 0);
        } else {
          salesByDate[dateKey] = invoice.total ?? 0;
        }
      }
    }

    return salesByDate;
  }

  // Calculate top selling products
  List<ProductSalesSummary> getTopSellingProducts({int limit = 10}) {
    final sortedProducts = List<ProductSalesSummary>.from(productSummaries);
    sortedProducts.sort((a, b) => b.totalSales.compareTo(a.totalSales));

    return sortedProducts.take(limit).toList();
  }

  // Calculate top products by quantity
  List<ProductSalesSummary> getTopProductsByQuantity({int limit = 10}) {
    final sortedProducts = List<ProductSalesSummary>.from(productSummaries);
    sortedProducts.sort((a, b) => b.quantity.compareTo(a.quantity));

    return sortedProducts.take(limit).toList();
  }
}

class ProductSalesSummary {
  final int productId;
  final String productName;
  final String barcode;
  final int quantity;
  final double totalSales;
  final double averagePrice;
  final String unit;

  ProductSalesSummary({
    required this.productId,
    required this.productName,
    required this.barcode,
    required this.quantity,
    required this.totalSales,
    required this.averagePrice,
    required this.unit,
  });

  // Create from invoice products
  static List<ProductSalesSummary> createFromInvoices(
      List<InvoiceModel> invoices) {
    final Map<int, ProductSalesSummary> productMap = {};

    for (var invoice in invoices) {
      if (invoice.salesItems != null) {
        for (var item in invoice.salesItems!) {
          final productId = item.itemID ?? 0;

          if (productMap.containsKey(productId)) {
            // Update existing product summary
            final existing = productMap[productId]!;
            final newQuantity =
                existing.quantity + (item.quantity?.toInt() ?? 0);
            final newTotalSales = existing.totalSales +
                ((item.unitPrice ?? 0) * (item.quantity ?? 0));

            productMap[productId] = ProductSalesSummary(
              productId: productId,
              productName: item.itemName ?? 'Unknown',
              barcode: item.barcode ?? '',
              quantity: newQuantity,
              totalSales: newTotalSales,
              averagePrice: newTotalSales / newQuantity,
              unit: item.unitName ?? '',
            );
          } else {
            // Create new product summary
            final quantity = item.quantity?.toInt() ?? 0;
            final totalSales = (item.unitPrice ?? 0) * (item.quantity ?? 0);

            productMap[productId] = ProductSalesSummary(
              productId: productId,
              productName: item.itemName ?? 'Unknown',
              barcode: item.barcode ?? '',
              quantity: quantity,
              totalSales: totalSales,
              averagePrice: quantity > 0 ? totalSales / quantity : 0,
              unit: item.unitName ?? '',
            );
          }
        }
      }
    }

    return productMap.values.toList();
  }
}
