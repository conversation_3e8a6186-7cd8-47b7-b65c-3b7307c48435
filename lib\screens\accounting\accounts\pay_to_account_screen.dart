import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class PayTOAccountScreen extends StatefulWidget {
  const PayTOAccountScreen({super.key});

  @override
  State<PayTOAccountScreen> createState() => _PayTOAccountScreenState();
}

class _PayTOAccountScreenState extends State<PayTOAccountScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
