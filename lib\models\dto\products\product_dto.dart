import 'package:inventory_application/models/model/product_model.dart';

class ProductDTO {
  int? id;
  String? title;
  String? barcode;
  String? barcodeName;
  String? code;
  String? description;
  double? price;
  double? total;
  double? discountValue;
  double? stock;
  int? uniteId;
  String? uniteName;
  int? categoryId;
  String? category;
  double? quantity = 1;
  int? warehouseId;
  String? warehouseName;
  String? thumbnail;
  List<ProductWarehousesDTO>? warehouse;
  List<ItemPriceDTO>? units;
  List<Barcodes>? barcodes;
  List<ItemAttribute>? itemAttributes;
  List<int>? selectedOptionIds;
  bool hasSelectedAttributes =
      false; // Flag to indicate if attributes are pre-selected
  String? virtualProductId; // Virtual ID for product + attributes combination

  ProductDTO(
      {this.id,
      this.title,
      this.barcode,
      this.barcodeName,
      this.code,
      this.description,
      this.price,
      this.quantity = 1, // Default quantity set to 1
      this.discountValue,
      this.stock,
      this.uniteId,
      this.uniteName,
      this.units,
      this.categoryId,
      this.category,
      this.thumbnail,
      this.total,
      this.barcodes,
      this.warehouse,
      this.warehouseId,
      this.warehouseName,
      this.itemAttributes,
      this.hasSelectedAttributes = false,
      this.virtualProductId,
      this.selectedOptionIds}) {
    // Calculate total as quantity * price
    total = total ?? (price ?? 0) * (quantity ?? 1);
    warehouseId = warehouseId ?? warehouse?.first.id;
    warehouseName = warehouseName ?? warehouse?.first.name;
  }

  // Method to convert from JSON to Dart object (fromJson)
  factory ProductDTO.fromJson(Map<String, dynamic> json) {
    return ProductDTO(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      price: json['price'].toDouble(),
      discountValue: json['discountValue'],
      stock: json['stock'],
      category: json['category'],
      barcode: json['barcode'],
      quantity: json['quantity']?.toDouble(),
      categoryId: json['categoryId'],
      thumbnail: json['thumbnail'],
      selectedOptionIds: json['selectedOptionIds'] != null
          ? List<int>.from(json['selectedOptionIds'])
          : null,
    );
  }

  // Method to convert from Dart object to JSON (toJson)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'discountValue': discountValue,
      'stock': stock,
      'category': category,
      'thumbnail': thumbnail,
      "barcode": barcode,
      "quantity": quantity,
      'selectedOptionIds': selectedOptionIds,
    };
  }
}

//----------------------------------------------------------------------------------------
class ProductWarehousesDTO {
  int? id;
  String? name;
  double? quantity;

  ProductWarehousesDTO({
    this.id,
    this.name,
    this.quantity = 1,
  });
}

//---------------------------------------------------------`-------------------------------
class ItemPriceDTO {
  int? iD;
  int? itemID;
  int? unitID;
  String? unitName;
  bool? isDefult;
  // int? purchasePrice;
  double? salesPrice;

  ItemPriceDTO(
      {this.iD,
      this.itemID,
      this.unitID,
      this.unitName,
      this.isDefult,
      // this.purchasePrice,
      this.salesPrice});

  ItemPriceDTO.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    itemID = json['Item_ID'];
    unitID = json['Unit_ID'];
    unitName = json['Unit_Name'];
    // isDefult = json['Is_Defult'];
    // purchasePrice = json['Purchase_Price'];
    salesPrice = json['Sales_Price'] != null
        ? double.parse(json['Sales_Price'].toString())
        : 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Item_ID'] = itemID;
    data['Unit_ID'] = unitID;
    data['Unit_Name'] = unitName;
    data['Is_Defult'] = isDefult;
    // data['Purchase_Price'] = this.purchasePrice;
    data['Sales_Price'] = salesPrice;
    return data;
  }
}
//---------------------------------------------------------`-------------------------------