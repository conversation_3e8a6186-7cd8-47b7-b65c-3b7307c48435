import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/controllers/inventory_items_transfar_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:provider/provider.dart';

class InventoryItemTransareBaseInfoWidget extends StatefulWidget {
  const InventoryItemTransareBaseInfoWidget({super.key});

  @override
  State<InventoryItemTransareBaseInfoWidget> createState() =>
      _InventoryItemTransareBaseInfoWidgetState();
}

class _InventoryItemTransareBaseInfoWidgetState
    extends State<InventoryItemTransareBaseInfoWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var provider =
        Provider.of<InventoryItemsTransfarController>(context, listen: false);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        // padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section with Operation Info
            _buildHeaderSection(context, provider),
            const SizedBox(height: 5),

            // Warehouse Transfer Section
            _buildWarehouseTransferSection(context, provider),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(
      BuildContext context, InventoryItemsTransfarController provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                // padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory_2_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                T('Operation Details'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoCard(
                  context,
                  icon: Icons.tag,
                  label: T('Code'),
                  value: provider.inventoryItemTransfar.aPPReferanceCode ??
                      T('Not assigned'),
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoCard(
                  context,
                  icon: Icons.calendar_today,
                  label: T('Date'),
                  value: DateFormat('yyyy-MM-dd').format(DateTime.now()),
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseTransferSection(
      BuildContext context, InventoryItemsTransfarController provider) {
    return Container(
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.swap_horiz,
                  color: Colors.orange,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                T('Warehouse Transfer'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              // From Warehouse
              Expanded(
                child: _buildWarehouseSelector(
                  context,
                  provider,
                  isFromWarehouse: true,
                ),
              ),

              // Transfer Arrow
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.arrow_forward,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),

              // To Warehouse
              Expanded(
                child: _buildWarehouseSelector(
                  context,
                  provider,
                  isFromWarehouse: false,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseSelector(
    BuildContext context,
    InventoryItemsTransfarController provider, {
    required bool isFromWarehouse,
  }) {
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isFromWarehouse ? Icons.outbox : Icons.inbox,
                  size: 16,
                  color: isFromWarehouse ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 6),
                Text(
                  isFromWarehouse ? T('From') : T('To'),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          MyComboBox(
            width: double.infinity,
            selectedValue: isFromWarehouse
                ? provider.inventoryItemTransfar.fromStoreID
                : provider.inventoryItemTransfar.toStoreID,
            caption: isFromWarehouse
                ? (provider.inventoryItemTransfar.storeName ??
                    T("Select warehouse"))
                : (provider.inventoryItemTransfar.toStoreName ??
                    T("Select warehouse")),
            height: 48,
            onSelect: (int id, String name) {
              if (isFromWarehouse) {
                provider.inventoryItemTransfar.fromStoreID = id;
                provider.inventoryItemTransfar.storeName = name;
              } else {
                provider.inventoryItemTransfar.toStoreID = id;
                provider.inventoryItemTransfar.toStoreName = name;
              }
              setState(() {});
            },
            fontSize: 14,
            modalTitle: isFromWarehouse ? T("From") : T("To"),
            data: warehouseController.warehouses,
            isShowLabel: false,
            labelText: "",
          ),
        ],
      ),
    );
  }
}
