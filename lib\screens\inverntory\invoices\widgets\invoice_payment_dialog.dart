import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:provider/provider.dart';

void invoicePaymentDialog(
    BuildContext context, InvoiceDto invoice, Function onSavePatment) {
  TextEditingController paymentController = TextEditingController(
    text: (invoice.paymentValue ?? 0).toString(),
  );

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return Dialog(
        backgroundColor: Colors.white,
        insetPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        elevation: 5,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: context.width > 800 ? 800 : context.width - 32,
            maxHeight: context.height * 0.85,
          ),
          padding: const EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with invoice info
              Container(
                width: context.width,
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                decoration: BoxDecoration(
                  color: context.newBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "تفاصيل الدفع",
                          style: TextStyle(
                            color: context.newSecondaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "اجمالي الفاتورة: ${invoice.total?.covertDoubleToMoneyReturnString(invoice.total ?? 0) ?? "0"}",
                          style: TextStyle(
                            color: context.newTextColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 15),

              // Payment section header
              Container(
                padding: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: context.newSecondaryColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                width: double.infinity,
                child: Text(
                  "الدفعة المالية",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),

              const SizedBox(height: 15),

              // Payment input section
              Container(
                width: context.width,
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                decoration: BoxDecoration(
                  color: context.newBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Payment details
                    _buildPaymentRow(
                      context,
                      "المبلغ المدفوع",
                      CommonTextField(
                        controller: paymentController,
                        label: 'الدفعة',
                        floatingLabelBehavior: FloatingLabelBehavior.never,
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          if (value.isNotEmpty) {
                            try {
                              invoice.paymentValue = double.parse(value);
                            } catch (e) {
                              invoice.paymentValue = 0.0;
                            }
                          } else {
                            invoice.paymentValue = 0.0;
                          }
                        },
                      ),
                    ),

                    // Display calculated values
                    if (invoice.totalAfterDiscount != null)
                      _buildInfoRow(
                        context,
                        "صافي الفاتورة بعد الخصم",
                        0.0.covertDoubleToMoneyReturnString(
                            invoice.totalAfterDiscount ?? 0),
                      ),

                    if (invoice.paymentValue != null &&
                        invoice.paymentValue! > 0)
                      _buildInfoRow(
                        context,
                        "المبلغ المتبقي",
                        0.0.covertDoubleToMoneyReturnString(
                          (invoice.totalAfterDiscount ?? 0) -
                              (invoice.paymentValue ?? 0),
                        ),
                      ),
                  ],
                ),
              ),

              // Buttons
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      final controller = Provider.of<SaleInvoiceController>(
                          context,
                          listen: false);
                      controller.setPaymentValue(invoice.paymentValue ?? 0);
                      onSavePatment();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.newPrimaryColor,
                      foregroundColor: Colors.white,
                      elevation: 3,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 25, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      T("حفظ"),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: context.newSecondaryColor,
                      side: BorderSide(color: context.newSecondaryColor),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 25, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      T("إلغاء"),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget _buildPaymentRow(BuildContext context, String label, Widget input) {
  return Column(
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                  color: context.newSecondaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            SizedBox(
              width: context.width / 2.5,
              child: input,
            ),
          ],
        ),
      ),
      Divider(
        color: Colors.grey.withOpacity(0.3),
        thickness: 1,
      ),
    ],
  );
}

Widget _buildInfoRow(BuildContext context, String label, String value) {
  return Column(
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                  color: context.newSecondaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            Text(
              value,
              style: TextStyle(
                color: context.newTextColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
      Divider(
        color: Colors.grey.withOpacity(0.3),
        thickness: 1,
      ),
    ],
  );
}
