import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/helpers/pdf_helper_formatroll80.dart';
import 'package:inventory_application/models/bluetooth_printer.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

class PrinterService {
  // Helper method to create a PDF document from an invoice
  static Future<pw.Document> _createInvoicePdf(InvoiceDto invoice,
      [PrinterSettingsController? printerSettings]) async {
    // Create PDF document with proper RTL support
    final pdf = pw.Document();

    // Load fonts with better Arabic support
    pw.Font arabicFont;

    try {
      // Try to load Noto Naskh Arabic font first (better for Arabic text)
      final notoNaskhData =
          await rootBundle.load("assets/fonts/NotoNaskhArabic-Regular.ttf");
      arabicFont = pw.Font.ttf(notoNaskhData);
      debugPrint("Using Noto Naskh Arabic font");
    } catch (e) {
      debugPrint("Noto Naskh Arabic font not found: $e");
      try {
        // Try to load Noto Kufi Arabic font as fallback
        final notoKufiData =
            await rootBundle.load("assets/fonts/NotoKufiArabic-Regular.ttf");
        arabicFont = pw.Font.ttf(notoKufiData);
        debugPrint("Using Noto Kufi Arabic font");
      } catch (e) {
        debugPrint("Noto Kufi Arabic font not found: $e");
        // Fall back to the default font
        final fontData =
            await rootBundle.load("assets/fonts/NeoSans-Regular.ttf");
        arabicFont = pw.Font.ttf(fontData);
        debugPrint("Using NeoSans Regular font");
      }
    }

    // Default page format
    PdfPageFormat pageFormat = PdfPageFormat.roll80;

    // Default header and footer
    String receiptHeader = '';
    String receiptFooter = '';
    bool printLogo = false;

    // If printer settings are provided, use them
    if (printerSettings != null) {
      // Get page format from settings
      switch (printerSettings.paperSize) {
        case PaperSize.mm58:
          pageFormat = const PdfPageFormat(
              58 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
          break;
        case PaperSize.mm80:
          pageFormat = PdfPageFormat.roll80;
          break;
        case PaperSize.a4:
          pageFormat = PdfPageFormat.a4;
          break;
        case PaperSize.letter:
          pageFormat = PdfPageFormat.letter;
          break;
        default:
          pageFormat = PdfPageFormat.roll80;
      }

      // Get header and footer from settings
      receiptHeader = printerSettings.receiptHeader;
      receiptFooter = printerSettings.receiptFooter;
      printLogo = printerSettings.printLogo;
    }

    // Add content to PDF
    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Logo if configured
              if (printLogo)
                pw.Center(
                  child: pw.SizedBox(
                    width: 150,
                    height: 50,
                    child: pw.Container(
                      alignment: pw.Alignment.center,
                      child: pw.Text(
                        'LOGO',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),

              // Custom Header
              if (receiptHeader.isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    receiptHeader,
                    style: pw.TextStyle(font: arabicFont, fontSize: 12),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                  ),
                ),

              // Header
              pw.Center(
                child: pw.Text(
                  'فاتورة مبيعات',
                  style: pw.TextStyle(font: arabicFont, fontSize: 15),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
              pw.SizedBox(height: 10),

              // Invoice Info
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'رقم الفاتورة: ${invoice.invoiceCode ?? "N/A"}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.Text(
                    'التاريخ: ${invoice.invoiceDate?.toString().split(' ')[0] ?? "N/A"}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ],
              ),
              pw.SizedBox(height: 10),

              // Customer Info
              pw.Text(
                'العميل: ${invoice.custoemrName ?? "N/A"}',
                style: pw.TextStyle(font: arabicFont, fontSize: 10),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 10),

              // Items Header
              pw.Container(
                padding: const pw.EdgeInsets.all(5),
                decoration: const pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide(width: 1),
                    top: pw.BorderSide(width: 1),
                  ),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('السعر',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                    pw.Text('الكمية',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                    pw.Text('المنتج',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                  ],
                ),
              ),

              // Items
              ...invoice.salesItems?.map((item) => pw.Container(
                        padding: const pw.EdgeInsets.all(5),
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(width: 0.5),
                          ),
                        ),
                        child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text(
                              item.price?.toStringAsFixed(2) ?? "0.00",
                              style:
                                  pw.TextStyle(font: arabicFont, fontSize: 10),
                            ),
                            pw.Text(
                              item.quantity?.toString() ?? "0",
                              style:
                                  pw.TextStyle(font: arabicFont, fontSize: 10),
                            ),
                            pw.Expanded(
                              child: pw.Text(
                                textDirection: pw.TextDirection.rtl,
                                item.title ?? "N/A",
                                style: pw.TextStyle(
                                    font: arabicFont, fontSize: 10),
                                textAlign: pw.TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      )) ??
                  [],

              pw.SizedBox(height: 20),

              // Totals
              pw.Container(
                padding: const pw.EdgeInsets.all(5),
                decoration: const pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide(width: 1),
                    top: pw.BorderSide(width: 1),
                  ),
                ),
                child: pw.Column(
                  children: [
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          invoice.total?.toStringAsFixed(2) ?? "0.00",
                          style: pw.TextStyle(font: arabicFont, fontSize: 10),
                        ),
                        pw.Text(
                          'المجموع',
                          style: pw.TextStyle(font: arabicFont, fontSize: 10),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ],
                    ),
                    if (invoice.discountValue != null &&
                        invoice.discountValue! > 0)
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            invoice.discountValue?.toStringAsFixed(2) ?? "0.00",
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                            ),
                          ),
                          pw.Text(
                            'الخصم',
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ],
                      ),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          invoice.totalAfterDiscount?.toStringAsFixed(2) ??
                              "0.00",
                          style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold),
                        ),
                        pw.Text(
                          'الإجمالي',
                          style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Custom Footer
              if (receiptFooter.isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    receiptFooter,
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                  ),
                ),

              // Default Footer
              pw.Center(
                child: pw.Text(
                  'شكراً لتعاملكم معنا',
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  static Future<void> printInvoice(
      InvoiceDto invoice, BuildContext context) async {
    try {
      // Check if we're on mobile or desktop
      final bool isMobile =
          !Platform.isWindows && !Platform.isLinux && !Platform.isMacOS;

      // Get printer settings
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);

      // If on mobile and printer settings are not configured, just show the print dialog directly
      if (isMobile && printerSettings.printerType == null) {
        // Create PDF document
        final pdf = await _createInvoicePdf(invoice);

        // Show print dialog directly
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: 'Invoice ${invoice.invoiceCode}',
          format: PdfPageFormat.roll80, // Default to 80mm receipt
        );

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال الفاتورة للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return;
      }

      // For desktop or configured mobile printers, use the printer settings
      // Get the appropriate page format based on printer settings
      PdfPageFormat pageFormat;
      switch (printerSettings.paperSize) {
        case PaperSize.mm58:
          pageFormat = const PdfPageFormat(
              58 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
          break;
        case PaperSize.mm80:
          pageFormat = PdfPageFormat.roll80;
          break;
        case PaperSize.a4:
          pageFormat = PdfPageFormat.a4;
          break;
        case PaperSize.letter:
          pageFormat = PdfPageFormat.letter;
          break;
        default:
          pageFormat = PdfPageFormat.roll80;
      }

      // Create PDF document
      final pdf = await _createInvoicePdf(invoice, printerSettings);

      // Save PDF to temporary file
      final output = await getTemporaryDirectory();
      final file = File(
          '${output.path}/invoice_${invoice.invoiceCode ?? DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      // Print based on printer type
      if (printerSettings.printerType == 'network') {
        // Network printer
        if (printerSettings.printerIP != null) {
          // Use Printing package to print to network printer
          try {
            await Printing.layoutPdf(
              onLayout: (_) async => await pdf.save(),
              name: 'Invoice ${invoice.invoiceCode}',
              format: pageFormat,
            );

            // Check if context is still valid
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الفاتورة للطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            debugPrint('Error printing to network printer: $e');
            // Check if context is still valid
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('فشل في الطباعة: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } else if (printerSettings.printerType == 'usb') {
        // USB printer
        try {
          // Find the selected printer
          final printers = await Printing.listPrinters();
          final selectedPrinter = printers.firstWhere(
            (printer) => printer.name == printerSettings.selectedPrinterName,
            orElse: () => throw Exception('الطابعة المحددة غير متوفرة'),
          );

          // Print to the selected printer
          final result = await Printing.directPrintPdf(
            printer: selectedPrinter,
            onLayout: (_) async => await pdf.save(),
          );

          // Check if context is still valid
          if (context.mounted) {
            if (result) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم طباعة الفاتورة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('فشل في طباعة الفاتورة'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } catch (e) {
          debugPrint('Error printing to USB printer: $e');
          // Check if context is still valid
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في الطباعة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else if (printerSettings.printerType == 'bluetooth') {
        // Bluetooth printer
        try {
          debugPrint(
              'Printing to Bluetooth printer: ${printerSettings.selectedPrinterName}');

          // For now, we'll use the generic print dialog
          // In a real implementation, you would need to:
          // 1. Find the Bluetooth device by address
          // 2. Connect to it
          // 3. Send the ESC/POS commands for printing

          // This is a simplified implementation
          await Printing.layoutPdf(
            onLayout: (_) async => await pdf.save(),
            name: 'Invoice ${invoice.invoiceCode}',
            format: pageFormat,
          );

          // Check if context is still valid
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إرسال الفاتورة للطباعة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }

          // Note: For actual Bluetooth printing, you would need to:
          // 1. Convert the PDF to a bitmap or ESC/POS commands
          // 2. Connect to the Bluetooth printer
          // 3. Send the commands to the printer
          // This requires platform-specific code and a library like esc_pos_bluetooth
        } catch (e) {
          debugPrint('Error printing to Bluetooth printer: $e');
          // Check if context is still valid
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في الطباعة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // No printer configured, show print dialog
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: 'Invoice ${invoice.invoiceCode}',
          format: pageFormat,
        );
      }
    } catch (e) {
      debugPrint('Error printing invoice: $e');
      // Check if context is still valid
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء طباعة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      rethrow;
    }
  }
}
