enum InventoryOperationType {
  DamagedExpired,
  Incoming,
  Outgoing,
  OpeningBalance,
  ItemsTransfer,
  Stocktaking,
  Shortage,
  Surplus,
}

class InventoryOperationModel {
  String? code;
  int? localId;
  String? entryDateFormated;
  int? fromStoreID;
  int? toStoreID;
  String? storeName;
  String? toStoreName;
  int? iD;
  int? operationType;
  DateTime? entryDate;
  String? notes;
  bool? isPosted;
  int? sourceID;
  int? sourceReference1;
  String? sourceReference2;
  String? aPPReferanceCode;
  double? total;
  List<InventoryOperationItems>? inventoryOperationItems;

  InventoryOperationModel({
    this.code,
    this.localId,
    this.entryDateFormated,
    this.inventoryOperationItems,
    this.fromStoreID,
    this.toStoreID,
    this.storeName,
    this.toStoreName,
    this.iD,
    this.operationType,
    this.entryDate,
    this.notes,
    this.isPosted,
    this.sourceID,
    this.sourceReference1,
    this.sourceReference2,
    this.total,
    this.aPPReferanceCode,
  });

  InventoryOperationModel.fromJson(Map<String, dynamic> json) {
    code = json['Code'];
    localId = json['localId'];
    entryDateFormated = json['Entry_Date_Formated'];
    if (json['InventoryOperationItems'] != null) {
      inventoryOperationItems = <InventoryOperationItems>[];
      json['InventoryOperationItems'].forEach((v) {
        inventoryOperationItems!.add(new InventoryOperationItems.fromJson(v));
      });
    }
    fromStoreID = json['From_Store_ID'];
    toStoreID = json['To_Store_ID'];
    storeName = json['Store_Name'];
    toStoreName = json['To_Store_Name'];

    iD = json['ID'];
    operationType = json['Operation_Type'];
    entryDate =
        json['Entry_Date'] != null ? DateTime.parse(json['Entry_Date']) : null;

    notes = json['Notes'];

    isPosted = json['Is_Posted'];
    sourceID = json['Source_ID'];
    sourceReference1 = json['Source_Reference_1'];
    sourceReference2 = json['Source_Reference_2'];
    total = json['total'];
    aPPReferanceCode = json['APP_Referance_Code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Code'] = code;
    data['localId'] = localId;
    data['Entry_Date_Formated'] = entryDateFormated;
    if (inventoryOperationItems != null) {
      data['InventoryOperationItems'] =
          inventoryOperationItems!.map((v) => v.toJson()).toList();
    }
    data['From_Store_ID'] = fromStoreID;
    data['To_Store_ID'] = toStoreID;
    data['Store_Name'] = storeName;
    data['To_Store_Name'] = toStoreName;
    data['ID'] = iD;
    data['Operation_Type'] = operationType;
    data['Entry_Date'] = entryDate?.toIso8601String();
    data['Notes'] = notes;
    data['Is_Posted'] = isPosted;
    data['total'] = total;
    data['Source_ID'] = sourceID;
    data['Source_Reference_1'] = sourceReference1;
    data['Source_Reference_2'] = sourceReference2;
    data['APP_Referance_Code'] = aPPReferanceCode;
    return data;
  }
}

class InventoryOperationItems {
  double? quantity;
  double? balance;
  int? unitID;
  double? unitPrice;
  String? itemName;
  String? itemCode;
  int? operationItemID;
  int? operationID;
  int? itemID;

  int? storeID;
  String? barcode;
  bool? hasSelectedAttributes;
  List<int>? selectedOptionIds;

  InventoryOperationItems(
      {this.quantity,
      this.balance,
      this.unitID,
      this.unitPrice,
      this.itemName,
      this.itemCode,
      this.operationItemID,
      this.operationID,
      this.itemID,
      this.storeID,
      this.barcode,
      this.hasSelectedAttributes,
      this.selectedOptionIds});

  InventoryOperationItems.fromJson(Map<String, dynamic> json) {
    quantity = json['Quantity'];
    balance = json['Balance'];
    unitID = json['Unit_ID'];
    unitPrice = json['Unit_Price'];
    itemName = json['Item_Name'];
    itemCode = json['Item_Code'];
    operationItemID = json['Operation_Item_ID'];
    operationID = json['Operation_ID'];
    itemID = json['Item_ID'];
    storeID = json['Store_ID'];
    barcode = json['barcode'];
    hasSelectedAttributes = json['hasSelectedAttributes'];
    if (json['selectedOptionIds'] != null) {
      selectedOptionIds = List<int>.from(json['selectedOptionIds']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Quantity'] = quantity;
    data['Balance'] = balance;
    data['Unit_ID'] = unitID;
    data['Unit_Price'] = unitPrice;
    data['Item_Name'] = itemName;
    data['Item_Code'] = itemCode;
    data['Operation_Item_ID'] = operationItemID;
    data['Operation_ID'] = operationID;
    data['Item_ID'] = itemID;
    data['Store_ID'] = storeID;
    data['barcode'] = barcode;
    data['hasSelectedAttributes'] = hasSelectedAttributes;
    if (selectedOptionIds != null) {
      data['selectedOptionIds'] = selectedOptionIds;
    }

    return data;
  }
}
