import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/warehouse_model.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';

class WarehouseSelectionDialog extends StatefulWidget {
  final int? selectedWarehouseId;

  const WarehouseSelectionDialog({
    super.key,
    this.selectedWarehouseId,
  });

  @override
  _WarehouseSelectionDialogState createState() =>
      _WarehouseSelectionDialogState();
}

class _WarehouseSelectionDialogState extends State<WarehouseSelectionDialog> {
  final WarehouseController _warehouseService = WarehouseController();
  List<ComboBoxDataModel> _warehouses = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
  }

  Future<void> _loadWarehouses() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      setState(() {
        _warehouses = _warehouseService.warehouses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.maxFinite,
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              T("Select Warehouse"),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: context.newTextColor,
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_error != null)
              Center(
                child: Column(
                  children: [
                    Text(
                      T("Error loading warehouses"),
                      style: TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _loadWarehouses,
                      child: Text(T("Retry")),
                    ),
                  ],
                ),
              )
            else if (_warehouses.isEmpty)
              Center(
                child: Text(
                  T("No warehouses available"),
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _warehouses.length,
                  itemBuilder: (context, index) {
                    final warehouse = _warehouses[index];
                    final isSelected =
                        warehouse.id == widget.selectedWarehouseId;

                    return ListTile(
                      title: Text(warehouse.name ?? ''),
                      leading: Icon(
                        Icons.warehouse,
                        color:
                            isSelected ? context.newPrimaryColor : Colors.grey,
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle,
                              color: context.newPrimaryColor,
                            )
                          : null,
                      onTap: () {
                        Navigator.of(context).pop(warehouse);
                      },
                    );
                  },
                ),
              ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(T("Cancel")),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
