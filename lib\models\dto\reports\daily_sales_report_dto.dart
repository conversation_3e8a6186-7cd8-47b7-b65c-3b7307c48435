import 'package:inventory_application/models/model/invoice_model.dart';

class DailySalesReportDTO {
  final DateTime fromDate;
  final DateTime toDate;
  final double totalSales;
  final double totalDiscount;
  final double netSales;
  final int invoiceCount;
  final List<InvoiceModel> invoices;
  
  // Optional additional metrics
  final Map<String, double>? salesByCustomer;
  final Map<String, double>? salesByProduct;
  final Map<String, double>? salesByWarehouse;
  final Map<String, double>? salesByPaymentMethod;

  DailySalesReportDTO({
    required this.fromDate,
    required this.toDate,
    required this.totalSales,
    required this.totalDiscount,
    required this.netSales,
    required this.invoiceCount,
    required this.invoices,
    this.salesByCustomer,
    this.salesByProduct,
    this.salesByWarehouse,
    this.salesByPaymentMethod,
  });

  // Calculate sales by customer
  Map<String, double> calculateSalesByCustomer() {
    final Map<String, double> result = {};
    
    for (var invoice in invoices) {
      final customerName = invoice.customerName ?? 'Unknown';
      final total = invoice.total ?? 0.0;
      
      if (result.containsKey(customerName)) {
        result[customerName] = result[customerName]! + total;
      } else {
        result[customerName] = total;
      }
    }
    
    return result;
  }

  // Calculate sales by payment method
  Map<String, double> calculateSalesByPaymentMethod() {
    final Map<String, double> result = {
      'Cash': 0.0,
      'Card/Check': 0.0,
    };
    
    for (var invoice in invoices) {
      final total = invoice.total ?? 0.0;
      final paidAmount = invoice.paidAmount ?? 0.0;
      
      // Simple logic to determine payment method
      // This should be enhanced based on actual data structure
      if (paidAmount > 0) {
        result['Cash'] = result['Cash']! + total;
      } else {
        result['Card/Check'] = result['Card/Check']! + total;
      }
    }
    
    return result;
  }

  // Calculate sales by warehouse
  Map<String, double> calculateSalesByWarehouse() {
    final Map<String, double> result = {};
    
    for (var invoice in invoices) {
      final warehouseName = invoice.storeName ?? 'Unknown';
      final total = invoice.total ?? 0.0;
      
      if (result.containsKey(warehouseName)) {
        result[warehouseName] = result[warehouseName]! + total;
      } else {
        result[warehouseName] = total;
      }
    }
    
    return result;
  }

  // Create a copy with updated values
  DailySalesReportDTO copyWith({
    DateTime? fromDate,
    DateTime? toDate,
    double? totalSales,
    double? totalDiscount,
    double? netSales,
    int? invoiceCount,
    List<InvoiceModel>? invoices,
    Map<String, double>? salesByCustomer,
    Map<String, double>? salesByProduct,
    Map<String, double>? salesByWarehouse,
    Map<String, double>? salesByPaymentMethod,
  }) {
    return DailySalesReportDTO(
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      totalSales: totalSales ?? this.totalSales,
      totalDiscount: totalDiscount ?? this.totalDiscount,
      netSales: netSales ?? this.netSales,
      invoiceCount: invoiceCount ?? this.invoiceCount,
      invoices: invoices ?? this.invoices,
      salesByCustomer: salesByCustomer ?? this.salesByCustomer,
      salesByProduct: salesByProduct ?? this.salesByProduct,
      salesByWarehouse: salesByWarehouse ?? this.salesByWarehouse,
      salesByPaymentMethod: salesByPaymentMethod ?? this.salesByPaymentMethod,
    );
  }
}
