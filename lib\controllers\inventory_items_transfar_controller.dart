import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:provider/provider.dart';

class InventoryItemsTransfarController with ChangeNotifier {
  List<ProductDTO> selectedTransfarProduct = [];
  String? returnTransfarCode;
  InventoryOperationModel inventoryItemTransfar = InventoryOperationModel();

  void addProductToSelectedList(ProductDTO model) {
    // IMPORTANT: Apply default unit first, before any other processing
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
      navigatorKey.currentContext!,
      listen: false,
    );

    // Force apply default unit if default is set (regardless of whether product already has a unit)
    if (invoiceSettingsController.defaultUnitId != null) {
      model.uniteId = invoiceSettingsController.defaultUnitId;
      model.uniteName = invoiceSettingsController.defaultUnitName;
    }

    // Apply default warehouse if available
    if (inventoryItemTransfar.fromStoreID != null) {
      model.warehouseId = inventoryItemTransfar.fromStoreID;
      model.warehouseName = inventoryItemTransfar.storeName ?? "";
    }

    // Create a copy of the model to ensure it's a completely separate object
    ProductDTO newProduct = ProductDTO(
        id: model.id,
        title: model.title,
        barcode: model.barcode,
        barcodeName: model.barcodeName,
        code: model.code,
        description: model.description,
        price: model.price,
        total: model.total,
        discountValue: model.discountValue,
        stock: model.stock,
        uniteId: model.uniteId,
        uniteName: model.uniteName,
        category: model.category,
        quantity: model.quantity,
        warehouseId: model.warehouseId,
        warehouseName: model.warehouseName,
        thumbnail: model.thumbnail,
        warehouse: model.warehouse,
        units: model.units,
        barcodes: model.barcodes,
        hasSelectedAttributes: model.hasSelectedAttributes,
        virtualProductId: model.virtualProductId,
        itemAttributes: model.itemAttributes);

    // Check if this exact product + attributes combination already exists
    int existingIndex = -1;
    if (model.virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      existingIndex = selectedTransfarProduct.indexWhere(
        (element) => element.virtualProductId == model.virtualProductId,
      );
    } else {
      // Otherwise, fall back to checking by regular ID
      existingIndex = selectedTransfarProduct.indexWhere(
        (element) =>
            element.id == model.id &&
            element.barcode == model.barcode &&
            !element.hasSelectedAttributes,
      );
    }

    // If we found the exact same product, increase its quantity instead of adding a new one
    if (existingIndex >= 0) {
      // Create a new list to trigger UI update
      List<ProductDTO> updatedList = List.from(selectedTransfarProduct);

      double newQuantity = (updatedList[existingIndex].quantity ?? 1) +
          (newProduct.quantity ?? 1);

      // Update the quantity
      updatedList[existingIndex].quantity = newQuantity;

      // Update the total
      double price = updatedList[existingIndex].price ?? 0;
      updatedList[existingIndex].total = price * newQuantity;

      // Replace the list with the updated one
      selectedTransfarProduct = updatedList;
    } else {
      // Add as a new item
      selectedTransfarProduct.add(newProduct);
    }

    calculateInvoiceTotal();
    notifyListeners();
  }

  ///--------------------------------------------------------------------------
  void deleteProductFromSelectedList(int id, {String? virtualProductId}) {
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      selectedTransfarProduct.removeAt(productIndex);
      calculateInvoiceTotal();
      notifyListeners();
    }
  }

  //--------------------------------------------------------------------------
  //--------------------------------------------------------------------------
  void updateProductPrice(int id, double price, [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        selectedTransfarProduct[productIndex].price = price;
        selectedTransfarProduct[productIndex].total =
            price * (selectedTransfarProduct[productIndex].quantity ?? 1);
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        selectedTransfarProduct[productIndex].price = price;
        selectedTransfarProduct[productIndex].total =
            price * (selectedTransfarProduct[productIndex].quantity ?? 1);
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

  //--------------------------------------------------------------------------
  //--------------------------------------------------------------------------
  void updateProductQuantity(int id, double quantity,
      [String? virtualProductId]) async {
    // Find the product index
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      // Update the quantity
      selectedTransfarProduct[productIndex].quantity = quantity;

      // Update the total amount based on the new quantity and existing price
      double price = selectedTransfarProduct[productIndex].price ?? 0;
      selectedTransfarProduct[productIndex].total = price * quantity;

      // Calculate the invoice total
      calculateInvoiceTotal();

      // Notify listeners for UI update
      notifyListeners();

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 100));

      // Notify again to ensure UI is updated
      notifyListeners();
    }
  }

  //--------------------------------------------------------------------------
  void updateProductUnit(int id, ItemPriceDTO unit,
      [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedTransfarProduct[productIndex].uniteId = unit.unitID;
        selectedTransfarProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedTransfarProduct[productIndex].price ?? 0;
        double quantity = selectedTransfarProduct[productIndex].quantity ?? 1;
        selectedTransfarProduct[productIndex].total = price * quantity;
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedTransfarProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedTransfarProduct[productIndex].uniteId = unit.unitID;
        selectedTransfarProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedTransfarProduct[productIndex].price ?? 0;
        double quantity = selectedTransfarProduct[productIndex].quantity ?? 1;
        selectedTransfarProduct[productIndex].total = price * quantity;
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

  //--------------------------------------------------------------------------
  Future<dynamic> getItemByBarcode({String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) {
        return false;
      }

      // Notify that we're starting to process
      notifyListeners();

      // Use getItemByBarcodeWithWriting instead to handle final barcodes
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (result != null) {
        // If the product has attributes but they haven't been selected yet,
        // and it's not a final barcode, return the product so the UI can show the attribute dialog
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          return result; // Return the product so the UI can show the attribute selection dialog
        }

        // First check if we already have this product with the same barcode
        int existingIndex = -1;

        // Handle products with virtual ID (products with attributes)
        if (result.virtualProductId != null) {
          existingIndex = selectedTransfarProduct.indexWhere(
            (element) => element.virtualProductId == result.virtualProductId,
          );
        } else {
          // For regular products, check by barcode or product ID
          for (int i = 0; i < selectedTransfarProduct.length; i++) {
            var product = selectedTransfarProduct[i];
            if ((product.barcode == barcode || product.id == result.id) &&
                !product.hasSelectedAttributes) {
              existingIndex = i;
              break;
            }
          }
        }

        // If we found an existing product, increase its quantity
        if (existingIndex >= 0) {
          var existingProduct = selectedTransfarProduct[existingIndex];
          double newQuantity = (existingProduct.quantity ?? 1) + 1;

          // Update the quantity
          selectedTransfarProduct[existingIndex].quantity = newQuantity;

          // Update the total
          double price = selectedTransfarProduct[existingIndex].price ?? 0;
          selectedTransfarProduct[existingIndex].total = price * newQuantity;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        } else {
          // This is a new product, add it to the list
          // Apply default unit if default is set
          final invoiceSettingsController =
              Provider.of<InvoiceSettingsController>(
            navigatorKey.currentContext!,
            listen: false,
          );

          // Force apply default unit if default is set
          if (invoiceSettingsController.defaultUnitId != null) {
            result.uniteId = invoiceSettingsController.defaultUnitId;
            result.uniteName = invoiceSettingsController.defaultUnitName;
          }

          // Set warehouse if available
          if (inventoryItemTransfar.fromStoreID != null) {
            result.warehouseId = inventoryItemTransfar.fromStoreID;
            result.warehouseName = inventoryItemTransfar.storeName ?? "";
          }

          // Ensure quantity is at least 1
          result.quantity = result.quantity ?? 1;

          // Calculate total
          result.total = (result.price ?? 0) * result.quantity!;

          // Add to list
          selectedTransfarProduct.add(result);

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        }
      }

      return false;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return false;
    }
  }

  //---------------------------------------------------------------------------
  void setProductWarehouse(int id, int warehouseId, String warehouseName,
      [String? virtualProductId]) {
    ProductDTO product;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      product = selectedTransfarProduct.firstWhere(
        (element) => element.virtualProductId == virtualProductId,
        orElse: () => selectedTransfarProduct.firstWhere(
          (element) => element.id == id,
          orElse: () => ProductDTO(),
        ),
      );
    } else {
      // Otherwise use the regular product ID
      product = selectedTransfarProduct.firstWhere(
        (element) => element.id == id,
        orElse: () => ProductDTO(),
      );
    }

    if (product.id != null) {
      product.warehouseId = warehouseId;
      product.warehouseName = warehouseName;
      notifyListeners();
    }
  }

  //---------------------------------------------------------------------------
  Future<String> getItemsTransferNumber() async {
    try {
      var number =
          await InventoryOperationCounterGenerator.getNextCounterByType(
              InventoryOperationType.ItemsTransfer);
      inventoryItemTransfar.aPPReferanceCode ??= number;

      notifyListeners();
      return inventoryItemTransfar.aPPReferanceCode ?? "";
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------

  void calculateInvoiceTotal() {
    double total = 0;
    double totalQuantity = 0;

    for (var product in selectedTransfarProduct) {
      total += product.total ?? 0;
      totalQuantity += product.quantity ?? 0;
    }

    inventoryItemTransfar.total = total;

    // Ensure UI updates with immediate notification
    notifyListeners();

    // Add a small delay and notify again to ensure UI updates
    Future.delayed(const Duration(milliseconds: 100), () {
      notifyListeners();
    });
  }

  //---------------------------------------------------------------------------
  //---------------------------------------------------------- -----------------
  Future<ResponseResultModel> saveInventoryItemTransacion() async {
    try {
      var url = '/InventoryOperation/Manage?transactions_type=ItemsTransfer';
      final db = InventoryOperationController();

      var mapeditems =
          mapListProductDtoToInventoryOperationItem(selectedTransfarProduct);
      inventoryItemTransfar.inventoryOperationItems = mapeditems;

      // mapedInvoice.paidAmount ??= 0.0;

      inventoryItemTransfar.operationType =
          InventoryOperationType.ItemsTransfer.index;
      inventoryItemTransfar.entryDateFormated =
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());
      if (inventoryItemTransfar.iD == null ||
          (inventoryItemTransfar.iD ?? 0) == 0) {
        inventoryItemTransfar.entryDate = DateTime.now();
        inventoryItemTransfar.code = "*";
      }

      if (inventoryItemTransfar.aPPReferanceCode == null ||
          inventoryItemTransfar.aPPReferanceCode == "") {
        inventoryItemTransfar.aPPReferanceCode = await getItemsTransferNumber();
      } else {
        if (await InventoryOperationCounterGenerator.checkIfCounterUsed(
            InventoryOperationType.ItemsTransfer,
            inventoryItemTransfar.aPPReferanceCode ?? "")) {
          inventoryItemTransfar.aPPReferanceCode =
              await getItemsTransferNumber();
        }
      }
      inventoryItemTransfar.iD ??= 0;
      // mapedInvoice.iD = 0;
      // mapedInvoice.vATPercent = 0;

      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateInventoryOperation(
            SqlLiteInvoiceModel(
                    data: jsonEncode(inventoryItemTransfar.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                    id: null,
                    localCode: inventoryItemTransfar.aPPReferanceCode,
                    type: InventoryOperationType.ItemsTransfer.name)
                .toJson());
        inventoryItemTransfar = InventoryOperationModel();
        selectedTransfarProduct.clear();
        if (inventoryItemTransfar.iD == 0) {
          await InventoryOperationCounterGenerator.setCounterByTypeAuto(
              type: InventoryOperationType.ItemsTransfer);
        }
        notifyListeners();

        return ResponseResultModel(
            isSuccess: true, data: InvoiceResponseDto(localId: localId));
      }

      var result = await Api.post(
        action: url,
        body: inventoryItemTransfar.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          if (inventoryItemTransfar.iD == 0) {
            await InventoryOperationCounterGenerator
                .setInventoryOperationCounterInServer();
            await InventoryOperationCounterGenerator.setCounterByTypeAuto(
                type: InventoryOperationType.ItemsTransfer);
          }
          var response = InvoiceResponseDto.fromJson(result.data);
          inventoryItemTransfar.iD = response.id;
          inventoryItemTransfar.code = response.code;
          var localId = await db.insertOrUpdateInventoryOperation(
              SqlLiteInvoiceModel(
                      data: jsonEncode(inventoryItemTransfar.toJson()),
                      status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                      id: null,
                      localCode: inventoryItemTransfar.aPPReferanceCode,
                      type: InventoryOperationType.ItemsTransfer.name)
                  .toJson());
          response.localId = localId;
          inventoryItemTransfar = InventoryOperationModel();
          selectedTransfarProduct.clear();
          // await db.saveReturnToEcommerce(mapedInvoice);
          notifyListeners();
          return ResponseResultModel(data: response, isSuccess: true);
        }
      }
      return result ?? ResponseResultModel(isSuccess: false);
    } catch (e) {
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }
}
