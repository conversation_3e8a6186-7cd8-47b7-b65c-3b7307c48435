import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/app/app_constants.dart';
import 'package:inventory_application/base/lang/language_manager.dart';
import 'package:inventory_application/base/lang/language_notifier.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/base/network/ecommerce_api.dart';
import 'package:inventory_application/base/theme/app_theme_light.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/barcode_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/configration_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/device_setup_controller.dart';
import 'package:inventory_application/controllers/inventory_items_transfar_controller.dart';
import 'package:inventory_application/controllers/inventory_operation_expired_controller.dart';
import 'package:inventory_application/controllers/inventory_operation_incoming_controller.dart';
import 'package:inventory_application/controllers/inventory_operation_outgoing_conrtoller.dart';
import 'package:inventory_application/controllers/inventory_operation_stocktaking_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/payment_type_controller.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/controllers/sale_order_invoice_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/logging.dart';
import 'package:inventory_application/helpers/network_monitor.dart';
import 'package:inventory_application/services/authentication_service.dart';
import 'package:inventory_application/splash_screen.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import 'package:sqflite_common_ffi/sqflite_ffi.dart'; // Import sqflite_ffi for database initialization

GlobalKey<NavigatorState> navigatorKey = GlobalKey();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Handle Flutter framework errors
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    logErrorToFile(details.exceptionAsString(), details.stack.toString());
  };

  // Initialize SQLite for Windows (if applicable)
  if (Platform.isWindows) {
    print("🔧 Initializing sqflite_ffi...");
    databaseFactory =
        databaseFactoryFfi; // Initialize the database factory for FFI
  }

  // Ensure localization is initialized
  await EasyLocalization.ensureInitialized();

  // Permissions initialization
  // await requestPermissions();

  // Initialize Locale Manager
  LocaleManager.prefrencesInit();

  // Ensure API initialization
  Api.initDio();
  EcommerceApi.initDio();

  runZonedGuarded(() async {
    runApp(
      EasyLocalization(
        supportedLocales: LanguageManager.instance.supportedLocales,
        fallbackLocale: LanguageManager.instance.arLocale,
        path: ApplicationConstants.LANG_ASSET_PATH,
        saveLocale: true,
        startLocale: LanguageManager.instance.arLocale,
        child: MyApp(),
      ),
    );
  }, (error, stackTrace) {
    // Global error logging in case of uncaught errors
    logErrorToFile(error.toString(), stackTrace.toString());
  });
}

Future<void> requestPermissions() async {
  try {
    if (Platform.isAndroid) {
      // Request permissions for external storage
      Map<Permission, PermissionStatus> statuses = await [
        Permission.storage,
        Permission.manageExternalStorage,
      ].request();

      // Check if permissions are granted
      if (statuses[Permission.storage]?.isGranted == true &&
          statuses[Permission.manageExternalStorage]?.isGranted == true) {
        print("Permissions granted!");
      } else {
        print("Permissions not granted. Backup feature might not work.");
      }
    } else if (Platform.isWindows) {
      // Windows doesn't require the same permissions as Android
      print("Running on Windows - storage permissions are handled differently");
    }
  } catch (e, stackTrace) {
    // Log any error that occurs during permission requests
    logErrorToFile(e.toString(), stackTrace.toString());
    print("Failed to request permissions: $e");
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    requestPermissions();
    Api.initDio();
    EcommerceApi.initDio();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<AppController>(create: (_) => AppController()),
        ChangeNotifierProvider<AuthController>(create: (_) => AuthController()),
        ChangeNotifierProvider<BarcodeController>(
            create: (_) => BarcodeController()),
        ChangeNotifierProvider<CategoryController>(
            create: (_) => CategoryController()),
        ChangeNotifierProvider<ConfigrationController>(
            create: (_) => ConfigrationController()),
        ChangeNotifierProvider<CustomerController>(
            create: (_) => CustomerController()),
        ChangeNotifierProvider<InvoiceController>(
            create: (_) => InvoiceController()),
        ChangeNotifierProvider<InvoiceSettingsController>(
            create: (_) => InvoiceSettingsController()),
        ChangeNotifierProvider<PrinterSettingsController>(
            create: (_) => PrinterSettingsController()),
        ChangeNotifierProvider<ProductController>(
            create: (_) => ProductController()),
        ChangeNotifierProvider<ReturnInvoiceController>(
            create: (_) => ReturnInvoiceController()),
        ChangeNotifierProvider<SaleInvoiceController>(
            create: (_) => SaleInvoiceController()),
        ChangeNotifierProvider<SaleOrderInvoiceController>(
            create: (_) => SaleOrderInvoiceController()),
        ChangeNotifierProvider<WarehouseController>(
            create: (_) => WarehouseController()),
        ChangeNotifierProvider<UnitController>(create: (_) => UnitController()),
        ChangeNotifierProvider<LanguageNotifier>(
            create: (_) => LanguageNotifier()),
        ChangeNotifierProvider<SalesmenController>(
            create: (_) => SalesmenController()),
        ChangeNotifierProvider<ReportController>(
            create: (_) => ReportController()),
        ChangeNotifierProvider<PaymentTypeController>(
            create: (_) => PaymentTypeController()),
        ChangeNotifierProvider<DeviceSetupController>(
            create: (_) => DeviceSetupController()),
        ChangeNotifierProvider<InventoryOperationController>(
            create: (_) => InventoryOperationController()),
        ChangeNotifierProvider<InventoryItemsTransfarController>(
            create: (_) => InventoryItemsTransfarController()),
        ChangeNotifierProvider<InventoryOpertationOutgoingController>(
            create: (_) => InventoryOpertationOutgoingController()),
        ChangeNotifierProvider<InventoryOperationIncomingController>(
            create: (_) => InventoryOperationIncomingController()),
        ChangeNotifierProvider<InventoryOperationExpiredController>(
            create: (_) => InventoryOperationExpiredController()),
        ChangeNotifierProvider<InventoryOperationStocktakingController>(
            create: (_) => InventoryOperationStocktakingController()),
        ChangeNotifierProvider<AuthenticationService>(
            create: (_) => AuthenticationService()),
      ],
      child: Builder(
        builder: (context) {
          NetworkMonitor().startMonitoring();
          context.read<AuthController>().checkAuth();
          context.read<AppController>().currentLang = context.locale;
          context.read<LanguageNotifier>().updateCurrentLangId();
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            navigatorKey: navigatorKey,
            theme: ThemeData(
              colorScheme: AppThemeLight.instance.theme.colorScheme,
              fontFamily: context.locale.languageCode == "ar"
                  ? 'MontserratAR'
                  : 'Montserrat',
            ),
            home: const SplashScreen(),
            builder: (context, child) {
              return child!;
            },
          );
        },
      ),
    );
  }
}
