import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/accounting/accounts/accounting_page.dart';
import 'package:inventory_application/screens/accounting/others/Others_page.dart';
import 'package:inventory_application/screens/accounting/Debets/debts_page.dart';
import 'package:inventory_application/screens/accounting/financialfund/financial_fund_screen.dart';

class AccountingHomeScreen extends StatefulWidget {
  const AccountingHomeScreen({super.key});

  @override
  State<AccountingHomeScreen> createState() => _AccountingHomeScreenState();
}

class _AccountingHomeScreenState extends State<AccountingHomeScreen> {
  @override
  Widget build(BuildContext context) {
    final isTablet = context.width > 600;
    return Column(
      children: [
        // Header
        Container(
          width: double.infinity,
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: context.colors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.account_balance_wallet,
                size: 28,
                color: context.colors.primary,
              ),
              const SizedBox(width: 12),
              Text(
                T("Accounting"),
                style: TextStyle(
                  color: context.colors.primary,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        // Grid content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.count(
              crossAxisCount: isTablet ? 4 : 2,
              crossAxisSpacing: 16.0,
              mainAxisSpacing: 16.0,
              childAspectRatio: isTablet ? 1.1 : 1.0,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const AccountingScreen(),
                    ));
                  },
                  child: PlaceCard(
                    image: Image.asset(
                      "assets/images/home/<USER>",
                      height: 75,
                    ),
                    iconColor: Colors.yellow,
                    title: 'الحسابات',
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const DebtsScreen(),
                    ));
                  },
                  child: PlaceCard(
                    image: Image.asset(
                      "assets/images/home/<USER>",
                      height: 75,
                    ),
                    iconColor: Colors.yellow,
                    title: 'الديون',
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const FinancialFundPage(),
                    ));
                  },
                  child: PlaceCard(
                    image: Image.asset(
                      "assets/images/home/<USER>",
                      height: 75,
                    ),
                    iconColor: Colors.amber,
                    title: 'الصندوق',
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const OthersPage(),
                    ));
                  },
                  child: PlaceCard(
                    image: Image.asset(
                      "assets/images/home/<USER>",
                      height: 75,
                    ),
                    iconColor: Colors.amber,
                    title: 'غير ذلك',
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class PlaceCard extends StatelessWidget {
  final Image image;
  final Color iconColor;
  final String title;

  const PlaceCard({
    super.key,
    required this.image,
    required this.iconColor,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          image,
          const SizedBox(height: 16.0),
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
