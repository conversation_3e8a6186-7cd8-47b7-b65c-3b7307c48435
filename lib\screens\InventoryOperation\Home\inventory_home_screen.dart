import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/InventoryOperation/Inventory%20operation%20lists/local_inventory_operation_list_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/Item%20transactions/inventory_item_transfare_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20expired/invenroty_operation_expired_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20incoming/inventory_operation_incoming_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20outgoing/invenroty_operation_outgoing_screen.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/stocktaking_list_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/stocktaking_screen.dart';

class InventoryHomeScreen extends StatefulWidget {
  const InventoryHomeScreen({super.key});

  @override
  State<InventoryHomeScreen> createState() => _InventoryHomeScreenState();
}

class _InventoryHomeScreenState extends State<InventoryHomeScreen> {
  final GlobalKey repaintBoundaryKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: SafeArea(
        child: Column(
          children: [
            // Modern Header with gradient
            // _buildModernHeader(context),

            // Status widgets with improved styling
            const Column(
              children: [
                InternetConnectionStatusWidget(),
                SizedBox(height: 8),
                SyncizationStatusWidget(),
              ],
            ),

            // Main Content
            Expanded(
              child: CustomScrollView(
                physics: const BouncingScrollPhysics(),
                slivers: [
                  SliverPadding(
                    padding: const EdgeInsets.all(16),
                    sliver: SliverList(
                      delegate: SliverChildListDelegate([
                        // Quick Actions Section
                        _buildSectionHeader(
                            context, T("Quick Actions"), Icons.flash_on),
                        const SizedBox(height: 16),

                        // Main Action Cards Grid
                        _buildMainActionsGrid(context, isTablet),
                        const SizedBox(height: 32),

                        // Management Section
                        _buildSectionHeader(
                            context, T("Management"), Icons.settings),
                        const SizedBox(height: 16),

                        // Management Cards
                        _buildManagementSection(context),
                        const SizedBox(height: 80),
                      ]),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Modern Header with gradient background
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 16, 20, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.inventory_2,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T("Inventory Management"),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        T("Manage your inventory operations"),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Section Header
  Widget _buildSectionHeader(
      BuildContext context, String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF667EEA).withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF667EEA),
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D3748),
          ),
        ),
      ],
    );
  }

  // Main Actions Grid
  Widget _buildMainActionsGrid(BuildContext context, bool isTablet) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: isTablet ? 4 : 2,
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      childAspectRatio: isTablet ? 1.1 : 1.0,
      children: [
        ModernActionCard(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const InventoryItemTransfareScreen(),
              ),
            );
          },
          icon: Icons.swap_horiz_rounded,
          title: T("Item Transfer"),
          subtitle: T("Transfer items between warehouses"),
          gradient: const [Color(0xFF4CA1AF), Color(0xFF2C3E50)],
        ),
        ModernActionCard(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const InventoryOperationIncomingScreen(),
              ),
            );
          },
          icon: Icons.input_rounded,
          title: T("Incoming"),
          subtitle: T("Receive new inventory"),
          gradient: const [Color(0xFF667EEA), Color(0xFF764BA2)],
        ),
        ModernActionCard(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const InventoryOperationOutgoingScreen(),
              ),
            );
          },
          icon: Icons.output_rounded,
          title: T("Outgoing"),
          subtitle: T("Ship inventory out"),
          gradient: const [Color(0xFF6B8DD6), Color(0xFF8E37D7)],
        ),
        ModernActionCard(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const InventoryOperationExpiredScreen(),
              ),
            );
          },
          icon: Icons.warning_amber_rounded,
          title: T("Damaged"),
          subtitle: T("Handle damaged items"),
          gradient: const [Color(0xFFFF8C42), Color(0xFFEB4A5F)],
        ),
      ],
    );
  }

  // Management Section
  Widget _buildManagementSection(BuildContext context) {
    return Column(
      children: [
        ManagementCard(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const StocktakingListScreen(),
              ),
            );
          },
          icon: Icons.list_alt_rounded,
          title: T("الجرد"),
          subtitle: T("جرد موقعي للمستوعات"),
          color: const Color(0xFF4CA1AF),
        ),
        ManagementCard(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const InventoryOperationsScreen(),
              ),
            );
          },
          icon: Icons.list_alt_rounded,
          title: T("Inventory Operations"),
          subtitle: T("View all inventory operations"),
          color: const Color(0xFF4CA1AF),
        ),
      ],
    );
  }
}

class ModernActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final String title;
  final String subtitle;
  final List<Color> gradient;

  const ModernActionCard({
    super.key,
    required this.onTap,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: gradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: gradient[0].withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    icon,
                    size: 32,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ManagementCard extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;

  const ManagementCard({
    super.key,
    required this.onTap,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    size: 24,
                    color: color,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D3748),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
