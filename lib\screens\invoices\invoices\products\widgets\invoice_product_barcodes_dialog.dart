import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/product_model.dart';

Future<Barcodes?> showBarcodeSelectionDialog(
    BuildContext context, List<Barcodes> barcodes) async {
  TextEditingController searchController = TextEditingController();
  List<Barcodes> filteredBarcodes = List.from(barcodes);

  return showDialog<Barcodes>(
    context: context,
    builder: (BuildContext context) {
      Barcodes? selectedBarcode;

      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(T('Select a Barcode'),
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            content: SizedBox(
              width: double.maxFinite, // Ensures the dialog has enough width
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Search Bar
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: <PERSON><PERSON><PERSON>(
                      controller: searchController,
                      decoration: InputDecoration(
                        hintText: T('Search barcode...'),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                      onChanged: (query) {
                        setState(() {
                          filteredBarcodes = barcodes
                              .where((barcode) =>
                                  barcode.barCode!.contains(query) ||
                                  (barcode.barCodeName?.contains(query) ??
                                      false))
                              .toList();
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 10),

                  // **Fixed: Wrapping ListView in SizedBox & setting constraints**
                  ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxHeight: 250, // Ensures the list doesn't overflow
                    ),
                    child: filteredBarcodes.isNotEmpty
                        ? ListView.builder(
                            shrinkWrap: true,
                            itemCount: filteredBarcodes.length,
                            itemBuilder: (context, index) {
                              return Card(
                                elevation: 2,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8)),
                                child: ListTile(
                                  leading: const Icon(Icons.qr_code,
                                      color: Colors.blue),
                                  title: Text(
                                      filteredBarcodes[index].barCode ??
                                          "Unknown",
                                      style: const TextStyle(
                                          fontWeight: FontWeight.w500)),
                                  subtitle: Text(
                                      filteredBarcodes[index].barCodeName ??
                                          ""),
                                  onTap: () {
                                    selectedBarcode = filteredBarcodes[index];
                                    Navigator.of(context).pop(selectedBarcode);
                                  },
                                ),
                              );
                            },
                          )
                        : const Center(
                            child: Text("No matching barcodes found")),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.red)),
              ),
            ],
          );
        },
      );
    },
  );
}
