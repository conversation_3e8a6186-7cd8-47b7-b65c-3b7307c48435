import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';

enum BottomNavbarItems { invoices, inventory, accounting, reports }

class BottomNavbarWidget extends StatelessWidget {
  final BottomNavbarItems selectedBottomNavbarItem;
  final Function(int) onChange;

  const BottomNavbarWidget({
    super.key,
    required this.selectedBottomNavbarItem,
    required this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.07),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                context,
                index: 0,
                icon: Icons.inventory_2_rounded,
                label: T('Invoices'),
                isSelected:
                    selectedBottomNavbarItem == BottomNavbarItems.invoices,
              ),
              _buildNavItem(
                context,
                index: 1,
                icon: Icons.account_balance_wallet_rounded,
                label: T('Inventory'),
                isSelected:
                    selectedBottomNavbarItem == BottomNavbarItems.inventory,
              ),
              _buildNavItem(
                context,
                index: 2,
                icon: Icons.bar_chart_rounded,
                label: T('Accounting'),
                isSelected:
                    selectedBottomNavbarItem == BottomNavbarItems.accounting,
              ),
              _buildNavItem(
                context,
                index: 3,
                icon: Icons.settings,
                label: T('Reports'),
                isSelected:
                    selectedBottomNavbarItem == BottomNavbarItems.reports,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(BuildContext context,
      {required int index,
      required IconData icon,
      required String label,
      required bool isSelected}) {
    // Color schemes from our new design
    final List<List<Color>> colorGradients = [
      [Color(0xFF4CA1AF), Color(0xFF2C3E50)], // Inventory
      [Color(0xFF667EEA), Color(0xFF764BA2)], // Accounting
      [Color(0xFF6B8DD6), Color(0xFF8E37D7)], // Reports
      [Color(0xFFFF8C42), Color(0xFFEB4A5F)], // Settings
    ];

    return GestureDetector(
      onTap: () => onChange(index),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: isSelected
                  ? LinearGradient(
                      colors: colorGradients[index],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
              color: isSelected ? null : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: colorGradients[index][0].withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ]
                  : null,
            ),
            child: Icon(
              icon,
              size: 24,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 0),
          Text(
            label,
            style: TextStyle(
              color:
                  isSelected ? colorGradients[index][0] : Colors.grey.shade600,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
