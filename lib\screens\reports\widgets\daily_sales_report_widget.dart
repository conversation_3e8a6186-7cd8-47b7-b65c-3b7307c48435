import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/daily_sales_report_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:inventory_application/screens/reports/widgets/sales_chart_widget.dart';
import 'package:inventory_application/screens/reports/widgets/sales_trend_chart_widget.dart';

class DailySalesReportWidget extends StatefulWidget {
  final DailySalesReportDTO report;

  const DailySalesReportWidget({
    Key? key,
    required this.report,
  }) : super(key: key);

  @override
  State<DailySalesReportWidget> createState() => _DailySalesReportWidgetState();
}

class _DailySalesReportWidgetState extends State<DailySalesReportWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          // Tab bar at the top (fixed)
          TabBar(
            controller: _tabController,
            tabs: [
              Tab(text: T('Summary')),
              Tab(text: T('Invoices')),
            ],
            labelColor: context.newPrimaryColor,
            unselectedLabelColor: Colors.grey,
            indicatorColor: context.newPrimaryColor,
          ),

          // Content area (scrollable)
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Summary tab
                _buildScrollableTab(_buildSummaryContent()),

                // Invoices tab
                _buildScrollableTab(_buildInvoicesContent()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to create a scrollable tab
  Widget _buildScrollableTab(Widget content) {
    return ListView(
      children: [
        // Report header at the top of each tab
        _buildReportHeader(),

        // Tab content
        content,
      ],
    );
  }

  // Build report header with key metrics
  Widget _buildReportHeader() {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final fromDate = dateFormat.format(widget.report.fromDate);
    final toDate = dateFormat.format(widget.report.toDate);
    final dateRange =
        fromDate == toDate ? fromDate : '$fromDate ${T('to')} $toDate';

    return Container(
      padding: const EdgeInsets.all(16),
      color: context.newPrimaryColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            T('Daily Sales Report'),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: context.newPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            dateRange,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),

          // Key metrics in a responsive layout
          LayoutBuilder(
            builder: (context, constraints) {
              // Check if we're on a small screen (mobile)
              final isSmallScreen = constraints.maxWidth < 600;

              return isSmallScreen
                  ? Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Total Sales'),
                                value:
                                    widget.report.totalSales.toStringAsFixed(2),
                                icon: Icons.attach_money,
                                color: Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Discounts'),
                                value: widget.report.totalDiscount
                                    .toStringAsFixed(2),
                                icon: Icons.discount,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Net Sales'),
                                value:
                                    widget.report.netSales.toStringAsFixed(2),
                                icon: Icons.account_balance_wallet,
                                color: Colors.blue,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Invoices'),
                                value: widget.report.invoiceCount.toString(),
                                icon: Icons.receipt_long,
                                color: Colors.purple,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        _buildMetricCard(
                          title: T('Total Sales'),
                          value: widget.report.totalSales.toStringAsFixed(2),
                          icon: Icons.attach_money,
                          color: Colors.green,
                        ),
                        _buildMetricCard(
                          title: T('Discounts'),
                          value: widget.report.totalDiscount.toStringAsFixed(2),
                          icon: Icons.discount,
                          color: Colors.orange,
                        ),
                        _buildMetricCard(
                          title: T('Net Sales'),
                          value: widget.report.netSales.toStringAsFixed(2),
                          icon: Icons.account_balance_wallet,
                          color: Colors.blue,
                        ),
                        _buildMetricCard(
                          title: T('Invoices'),
                          value: widget.report.invoiceCount.toString(),
                          icon: Icons.receipt_long,
                          color: Colors.purple,
                        ),
                      ],
                    );
            },
          ),
        ],
      ),
    );
  }

  // Build a metric card for the header
  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Expanded(
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build the summary tab content
  Widget _buildSummaryContent() {
    // Calculate sales by customer and warehouse for display
    final salesByCustomer = widget.report.calculateSalesByCustomer();
    final salesByWarehouse = widget.report.calculateSalesByWarehouse();
    final salesByPaymentMethod = widget.report.calculateSalesByPaymentMethod();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sales trend chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SalesTrendChartWidget(
                report: widget.report,
              ),
            ),
          ),

          // Sales distribution chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SalesChartWidget(
                report: widget.report,
              ),
            ),
          ),

          // Sales by customer
          _buildSummarySection(
            title: T('Sales by Customer'),
            data: salesByCustomer,
            icon: Icons.person,
          ),

          const SizedBox(height: 24),

          // Sales by warehouse
          _buildSummarySection(
            title: T('Sales by Warehouse'),
            data: salesByWarehouse,
            icon: Icons.warehouse,
          ),

          const SizedBox(height: 24),

          // Sales by payment method
          _buildSummarySection(
            title: T('Sales by Payment Method'),
            data: salesByPaymentMethod,
            icon: Icons.payment,
          ),

          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build a summary section with a title and data table
  Widget _buildSummarySection({
    required String title,
    required Map<String, double> data,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: context.newPrimaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Data table
        if (data.isEmpty)
          Text(
            T('No data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: data.length,
            itemBuilder: (context, index) {
              final entry = data.entries.elementAt(index);
              return Card(
                elevation: 1,
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  title: Text(
                    entry.key,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  trailing: Text(
                    entry.value.toStringAsFixed(2),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  // Build the invoices tab content
  Widget _buildInvoicesContent() {
    final invoices = widget.report.invoices;

    if (invoices.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No invoices found for the selected period'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...invoices.map((invoice) => _buildInvoiceCard(invoice)),
          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build a card for a single invoice
  Widget _buildInvoiceCard(InvoiceModel invoice) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final formattedDate = invoice.entryDate != null
        ? dateFormat.format(invoice.entryDate!)
        : T('Unknown date');

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice header - responsive layout
            LayoutBuilder(
              builder: (context, constraints) {
                // Check if we're on a small screen (mobile)
                final isSmallScreen = constraints.maxWidth < 400;

                return isSmallScreen
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? T('Unknown')}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? T('Unknown')}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      );
              },
            ),
            const Divider(),

            // Invoice details
            _buildInvoiceDetailRow(
              label: T('Customer'),
              value: invoice.customerName ?? T('Unknown'),
              icon: Icons.person,
            ),
            _buildInvoiceDetailRow(
              label: T('Warehouse'),
              value: invoice.storeName ?? T('Unknown'),
              icon: Icons.warehouse,
            ),
            _buildInvoiceDetailRow(
              label: T('Total'),
              value: (invoice.total ?? 0.0).toStringAsFixed(2),
              icon: Icons.attach_money,
              valueColor: Colors.green.shade700,
              valueBold: true,
            ),
            if (invoice.discountValue != null && invoice.discountValue! > 0)
              _buildInvoiceDetailRow(
                label: T('Discount'),
                value: invoice.discountValue!.toStringAsFixed(2),
                icon: Icons.discount,
                valueColor: Colors.orange.shade700,
              ),

            // Invoice notes if available
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  '${T('Notes')}: ${invoice.notes}',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Build a detail row for the invoice card
  Widget _buildInvoiceDetailRow({
    required String label,
    required String value,
    required IconData icon,
    Color? valueColor,
    bool valueBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Check if we're on a very small screen
          final isVerySmallScreen = constraints.maxWidth < 300;

          return isVerySmallScreen
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          icon,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '$label:',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 24, top: 4),
                      child: Text(
                        value,
                        style: TextStyle(
                          color: valueColor,
                          fontWeight:
                              valueBold ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                )
              : Row(
                  children: [
                    Icon(
                      icon,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '$label:',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        value,
                        style: TextStyle(
                          color: valueColor,
                          fontWeight:
                              valueBold ? FontWeight.bold : FontWeight.normal,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                );
        },
      ),
    );
  }
}
