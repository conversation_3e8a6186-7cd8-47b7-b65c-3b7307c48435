import 'package:flutter/material.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class InvoiceSettingsController with ChangeNotifier {
  // Default customer settings
  int? _defaultCustomerId;
  String? _defaultCustomerName;

  // Default warehouse settings
  int? _defaultWarehouseId;
  String? _defaultWarehouseName;

  // Default unit settings
  int? _defaultUnitId;
  String? _defaultUnitName;

  // Getters
  int? get defaultCustomerId => _defaultCustomerId;
  String? get defaultCustomerName => _defaultCustomerName;
  int? get defaultWarehouseId => _defaultWarehouseId;
  String? get defaultWarehouseName => _defaultWarehouseName;
  int? get defaultUnitId => _defaultUnitId;
  String? get defaultUnitName => _defaultUnitName;

  // Constructor
  InvoiceSettingsController() {
    loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load customer settings
      _defaultCustomerId = prefs.getInt('defaultCustomerId');
      _defaultCustomerName = prefs.getString('defaultCustomerName');

      // Load warehouse settings
      _defaultWarehouseId = prefs.getInt('defaultWarehouseId');
      _defaultWarehouseName = prefs.getString('defaultWarehouseName');

      // Load unit settings
      _defaultUnitId = prefs.getInt('defaultUnitId');
      _defaultUnitName = prefs.getString('defaultUnitName');

      notifyListeners();
    } catch (e) {
      print("Error loading invoice settings: $e");
    }
  }

  // Save customer settings
  Future<void> setDefaultCustomer(int customerId, String customerName) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save to SharedPreferences
      await prefs.setInt('defaultCustomerId', customerId);
      await prefs.setString('defaultCustomerName', customerName);

      // Update controller state
      _defaultCustomerId = customerId;
      _defaultCustomerName = customerName;

      notifyListeners();
      successSnackBar(message: 'Default customer saved successfully');
    } catch (e) {
      print("Error saving default customer: $e");
      errorSnackBar(message: 'Failed to save default customer');
    }
  }

  // Save warehouse settings
  Future<void> setDefaultWarehouse(
      int warehouseId, String warehouseName) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save to SharedPreferences
      await prefs.setInt('defaultWarehouseId', warehouseId);
      await prefs.setString('defaultWarehouseName', warehouseName);

      // Update controller state
      _defaultWarehouseId = warehouseId;
      _defaultWarehouseName = warehouseName;

      notifyListeners();
      successSnackBar(message: 'Default warehouse saved successfully');
    } catch (e) {
      print("Error saving default warehouse: $e");
      errorSnackBar(message: 'Failed to save default warehouse');
    }
  }

  // Clear customer settings
  Future<void> clearDefaultCustomer() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove from SharedPreferences
      await prefs.remove('defaultCustomerId');
      await prefs.remove('defaultCustomerName');

      // Update controller state
      _defaultCustomerId = null;
      _defaultCustomerName = null;

      notifyListeners();
      successSnackBar(message: 'Default customer cleared');
    } catch (e) {
      print("Error clearing default customer: $e");
      errorSnackBar(message: 'Failed to clear default customer');
    }
  }

  // Clear warehouse settings
  Future<void> clearDefaultWarehouse() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove from SharedPreferences
      await prefs.remove('defaultWarehouseId');
      await prefs.remove('defaultWarehouseName');

      // Update controller state
      _defaultWarehouseId = null;
      _defaultWarehouseName = null;

      notifyListeners();
      successSnackBar(message: 'Default warehouse cleared');
    } catch (e) {
      print("Error clearing default warehouse: $e");
      errorSnackBar(message: 'Failed to clear default warehouse');
    }
  }

  // Save unit settings
  Future<void> setDefaultUnit(int unitId, String unitName) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save to SharedPreferences
      await prefs.setInt('defaultUnitId', unitId);
      await prefs.setString('defaultUnitName', unitName);

      // Update controller state
      _defaultUnitId = unitId;
      _defaultUnitName = unitName;

      notifyListeners();
      successSnackBar(message: 'Default unit saved successfully');
    } catch (e) {
      print("Error saving default unit: $e");
      errorSnackBar(message: 'Failed to save default unit');
    }
  }

  // Clear unit settings
  Future<void> clearDefaultUnit() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove from SharedPreferences
      await prefs.remove('defaultUnitId');
      await prefs.remove('defaultUnitName');

      // Update controller state
      _defaultUnitId = null;
      _defaultUnitName = null;

      notifyListeners();
      successSnackBar(message: 'Default unit cleared');
    } catch (e) {
      print("Error clearing default unit: $e");
      errorSnackBar(message: 'Failed to clear default unit');
    }
  }
}
