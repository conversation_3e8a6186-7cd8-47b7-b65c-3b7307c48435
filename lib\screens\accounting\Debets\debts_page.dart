import 'package:flutter/material.dart';
import 'package:inventory_application/screens/accounting/Debets/Register_old_debt_screen.dart';
import 'package:inventory_application/screens/accounting/Debets/debt_list_screen.dart';
import 'package:inventory_application/screens/accounting/Debets/register_a_new_debt_screen.dart';
import 'package:inventory_application/screens/accounting/accounts/accounting_home_screen.dart';
import 'package:inventory_application/screens/components/back_button_header.dart';
import 'package:inventory_application/screens/home/<USER>';

class DebtsScreen extends StatefulWidget {
  const DebtsScreen({super.key});

  @override
  State<DebtsScreen> createState() => _DebtsScreenState();
}

class _DebtsScreenState extends State<DebtsScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          const BackButtonHeader(
            title: 'Debts',
            icon: Icons.money_off,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16.0,
                mainAxisSpacing: 16.0,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const DebtListScreen(),
                      ));
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'قائمة الديون',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const RegisterANewDebtScreen(),
                      ));
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.amber,
                      title: 'تسجيل دين جديد',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const RegisterOldDebtScreen(),
                      ));
                    },
                    child: PlaceCard(
                      image: Image.asset(
                        "assets/images/home/<USER>",
                        height: 75,
                      ),
                      iconColor: Colors.yellow,
                      title: 'تسجيل الديون القديمة',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
