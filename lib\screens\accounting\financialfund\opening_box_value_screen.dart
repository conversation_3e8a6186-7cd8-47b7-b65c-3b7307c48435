import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class OpeningBoxValueScreen extends StatefulWidget {
  const OpeningBoxValueScreen({super.key});

  @override
  State<OpeningBoxValueScreen> createState() => _OpeningBoxValueScreenState();
}

class _OpeningBoxValueScreenState extends State<OpeningBoxValueScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
