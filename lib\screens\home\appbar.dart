import 'package:flutter/material.dart';

import 'dart:ui' as ui;

import 'package:inventory_application/base/extension/context_extension.dart';

class MyAppBar extends StatefulWidget {
  const MyAppBar({super.key, required this.onClick});

  final Function onClick;
  @override
  State<MyAppBar> createState() => _MyAppBarState();
}

class _MyAppBarState extends State<MyAppBar> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: ui.TextDirection.ltr,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.asset(
            "assets/images/base_images/main_logo.png",
            fit: BoxFit.contain,
          ),
          InkWell(
            onTap: () {
              widget.onClick();
            },
            child: Icon(
              Icons.menu_rounded,
              color: context.colors.scrim,
            ),
          )
        ],
      ),
    );
  }
}
