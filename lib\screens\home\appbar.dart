import 'package:flutter/material.dart';

import 'dart:ui' as ui;

import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/screens/setting/setting_page.dart';

class MyAppBar extends StatefulWidget {
  const MyAppBar({super.key, required this.onClick});

  final Function onClick;
  @override
  State<MyAppBar> createState() => _MyAppBarState();
}

class _MyAppBarState extends State<MyAppBar> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: ui.TextDirection.ltr,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Image.asset(
                "assets/images/base_images/main_logo.png",
                fit: BoxFit.contain,
              ),
              const SizedBox(width: 10),
              Text(
                AppController.currentBranchName,
                style: TextStyle(
                  color: context.colors.primary,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Row(
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SettingPage(),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.settings,
                    color: context.primaryColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 5),
              InkWell(
                onTap: () {
                  widget.onClick();
                },
                child: Icon(
                  Icons.menu_rounded,
                  color: context.colors.scrim,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
