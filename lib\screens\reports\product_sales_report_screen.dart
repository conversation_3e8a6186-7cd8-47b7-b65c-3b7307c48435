import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/base_report_screen.dart';
import 'package:inventory_application/screens/reports/widgets/product_sales_report_widget.dart';
import 'package:provider/provider.dart';

class ProductSalesReportScreen extends BaseReportScreen {
  const ProductSalesReportScreen({Key? key}) : super(key: key);

  @override
  State<ProductSalesReportScreen> createState() => _ProductSalesReportScreenState();
}

class _ProductSalesReportScreenState extends BaseReportScreenState<ProductSalesReportScreen> {
  @override
  ReportType get reportType => ReportType.productSales;

  @override
  String getReportTitle() => T('Product Sales');

  @override
  IconData getReportIcon() => Icons.inventory_2;

  @override
  Color getHeaderColor() => const Color(0xFFF72585);

  @override
  Color getAccentColor() => const Color(0xFF7209B7);

  @override
  Future<void> generateReport() async {
    setLoading(true);

    try {
      final reportController = Provider.of<ReportController>(context, listen: false);
      await reportController.generateProductSalesReport();
    } catch (e) {
      errorSnackBar(message: 'Error generating product sales report: $e');
    } finally {
      setLoading(false);
    }
  }

  @override
  Widget buildReportContent(ReportController reportController) {
    final report = reportController.productSalesReport;
    if (report == null) {
      return Center(
        child: Text(T('No product report data available')),
      );
    }
    return ProductSalesReportWidget(report: report);
  }
}
