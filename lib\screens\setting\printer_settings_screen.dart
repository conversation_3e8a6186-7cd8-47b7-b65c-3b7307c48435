import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/models/bluetooth_printer.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:printing/printing.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'dart:io';

class PrinterSettingsScreen extends StatefulWidget {
  const PrinterSettingsScreen({Key? key}) : super(key: key);

  @override
  State<PrinterSettingsScreen> createState() => _PrinterSettingsScreenState();
}

class _PrinterSettingsScreenState extends State<PrinterSettingsScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _printerNameController = TextEditingController();
  final _printerIPController = TextEditingController();
  final _printerPortController = TextEditingController();
  final _printerModelController = TextEditingController();
  final _receiptHeaderController = TextEditingController();
  final _receiptFooterController = TextEditingController();

  String _selectedPrinterType = 'network';
  PaperSize _selectedPaperSize = PaperSize.mm80;
  bool _printLogo = false;
  String? _logoPath;
  int _copies = 1;

  List<Printer> _availablePrinters = [];
  bool _isLoadingPrinters = false;
  bool _isTestingConnection = false;
  bool _isPrinting = false;
  bool _isScanning = false;
  List<BluetoothPrinter> _bluetoothPrinters = [];

  TabController? _tabController;

  // Helper method for translations
  String T(String text) => text;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSettings();
  }

  @override
  void dispose() {
    _printerNameController.dispose();
    _printerIPController.dispose();
    _printerPortController.dispose();
    _printerModelController.dispose();
    _receiptHeaderController.dispose();
    _receiptFooterController.dispose();
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final printerSettings =
        Provider.of<PrinterSettingsController>(context, listen: false);
    await printerSettings.loadSettings();

    setState(() {
      _printerNameController.text = printerSettings.printerName ?? '';
      _printerIPController.text = printerSettings.printerIP ?? '';
      _printerPortController.text =
          printerSettings.printerPort?.toString() ?? '';
      _printerModelController.text = printerSettings.printerModel ?? '';
      _selectedPrinterType = printerSettings.printerType ?? 'network';
      _selectedPaperSize = printerSettings.paperSize;
      _receiptHeaderController.text = printerSettings.receiptHeader;
      _receiptFooterController.text = printerSettings.receiptFooter;
      _printLogo = printerSettings.printLogo;
      _logoPath = printerSettings.logoPath;
      _copies = printerSettings.copies;
    });

    if (_selectedPrinterType == 'usb') {
      _loadAvailablePrinters();
    }
  }

  Future<void> _loadAvailablePrinters() async {
    setState(() {
      _isLoadingPrinters = true;
    });

    try {
      final printers = await Printing.listPrinters();
      setState(() {
        _availablePrinters = printers;
        _isLoadingPrinters = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPrinters = false;
      });
      errorSnackBar(message: T('Failed to load printers: $e'));
    }
  }

  // Scan for Bluetooth printers
  Future<void> _scanForBluetoothPrinters() async {
    setState(() {
      _isScanning = true;
      _bluetoothPrinters = [];
    });

    try {
      // Check if we're on a desktop platform
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // For desktop platforms, we'll use mock data since flutter_blue_plus doesn't support them
        await Future.delayed(const Duration(seconds: 2)); // Simulate scanning

        // Mock data for demonstration
        final mockPrinters = [
          BluetoothPrinter(
            name: 'Thermal Printer 58mm (Mock)',
            address: '00:11:22:33:44:55',
          ),
          BluetoothPrinter(
            name: 'POS Printer 80mm (Mock)',
            address: 'AA:BB:CC:DD:EE:FF',
          ),
          BluetoothPrinter(
            name: 'Label Printer (Mock)',
            address: '11:22:33:44:55:66',
          ),
        ];

        setState(() {
          _bluetoothPrinters = mockPrinters;
          _isScanning = false;
        });

        // Show a message that this is mock data
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(T(
                  'Bluetooth scanning is not supported on desktop platforms. Showing mock data.')),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // For mobile platforms, use the actual Bluetooth scanning
      // Check if Bluetooth is available and turned on
      if (!await FlutterBluePlus.isSupported) {
        throw Exception('Bluetooth is not supported on this device');
      }

      // Request Bluetooth permissions on Android
      if (Platform.isAndroid) {
        try {
          // Try to directly use the FlutterBluePlus requestPermissions method
          await FlutterBluePlus.turnOn();

          // Check if we have the necessary permissions
          bool hasPermissions = false;
          try {
            // This will throw an exception if permissions are not granted
            await FlutterBluePlus.isSupported;
            hasPermissions = true;
          } catch (e) {
            debugPrint("Permission check failed: $e");
            hasPermissions = false;
          }

          if (!hasPermissions) {
            if (mounted) {
              errorSnackBar(
                  message: T(
                      'Bluetooth permissions are required to scan for printers. Please grant them in settings.'));
            }
            setState(() {
              _isScanning = false;
            });
            return;
          }
        } catch (e) {
          debugPrint("Error requesting Bluetooth permissions: $e");
          if (mounted) {
            errorSnackBar(
                message: T('Failed to request Bluetooth permissions: $e'));
          }
          setState(() {
            _isScanning = false;
          });
          return;
        }
      }

      // Check if Bluetooth is turned on
      final adapterState = await FlutterBluePlus.adapterState.first;
      if (adapterState != BluetoothAdapterState.on) {
        // Ask user to turn on Bluetooth
        if (mounted) {
          errorSnackBar(
              message: T('Please turn on Bluetooth to scan for printers'));
        }
        setState(() {
          _isScanning = false;
        });
        return;
      }

      // Start scanning
      List<BluetoothDevice> devices = [];

      // Show a message that scanning has started
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(T('Scanning for Bluetooth devices...')),
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // Set up subscription for scan results
      final subscription = FlutterBluePlus.scanResults.listen((results) {
        // Update the list of devices
        setState(() {
          for (ScanResult result in results) {
            debugPrint(
                "Found device: ${result.device.platformName} (${result.device.remoteId.str}) - RSSI: ${result.rssi}");

            // Only add the device if it's not already in the list
            if (!devices
                .any((d) => d.remoteId.str == result.device.remoteId.str)) {
              devices.add(result.device);

              // Convert to BluetoothPrinter and add to the list
              _bluetoothPrinters
                  .add(BluetoothPrinter.fromDevice(result.device));

              // Show a message when a device is found
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text(T('Found device: ${result.device.platformName}')),
                    duration: const Duration(seconds: 1),
                  ),
                );
              }
            }
          }
        });
      });

      // Start scanning with a longer timeout
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 20),
        androidScanMode: AndroidScanMode.lowLatency,
      );

      // Wait for scan to complete
      await Future.delayed(const Duration(seconds: 20));

      // Stop scanning
      await FlutterBluePlus.stopScan();
      await subscription.cancel();

      setState(() {
        _isScanning = false;
      });

      if (_bluetoothPrinters.isEmpty && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(T(
                'No Bluetooth devices found. Make sure your printer is turned on and in pairing mode.')),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error scanning for Bluetooth printers: $e');
      setState(() {
        _isScanning = false;
      });
      if (mounted) {
        errorSnackBar(message: T('Failed to scan for Bluetooth printers: $e'));
      }
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    final printerSettings =
        Provider.of<PrinterSettingsController>(context, listen: false);
    try {
      await printerSettings.savePrinterSettings(
        printerName: _printerNameController.text,
        type: _selectedPrinterType,
        ip: _printerIPController.text,
        port: int.tryParse(_printerPortController.text),
        model: _printerModelController.text,
        paperSize: _selectedPaperSize,
        receiptHeader: _receiptHeaderController.text,
        receiptFooter: _receiptFooterController.text,
        printLogo: _printLogo,
        logoPath: _logoPath,
        copies: _copies,
      );
      successSnackBar(message: T('Printer settings saved successfully'));
    } catch (e) {
      errorSnackBar(message: T('Failed to save printer settings: $e'));
    }
  }

  Future<void> _clearSettings() async {
    final printerSettings =
        Provider.of<PrinterSettingsController>(context, listen: false);
    try {
      await printerSettings.clearPrinterSettings();
      _printerNameController.clear();
      _printerIPController.clear();
      _printerPortController.clear();
      _printerModelController.clear();
      _receiptHeaderController.clear();
      _receiptFooterController.clear();
      setState(() {
        _selectedPrinterType = 'network';
        _selectedPaperSize = PaperSize.mm80;
        _printLogo = false;
        _logoPath = null;
        _copies = 1;
      });
      successSnackBar(message: T('Printer settings cleared successfully'));
    } catch (e) {
      errorSnackBar(message: T('Failed to clear printer settings: $e'));
    }
  }

  // Test printer connection
  Future<void> _testPrinterConnection() async {
    setState(() {
      _isTestingConnection = true;
    });

    final printerSettings =
        Provider.of<PrinterSettingsController>(context, listen: false);

    try {
      final isConnected = await printerSettings.testPrinterConnection();

      setState(() {
        _isTestingConnection = false;
      });

      if (isConnected) {
        successSnackBar(message: T('Printer connection successful'));
      } else {
        errorSnackBar(message: T('Failed to connect to printer'));
      }
    } catch (e) {
      setState(() {
        _isTestingConnection = false;
      });
      errorSnackBar(message: T('Error testing printer connection: $e'));
    }
  }

  // Print test receipt
  Future<void> _printTestReceipt() async {
    setState(() {
      _isPrinting = true;
    });

    final printerSettings =
        Provider.of<PrinterSettingsController>(context, listen: false);

    try {
      final success = await printerSettings.printTestReceipt();

      setState(() {
        _isPrinting = false;
      });

      if (success) {
        successSnackBar(message: T('Test receipt printed successfully'));
      } else {
        errorSnackBar(message: T('Failed to print test receipt'));
      }
    } catch (e) {
      setState(() {
        _isPrinting = false;
      });
      errorSnackBar(message: T('Error printing test receipt: $e'));
    }
  }

  // Select logo file
  Future<void> _selectLogoFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _logoPath = result.files.first.path;
          _printLogo = true;
        });
        successSnackBar(message: T('Logo selected successfully'));
      }
    } catch (e) {
      errorSnackBar(message: T('Error selecting logo: $e'));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(T('Printer Settings')),
        bottom: _tabController != null
            ? TabBar(
                controller: _tabController,
                tabs: [
                  Tab(text: T('Connection')),
                  Tab(text: T('Receipt')),
                  Tab(text: T('Test')),
                ],
              )
            : null,
      ),
      body: _tabController != null
          ? TabBarView(
              controller: _tabController,
              children: [
                // Connection Tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Printer Type Selection
                        Text(
                          T('Printer Type'),
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: Text(T('Network Printer')),
                                    value: 'network',
                                    groupValue: _selectedPrinterType,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedPrinterType = value!;
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: Text(T('USB Printer')),
                                    value: 'usb',
                                    groupValue: _selectedPrinterType,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedPrinterType = value!;
                                        if (value == 'usb') {
                                          _loadAvailablePrinters();
                                        }
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: Text(T('Bluetooth Printer')),
                                    value: 'bluetooth',
                                    groupValue: _selectedPrinterType,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedPrinterType = value!;
                                        if (value == 'bluetooth') {
                                          _scanForBluetoothPrinters();
                                        }
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                    child:
                                        Container()), // Empty space for alignment
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Network Printer Settings
                        if (_selectedPrinterType == 'network') ...[
                          TextFormField(
                            controller: _printerNameController,
                            decoration: InputDecoration(
                              labelText: T('Printer Name'),
                              border: const OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return T('Please enter a printer name');
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _printerIPController,
                            decoration: InputDecoration(
                              labelText: T('IP Address'),
                              border: const OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return T('Please enter an IP address');
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _printerPortController,
                            decoration: InputDecoration(
                              labelText: T('Port (Optional)'),
                              hintText: T('Default port will be used if empty'),
                              border: const OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                if (int.tryParse(value) == null) {
                                  return T('Please enter a valid port number');
                                }
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _printerModelController,
                            decoration: InputDecoration(
                              labelText: T('Printer Model'),
                              border: const OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return T('Please enter a printer model');
                              }
                              return null;
                            },
                          ),
                        ],

                        // USB Printer Selection
                        if (_selectedPrinterType == 'usb') ...[
                          if (_isLoadingPrinters)
                            const Center(child: CircularProgressIndicator())
                          else if (_availablePrinters.isEmpty)
                            Center(
                              child: Text(
                                T('No USB printers found'),
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            )
                          else
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  T('Available Printers'),
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                                const SizedBox(height: 8),
                                ..._availablePrinters.map((printer) => Card(
                                      child: ListTile(
                                        title: Text(printer.name),
                                        subtitle: Text(printer.url),
                                        selected: _printerNameController.text ==
                                            printer.name,
                                        onTap: () {
                                          setState(() {
                                            _printerNameController.text =
                                                printer.name;
                                            _printerModelController.text =
                                                printer.model ?? '';
                                          });
                                        },
                                      ),
                                    )),
                              ],
                            ),
                        ],

                        // Bluetooth Printer Selection
                        if (_selectedPrinterType == 'bluetooth') ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                T('Bluetooth Printers'),
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              ElevatedButton.icon(
                                onPressed: _isScanning
                                    ? null
                                    : _scanForBluetoothPrinters,
                                icon: _isScanning
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2))
                                    : const Icon(Icons.refresh),
                                label: Text(T('Scan')),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_isScanning)
                            const Center(child: CircularProgressIndicator())
                          else if (_bluetoothPrinters.isEmpty)
                            Center(
                              child: Text(
                                T('No Bluetooth printers found. Tap Scan to search for printers.'),
                                style: Theme.of(context).textTheme.bodyLarge,
                                textAlign: TextAlign.center,
                              ),
                            )
                          else
                            Column(
                              children: [
                                ..._bluetoothPrinters.map((printer) => Card(
                                      child: ListTile(
                                        leading: const Icon(Icons.bluetooth),
                                        title: Text(printer.name),
                                        subtitle: Text(printer.address),
                                        trailing: printer.isConnected
                                            ? const Icon(Icons.check_circle,
                                                color: Colors.green)
                                            : null,
                                        selected: _printerNameController.text ==
                                            printer.name,
                                        onTap: () {
                                          setState(() {
                                            _printerNameController.text =
                                                printer.name;
                                            _printerModelController.text =
                                                'Bluetooth Printer';
                                          });
                                        },
                                      ),
                                    )),
                                const SizedBox(height: 8),
                                Text(
                                  T('Tap on a printer to select it'),
                                  style: Theme.of(context).textTheme.bodySmall,
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _printerNameController,
                            decoration: InputDecoration(
                              labelText: T('Selected Printer'),
                              border: const OutlineInputBorder(),
                              enabled: false,
                            ),
                          ),
                        ],

                        const SizedBox(height: 24),

                        // Test Connection Button
                        ElevatedButton.icon(
                          onPressed: _isTestingConnection
                              ? null
                              : _testPrinterConnection,
                          icon: _isTestingConnection
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2))
                              : const Icon(Icons.wifi),
                          label: Text(T('Test Connection')),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action Buttons
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: _saveSettings,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: context.primaryColor,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: Text(
                                  T('Save Settings'),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: OutlinedButton(
                                onPressed: _clearSettings,
                                style: OutlinedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: Text(
                                  T('Clear Settings'),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Receipt Tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Paper Size Selection
                      Text(
                        T('Paper Size'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: [
                          ChoiceChip(
                            label: Text(T('58mm')),
                            selected: _selectedPaperSize == PaperSize.mm58,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedPaperSize = PaperSize.mm58;
                                });
                              }
                            },
                          ),
                          ChoiceChip(
                            label: Text(T('80mm')),
                            selected: _selectedPaperSize == PaperSize.mm80,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedPaperSize = PaperSize.mm80;
                                });
                              }
                            },
                          ),
                          ChoiceChip(
                            label: Text(T('A4')),
                            selected: _selectedPaperSize == PaperSize.a4,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedPaperSize = PaperSize.a4;
                                });
                              }
                            },
                          ),
                          ChoiceChip(
                            label: Text(T('Letter')),
                            selected: _selectedPaperSize == PaperSize.letter,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedPaperSize = PaperSize.letter;
                                });
                              }
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Receipt Header
                      Text(
                        T('Receipt Header'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _receiptHeaderController,
                        decoration: InputDecoration(
                          hintText: T(
                              'Enter header text to appear at the top of receipts'),
                          border: const OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),

                      const SizedBox(height: 16),

                      // Receipt Footer
                      Text(
                        T('Receipt Footer'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _receiptFooterController,
                        decoration: InputDecoration(
                          hintText: T(
                              'Enter footer text to appear at the bottom of receipts'),
                          border: const OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),

                      const SizedBox(height: 16),

                      // Logo Settings
                      Text(
                        T('Logo Settings'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: Text(T('Print Logo on Receipt')),
                        value: _printLogo,
                        onChanged: (value) {
                          setState(() {
                            _printLogo = value;
                          });
                        },
                      ),

                      if (_printLogo) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _logoPath != null
                                    ? _logoPath!.split('/').last
                                    : T('No logo selected'),
                              ),
                            ),
                            ElevatedButton.icon(
                              onPressed: _selectLogoFile,
                              icon: const Icon(Icons.image),
                              label: Text(T('Select Logo')),
                            ),
                          ],
                        ),
                      ],

                      const SizedBox(height: 16),

                      // Number of Copies
                      Text(
                        T('Number of Copies'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.remove),
                            onPressed: _copies > 1
                                ? () {
                                    setState(() {
                                      _copies--;
                                    });
                                  }
                                : null,
                          ),
                          Text(
                            _copies.toString(),
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          IconButton(
                            icon: const Icon(Icons.add),
                            onPressed: _copies < 5
                                ? () {
                                    setState(() {
                                      _copies++;
                                    });
                                  }
                                : null,
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Save Button
                      ElevatedButton(
                        onPressed: _saveSettings,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.primaryColor,
                          minimumSize: const Size.fromHeight(50),
                        ),
                        child: Text(
                          T('Save Receipt Settings'),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Test Tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                T('Test Printing'),
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                T('Print a test receipt to verify your printer settings.'),
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed:
                                    _isPrinting ? null : _printTestReceipt,
                                icon: _isPrinting
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      )
                                    : const Icon(Icons.print),
                                label: Text(T('Print Test Receipt')),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                  minimumSize: const Size.fromHeight(50),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                T('Current Settings'),
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 16),
                              ListTile(
                                title: Text(T('Printer Name')),
                                subtitle: Text(
                                    _printerNameController.text.isEmpty
                                        ? T('Not set')
                                        : _printerNameController.text),
                                leading: const Icon(Icons.print),
                              ),
                              ListTile(
                                title: Text(T('Printer Type')),
                                subtitle: Text(_selectedPrinterType == 'network'
                                    ? T('Network Printer')
                                    : T('USB Printer')),
                                leading: const Icon(Icons.device_hub),
                              ),
                              if (_selectedPrinterType == 'network') ...[
                                ListTile(
                                  title: Text(T('IP Address')),
                                  subtitle: Text(
                                      _printerIPController.text.isEmpty
                                          ? T('Not set')
                                          : _printerIPController.text),
                                  leading: const Icon(Icons.wifi),
                                ),
                                ListTile(
                                  title: Text(T('Port')),
                                  subtitle: Text(
                                      _printerPortController.text.isEmpty
                                          ? T('Not set')
                                          : _printerPortController.text),
                                  leading: const Icon(Icons.settings_ethernet),
                                ),
                              ],
                              ListTile(
                                title: Text(T('Paper Size')),
                                subtitle: Text(_selectedPaperSize
                                    .toString()
                                    .split('.')
                                    .last),
                                leading: const Icon(Icons.description),
                              ),
                              ListTile(
                                title: Text(T('Copies')),
                                subtitle: Text(_copies.toString()),
                                leading: const Icon(Icons.copy),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : const Center(
              child: CircularProgressIndicator(),
            ),
    );
  }
}
