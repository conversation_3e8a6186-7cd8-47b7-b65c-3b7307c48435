import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/reports/customer_sales_report_dto.dart';
import 'package:inventory_application/models/dto/reports/daily_sales_report_dto.dart';
import 'package:inventory_application/models/dto/reports/monthly_sales_report_dto.dart';
import 'package:inventory_application/models/dto/reports/product_sales_report_dto.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';

class ReportController with ChangeNotifier {
  // Reports data
  DailySalesReportDTO? _dailySalesReport;
  MonthlySalesReportDTO? _monthlySalesReport;
  ProductSalesReportDTO? _productSalesReport;
  CustomerSalesReportDTO? _customerSalesReport;

  ReportFilterDTO _filter = ReportFilterDTO(
    fromDate: DateTime.now(),
    toDate: DateTime.now(),
    reportType: ReportType.dailySales,
  );

  // Getters
  DailySalesReportDTO? get dailySalesReport => _dailySalesReport;
  MonthlySalesReportDTO? get monthlySalesReport => _monthlySalesReport;
  ProductSalesReportDTO? get productSalesReport => _productSalesReport;
  CustomerSalesReportDTO? get customerSalesReport => _customerSalesReport;

  ReportFilterDTO get filter => _filter;

  // Setters
  set filter(ReportFilterDTO value) {
    _filter = value;
    notifyListeners();
  }

  // Initialize the controller
  ReportController() {
    // Set default filter to today
    _filter = ReportFilterDTO(
      fromDate: DateTime.now(),
      toDate: DateTime.now(),
      reportType: ReportType.dailySales,
    );
  }

  // Generate daily sales report
  Future<DailySalesReportDTO> generateDailySalesReport({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // Update filter dates if provided
      if (fromDate != null) {
        _filter.fromDate = fromDate;
      }
      if (toDate != null) {
        _filter.toDate = toDate;
      }

      // Format dates for comparison (remove time component)
      final fromDateFormatted = DateTime(
        _filter.fromDate!.year,
        _filter.fromDate!.month,
        _filter.fromDate!.day,
      );
      final toDateFormatted = DateTime(
        _filter.toDate!.year,
        _filter.toDate!.month,
        _filter.toDate!.day,
        23, 59, 59, // End of day
      );

      // Get all invoices from local storage
      final db = await DatabaseHelper().database;
      final List<Map<String, dynamic>> result = await db.query('Invoice');
      final List<SqlLiteInvoiceModel> allInvoices =
          result.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();

      // Filter invoices by date and type (sales invoices only)
      final List<InvoiceModel> salesInvoices = [];
      double totalSales = 0;
      double totalDiscount = 0;
      int invoiceCount = 0;

      for (var invoice in allInvoices) {
        if (invoice.data != null &&
            invoice.type == getInvoiceTypeName(SalesType.Invoice)) {
          final InvoiceModel invoiceModel = InvoiceModel.fromJson(
            json.decode(invoice.data!),
          );

          // Check if invoice date is within the filter range
          if (invoiceModel.entryDate != null) {
            final invoiceDate = DateTime(
              invoiceModel.entryDate!.year,
              invoiceModel.entryDate!.month,
              invoiceModel.entryDate!.day,
            );

            if ((invoiceDate.isAtSameMomentAs(fromDateFormatted) ||
                    invoiceDate.isAfter(fromDateFormatted)) &&
                (invoiceDate.isAtSameMomentAs(toDateFormatted) ||
                    invoiceDate.isBefore(toDateFormatted))) {
              salesInvoices.add(invoiceModel);
              totalSales += invoiceModel.total ?? 0;
              totalDiscount += invoiceModel.discountValue ?? 0;
              invoiceCount++;
            }
          }
        }
      }

      // Create report data
      _dailySalesReport = DailySalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: totalSales,
        totalDiscount: totalDiscount,
        netSales: totalSales - totalDiscount,
        invoiceCount: invoiceCount,
        invoices: salesInvoices,
      );

      // Notify listeners of data change
      notifyListeners();

      return _dailySalesReport!;
    } catch (e) {
      debugPrint('Error generating daily sales report: $e');
      // Return empty report on error
      return DailySalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: 0,
        totalDiscount: 0,
        netSales: 0,
        invoiceCount: 0,
        invoices: [],
      );
    }
  }

  // Helper method to get filtered invoices
  Future<List<InvoiceModel>> _getFilteredInvoices(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // Get all invoices from local storage
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> result = await db.query('Invoice');
    final List<SqlLiteInvoiceModel> allInvoices =
        result.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();

    // Filter invoices by date and type (sales invoices only)
    final List<InvoiceModel> salesInvoices = [];

    for (var invoice in allInvoices) {
      if (invoice.data != null &&
          invoice.type == getInvoiceTypeName(SalesType.Invoice)) {
        final InvoiceModel invoiceModel = InvoiceModel.fromJson(
          json.decode(invoice.data!),
        );

        // Check if invoice date is within the filter range
        if (invoiceModel.entryDate != null) {
          final invoiceDate = DateTime(
            invoiceModel.entryDate!.year,
            invoiceModel.entryDate!.month,
            invoiceModel.entryDate!.day,
          );

          if ((invoiceDate.isAtSameMomentAs(fromDate) ||
                  invoiceDate.isAfter(fromDate)) &&
              (invoiceDate.isAtSameMomentAs(toDate) ||
                  invoiceDate.isBefore(toDate))) {
            salesInvoices.add(invoiceModel);
          }
        }
      }
    }

    return salesInvoices;
  }

  // Generate monthly sales report
  Future<MonthlySalesReportDTO> generateMonthlySalesReport({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // Update filter dates if provided
      if (fromDate != null) {
        _filter.fromDate = fromDate;
      }
      if (toDate != null) {
        _filter.toDate = toDate;
      }

      // Format dates for comparison (remove time component)
      final fromDateFormatted = DateTime(
        _filter.fromDate!.year,
        _filter.fromDate!.month,
        _filter.fromDate!.day,
      );
      final toDateFormatted = DateTime(
        _filter.toDate!.year,
        _filter.toDate!.month,
        _filter.toDate!.day,
        23, 59, 59, // End of day
      );

      // Get filtered invoices
      final salesInvoices = await _getFilteredInvoices(
        fromDateFormatted,
        toDateFormatted,
      );

      // Calculate totals
      double totalSales = 0;
      double totalDiscount = 0;
      int invoiceCount = salesInvoices.length;

      for (var invoice in salesInvoices) {
        totalSales += invoice.total ?? 0;
        totalDiscount += invoice.discountValue ?? 0;
      }

      // Create report data
      _monthlySalesReport = MonthlySalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: totalSales,
        totalDiscount: totalDiscount,
        netSales: totalSales - totalDiscount,
        invoiceCount: invoiceCount,
        invoices: salesInvoices,
      );

      // Notify listeners of data change
      notifyListeners();

      return _monthlySalesReport!;
    } catch (e) {
      debugPrint('Error generating monthly sales report: $e');
      // Return empty report on error
      return MonthlySalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: 0,
        totalDiscount: 0,
        netSales: 0,
        invoiceCount: 0,
        invoices: [],
      );
    }
  }

  // Generate product sales report
  Future<ProductSalesReportDTO> generateProductSalesReport({
    DateTime? fromDate,
    DateTime? toDate,
    int? productId,
  }) async {
    try {
      // Update filter dates if provided
      if (fromDate != null) {
        _filter.fromDate = fromDate;
      }
      if (toDate != null) {
        _filter.toDate = toDate;
      }
      if (productId != null) {
        _filter.productId = productId;
      }

      // Format dates for comparison (remove time component)
      final fromDateFormatted = DateTime(
        _filter.fromDate!.year,
        _filter.fromDate!.month,
        _filter.fromDate!.day,
      );
      final toDateFormatted = DateTime(
        _filter.toDate!.year,
        _filter.toDate!.month,
        _filter.toDate!.day,
        23, 59, 59, // End of day
      );

      // Get filtered invoices
      final salesInvoices = await _getFilteredInvoices(
        fromDateFormatted,
        toDateFormatted,
      );

      // Filter by product if specified
      List<InvoiceModel> filteredInvoices = salesInvoices;
      if (_filter.productId != null) {
        filteredInvoices = salesInvoices.where((invoice) {
          if (invoice.salesItems == null) return false;
          return invoice.salesItems!
              .any((item) => item.itemID == _filter.productId);
        }).toList();
      }

      // Create product summaries
      final productSummaries =
          ProductSalesSummary.createFromInvoices(filteredInvoices);

      // Calculate totals
      double totalSales = 0;
      int totalQuantity = 0;

      for (var product in productSummaries) {
        totalSales += product.totalSales;
        totalQuantity += product.quantity;
      }

      // Create report data
      _productSalesReport = ProductSalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: totalSales,
        totalQuantity: totalQuantity,
        invoices: filteredInvoices,
        productSummaries: productSummaries,
      );

      // Notify listeners of data change
      notifyListeners();

      return _productSalesReport!;
    } catch (e) {
      debugPrint('Error generating product sales report: $e');
      // Return empty report on error
      return ProductSalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: 0,
        totalQuantity: 0,
        invoices: [],
        productSummaries: [],
      );
    }
  }

  // Generate customer sales report
  Future<CustomerSalesReportDTO> generateCustomerSalesReport({
    DateTime? fromDate,
    DateTime? toDate,
    int? customerId,
  }) async {
    try {
      // Update filter dates if provided
      if (fromDate != null) {
        _filter.fromDate = fromDate;
      }
      if (toDate != null) {
        _filter.toDate = toDate;
      }
      if (customerId != null) {
        _filter.customerId = customerId;
      }

      // Format dates for comparison (remove time component)
      final fromDateFormatted = DateTime(
        _filter.fromDate!.year,
        _filter.fromDate!.month,
        _filter.fromDate!.day,
      );
      final toDateFormatted = DateTime(
        _filter.toDate!.year,
        _filter.toDate!.month,
        _filter.toDate!.day,
        23, 59, 59, // End of day
      );

      // Get filtered invoices
      final salesInvoices = await _getFilteredInvoices(
        fromDateFormatted,
        toDateFormatted,
      );

      // Filter by customer if specified
      List<InvoiceModel> filteredInvoices = salesInvoices;
      if (_filter.customerId != null) {
        filteredInvoices = salesInvoices.where((invoice) {
          return invoice.customerID == _filter.customerId;
        }).toList();
      }

      // Create customer summaries
      final customerSummaries =
          CustomerSalesSummary.createFromInvoices(filteredInvoices);

      // Calculate totals
      double totalSales = 0;

      for (var customer in customerSummaries) {
        totalSales += customer.totalSales;
      }

      // Create report data
      _customerSalesReport = CustomerSalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: totalSales,
        invoiceCount: filteredInvoices.length,
        invoices: filteredInvoices,
        customerSummaries: customerSummaries,
      );

      // Notify listeners of data change
      notifyListeners();

      return _customerSalesReport!;
    } catch (e) {
      debugPrint('Error generating customer sales report: $e');
      // Return empty report on error
      return CustomerSalesReportDTO(
        fromDate: _filter.fromDate!,
        toDate: _filter.toDate!,
        totalSales: 0,
        invoiceCount: 0,
        invoices: [],
        customerSummaries: [],
      );
    }
  }

  // Helper method to format date for display
  String formatReportDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }
}
