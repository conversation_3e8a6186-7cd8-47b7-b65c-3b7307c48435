import 'package:flutter/material.dart';
import 'package:inventory_application/screens/setting/comming_soon_screen.dart';
import 'package:inventory_application/screens/home/<USER>';

class ExpensesRecordingScreen extends StatefulWidget {
  const ExpensesRecordingScreen({super.key});

  @override
  State<ExpensesRecordingScreen> createState() =>
      _ExpensesRecordingScreenState();
}

class _ExpensesRecordingScreenState extends State<ExpensesRecordingScreen> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
        child: Container(color: Colors.white, child: CommingSoonScreen()));
  }
}
