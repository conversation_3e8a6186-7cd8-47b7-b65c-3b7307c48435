class ComparisonItemDto {
  final int itemId;
  final String itemName;
  final String itemCode;
  final double stocktakingQuantity;
  final double serverQuantity;
  final double difference;

  ComparisonItemDto({
    required this.itemId,
    required this.itemName,
    required this.itemCode,
    required this.stocktakingQuantity,
    required this.serverQuantity,
    required this.difference,
  });
}
