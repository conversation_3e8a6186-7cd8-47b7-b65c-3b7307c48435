class ComparisonItemDto {
  final int itemId;
  final String itemName;
  final String? barcode;
  final String itemCode;
  final double stocktakingQuantity;
  final double serverQuantity;
  final double difference;
  final double itemPrice;
  bool? hasSelectedAttributes;
  List<int>? selectedOptionIds;
  ComparisonItemDto({
    required this.itemId,
    required this.itemName,
    required this.itemCode,
    required this.stocktakingQuantity,
    required this.serverQuantity,
    required this.difference,
    required this.itemPrice,
    this.hasSelectedAttributes,
    this.selectedOptionIds,
    this.barcode,
  });
}
