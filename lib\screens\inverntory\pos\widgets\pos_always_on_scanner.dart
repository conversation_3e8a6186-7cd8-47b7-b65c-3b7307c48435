import 'package:flutter/material.dart';
import 'dart:io' show Platform;
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/barcode_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_virtual_keyboard_widget.dart';
import 'package:inventory_application/screens/components/continuous_barcode_scanner_dialog.dart';
import 'package:provider/provider.dart';
import 'package:virtual_keyboard_multi_language/virtual_keyboard_multi_language.dart';

class POSAlwaysOnScanner extends StatefulWidget {
  final Function(String) onBarcodeDetected;
  final FocusNode barcodeFocusNode;
  final TextEditingController barcodeController;

  const POSAlwaysOnScanner({
    super.key,
    required this.onBarcodeDetected,
    required this.barcodeFocusNode,
    required this.barcodeController,
  });

  @override
  State<POSAlwaysOnScanner> createState() => _POSAlwaysOnScannerState();
}

class _POSAlwaysOnScannerState extends State<POSAlwaysOnScanner> {
  bool _isActive = true;
  String? _lastDetectedBarcode;
  bool _isProcessing = false;
  bool _showKeyboard = false;

  @override
  void initState() {
    super.initState();
    // Request focus for the barcode input field on startup
  }

  void _processBarcode(String barcode) async {
    if (barcode.isEmpty || !_isActive || _isProcessing) return;

    // Prevent duplicate scans
    if (_lastDetectedBarcode == barcode) {
      widget.barcodeController.clear();
      return;
    }

    setState(() {
      _isProcessing = true;
      _lastDetectedBarcode = barcode;
    });

    try {
      // Call the callback and handle result
      await widget.onBarcodeDetected(barcode);
    } finally {
      setState(() {
        _isProcessing = false;
      });

      // Clear the field and refocus
      widget.barcodeController.clear();
      widget.barcodeFocusNode.requestFocus();
    }
  }

  void _toggleActive() {
    setState(() {
      _isActive = !_isActive;

      if (_isActive) {
        widget.barcodeFocusNode.requestFocus();
      }
    });
  }

  void _showCameraScanner() {
    showDialog(
      context: context,
      builder: (context) => ContinuousBarcodeScannerDialog(
        onBarcodeDetected: widget.onBarcodeDetected,
      ),
    );
  }

  void _toggleKeyboard() {
    setState(() {
      _showKeyboard = !_showKeyboard;
    });
  }

  @override
  Widget build(BuildContext context) {
    final barcodeController = Provider.of<BarcodeController>(context);
    final isExternalReaderEnabled = barcodeController.shouldUseExternalReader();
    final deviceName = barcodeController.selectedDevice ?? T('Unknown device');

    // Active scanner with visible input field
    if (_isActive) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            margin: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                // Scanner status indicator with animation
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isExternalReaderEnabled
                            ? Icons.usb
                            : Icons.qr_code_scanner,
                        size: 16,
                        color: context.newPrimaryColor,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        isExternalReaderEnabled
                            ? T('Scanner: $deviceName')
                            : T('Camera Scanner'),
                        style: TextStyle(
                          fontSize: 12,
                          color: context.newPrimaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 6),
                      _buildPulsingDot(),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // Camera scanner button
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _showCameraScanner,
                    borderRadius: BorderRadius.circular(6),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: context.newPrimaryColor.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.camera_alt,
                            size: 14,
                            color: context.newPrimaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            T('Scan'),
                            style: TextStyle(
                              fontSize: 12,
                              color: context.newPrimaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Keyboard button - show only on Windows
                if (Platform.isWindows)
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: _toggleKeyboard,
                      borderRadius: BorderRadius.circular(6),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: _showKeyboard
                              ? context.newPrimaryColor.withOpacity(0.1)
                              : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: _showKeyboard
                                ? context.newPrimaryColor.withOpacity(0.3)
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.keyboard,
                              size: 14,
                              color: _showKeyboard
                                  ? context.newPrimaryColor
                                  : Colors.grey.shade700,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              T('Keyboard'),
                              style: TextStyle(
                                fontSize: 12,
                                color: _showKeyboard
                                    ? context.newPrimaryColor
                                    : Colors.grey.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                // Toggle button with better styling
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _toggleActive,
                    borderRadius: BorderRadius.circular(6),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.power_settings_new,
                            size: 14,
                            color: Colors.grey.shade700,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            T('Disable'),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                // Visible input field for manual entry with better styling
                Expanded(
                  child: Container(
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: _isProcessing
                            ? context.newPrimaryColor.withOpacity(0.5)
                            : Colors.grey.shade300,
                        width: _isProcessing ? 1.5 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: Icon(
                            Icons.search,
                            size: 16,
                            color: Colors.grey.shade400,
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: widget.barcodeController,
                            focusNode: widget.barcodeFocusNode,
                            onSubmitted: _processBarcode,
                            textInputAction: TextInputAction.done,
                            // Allow system keyboard on mobile
                            onTap: () {
                              // Optional: On Windows, show custom keyboard
                              if (Platform.isWindows) {
                                // setState(() {
                                //   _showKeyboard = true;
                                // });
                              }
                            },
                            onEditingComplete: () {
                              if (widget.barcodeController.text.isNotEmpty) {
                                _processBarcode(widget.barcodeController.text);
                              }
                              FocusScope.of(context).unfocus();
                            },
                            decoration: InputDecoration(
                              isDense: true,
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                              border: InputBorder.none,
                              hintText: Platform.isWindows
                                  ? T('Enter barcode or scan item... (F4)')
                                  : T('Enter barcode or scan item...'),
                              hintStyle: TextStyle(
                                  fontSize: 12, color: Colors.grey.shade400),
                            ),
                            style: TextStyle(
                                fontSize: 13, color: context.newTextColor),
                          ),
                        ),
                        if (_isProcessing)
                          Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: context.newPrimaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Virtual keyboard - only show on Windows
          if (_showKeyboard && Platform.isWindows)
            CommonVirtualKeyboardWidget(
              textEditingController: widget.barcodeController,
              type: VirtualKeyboardType.Alphanumeric,
              height: 300,
              onKeyPress: (key) {
                // If the user presses Enter or Done on the keyboard, process the barcode
                if (key.keyType == VirtualKeyboardKeyType.Action &&
                    key.action == VirtualKeyboardKeyAction.Return &&
                    widget.barcodeController.text.isNotEmpty) {
                  _processBarcode(widget.barcodeController.text);
                }
              },
            ),
        ],
      );
    } else {
      // Inactive scanner indicator with better styling
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        margin: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.qr_code_scanner,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    T('Scanner Disabled'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.red.shade400,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            // Enable button with better styling
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _toggleActive,
                borderRadius: BorderRadius.circular(6),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                        color: context.newPrimaryColor.withOpacity(0.2)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.power_settings_new,
                        size: 14,
                        color: context.newPrimaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        T('Enable'),
                        style: TextStyle(
                          fontSize: 12,
                          color: context.newPrimaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const Spacer(),
          ],
        ),
      );
    }
  }

  // Animated pulsing dot for active scanner
  Widget _buildPulsingDot() {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.5, end: 1.0),
      duration: const Duration(seconds: 1),
      builder: (context, value, child) {
        return Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.green.withOpacity(value),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3 * value),
                blurRadius: 4 * value,
                spreadRadius: 1 * value,
              ),
            ],
          ),
        );
      },
      onEnd: () {
        setState(() {}); // Trigger rebuild to restart animation
      },
    );
  }
}
